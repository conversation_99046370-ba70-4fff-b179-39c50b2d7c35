/* =====================================================
   电商管理系统 - 暗黑主题样式表
   ===================================================== */

/* 全局样式 */
* {
    color: #ffffff;
    background-color: #2b2b2b;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* 主窗口 */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 菜单栏 */
QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-bottom: 1px solid #555555;
    padding: 2px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 3px;
}

QMenuBar::item:selected {
    background-color: #4a90e2;
}

QMenuBar::item:pressed {
    background-color: #357abd;
}

/* 菜单 */
QMenu {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 2px;
}

QMenu::item {
    background-color: transparent;
    padding: 6px 20px;
    margin: 1px;
    border-radius: 3px;
}

QMenu::item:selected {
    background-color: #4a90e2;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 2px 0px;
}

/* 工具栏 */
QToolBar {
    background-color: #3c3c3c;
    border: none;
    padding: 2px;
    spacing: 2px;
}

QToolBar::handle {
    background-color: #555555;
    width: 8px;
    margin: 4px 2px;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px;
    margin: 1px;
}

QToolButton:hover {
    background-color: #4a90e2;
    border-color: #357abd;
}

QToolButton:pressed {
    background-color: #357abd;
}

/* 状态栏 */
QStatusBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-top: 1px solid #555555;
    max-height: 25px;
    min-height: 20px;
    padding: 2px 5px;
}

QStatusBar::item {
    border: none;
    padding: 0px 2px;
}

QStatusBar QLabel {
    background-color: transparent;
    color: #ffffff;
    padding: 2px 5px;
    font-size: 9pt;
    max-height: 20px;
}

QStatusBar QProgressBar {
    max-height: 16px;
    min-height: 12px;
}

/* 标签页控件 */
QTabWidget::pane {
    border: 1px solid #555555;
    background-color: #2b2b2b;
}

QTabWidget::tab-bar {
    left: 5px;
}

QTabBar::tab {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-bottom: none;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #4a90e2;
    border-color: #357abd;
}

QTabBar::tab:hover:!selected {
    background-color: #4c4c4c;
}

/* 按钮 */
QPushButton {
    background-color: #4a90e2;
    color: #ffffff;
    border: 1px solid #357abd;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #5ba0f2;
    border-color: #4a90e2;
}

QPushButton:pressed {
    background-color: #357abd;
    border-color: #2968a3;
}

QPushButton:disabled {
    background-color: #555555;
    color: #888888;
    border-color: #444444;
}

/* 输入框 */
QLineEdit {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px 8px;
}

QLineEdit:focus {
    border-color: #4a90e2;
    background-color: #404040;
}

QLineEdit:disabled {
    background-color: #2a2a2a;
    color: #666666;
}

/* 文本框 */
QTextEdit, QPlainTextEdit {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #4a90e2;
}

/* 组合框 */
QComboBox {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 4px 8px;
    min-width: 60px;
}

QComboBox:hover {
    border-color: #4a90e2;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #555555;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    selection-background-color: #4a90e2;
}

/* 列表控件 */
QListWidget {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    alternate-background-color: #404040;
}

QListWidget::item {
    padding: 4px;
    border-bottom: 1px solid #555555;
}

QListWidget::item:selected {
    background-color: #4a90e2;
}

QListWidget::item:hover {
    background-color: #4c4c4c;
}

/* 表格控件 */
QTableWidget {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    gridline-color: #555555;
    alternate-background-color: #404040;
}

QTableWidget::item {
    padding: 4px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #4a90e2;
}

QTableWidget::item:hover {
    background-color: #4c4c4c;
}

QHeaderView::section {
    background-color: #4c4c4c;
    color: #ffffff;
    border: 1px solid #555555;
    padding: 4px 8px;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #5c5c5c;
}

/* 树形控件 */
QTreeWidget {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    alternate-background-color: #404040;
}

QTreeWidget::item {
    padding: 2px;
    border: none;
}

QTreeWidget::item:selected {
    background-color: #4a90e2;
}

QTreeWidget::item:hover {
    background-color: #4c4c4c;
}

QTreeWidget::branch:has-children:!has-siblings:closed,
QTreeWidget::branch:closed:has-children:has-siblings {
    border-image: none;
    image: url(branch_closed.png);
}

QTreeWidget::branch:open:has-children:!has-siblings,
QTreeWidget::branch:open:has-children:has-siblings {
    border-image: none;
    image: url(branch_open.png);
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #2b2b2b;
    width: 12px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #2b2b2b;
    height: 12px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 进度条 */
QProgressBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #4a90e2;
    border-radius: 2px;
}

/* 分组框 */
QGroupBox {
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 3px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #4a90e2;
    font-weight: bold;
}

/* 复选框 */
QCheckBox {
    color: #ffffff;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}

QCheckBox::indicator:unchecked {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    background-color: #4a90e2;
    border: 1px solid #357abd;
    border-radius: 2px;
}

/* 单选按钮 */
QRadioButton {
    color: #ffffff;
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border-radius: 8px;
}

QRadioButton::indicator:unchecked {
    background-color: #3c3c3c;
    border: 1px solid #555555;
}

QRadioButton::indicator:checked {
    background-color: #4a90e2;
    border: 1px solid #357abd;
}

/* 标签 */
QLabel {
    color: #ffffff;
    background-color: transparent;
}

/* 分割器 */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 对话框 */
QDialog {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 消息框 */
QMessageBox {
    background-color: #2b2b2b;
    color: #ffffff;
}

QMessageBox QPushButton {
    min-width: 80px;
}
