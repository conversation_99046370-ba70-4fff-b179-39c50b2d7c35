#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理模块

提供全局异常处理和错误报告功能。
"""

import sys
import traceback
import logging
from typing import Optional, Callable, Any
from datetime import datetime
from pathlib import Path


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化异常处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.error_callback: Optional[Callable] = None
        
    def set_error_callback(self, callback: Callable[[Exception, str], None]):
        """
        设置错误回调函数
        
        Args:
            callback: 错误回调函数，接收异常对象和错误信息
        """
        self.error_callback = callback
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """
        处理未捕获的异常
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪信息
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许用户中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 格式化错误信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 记录错误日志
        self.logger.critical(f"未捕获的异常: {error_msg}")
        
        # 保存错误报告
        self.save_error_report(exc_type, exc_value, exc_traceback, error_msg)
        
        # 调用错误回调
        if self.error_callback:
            try:
                self.error_callback(exc_value, error_msg)
            except Exception as e:
                self.logger.error(f"错误回调函数执行失败: {e}")
    
    def save_error_report(self, exc_type, exc_value, exc_traceback, error_msg: str):
        """
        保存错误报告到文件
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪信息
            error_msg: 格式化的错误信息
        """
        try:
            # 创建错误报告目录
            error_dir = Path("logs/errors")
            error_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成错误报告文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_file = error_dir / f"error_report_{timestamp}.txt"
            
            # 收集系统信息
            import platform
            import sys
            
            system_info = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Python版本": sys.version,
                "平台": platform.platform(),
                "架构": platform.architecture()[0],
                "处理器": platform.processor(),
                "异常类型": str(exc_type.__name__),
                "异常信息": str(exc_value)
            }
            
            # 写入错误报告
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("电商管理系统 - 错误报告\n")
                f.write("=" * 80 + "\n\n")
                
                # 系统信息
                f.write("系统信息:\n")
                f.write("-" * 40 + "\n")
                for key, value in system_info.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 异常详情
                f.write("异常详情:\n")
                f.write("-" * 40 + "\n")
                f.write(error_msg)
                f.write("\n")
                
                # 局部变量
                if exc_traceback:
                    f.write("局部变量:\n")
                    f.write("-" * 40 + "\n")
                    frame = exc_traceback.tb_frame
                    while frame:
                        f.write(f"文件: {frame.f_code.co_filename}\n")
                        f.write(f"函数: {frame.f_code.co_name}\n")
                        f.write(f"行号: {frame.f_lineno}\n")
                        f.write("局部变量:\n")
                        for var_name, var_value in frame.f_locals.items():
                            try:
                                f.write(f"  {var_name} = {repr(var_value)}\n")
                            except:
                                f.write(f"  {var_name} = <无法显示>\n")
                        f.write("\n")
                        frame = frame.f_back
            
            self.logger.info(f"错误报告已保存: {error_file}")
            
        except Exception as e:
            self.logger.error(f"保存错误报告失败: {e}")


def safe_execute(func: Callable, *args, default_return: Any = None, 
                logger: Optional[logging.Logger] = None, **kwargs) -> Any:
    """
    安全执行函数，捕获异常并记录日志
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        logger: 日志记录器
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或默认返回值
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"执行函数 {func.__name__} 时出错: {e}")
        logger.debug(f"函数参数: args={args}, kwargs={kwargs}")
        return default_return


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None,
                 reraise: bool = False, default_return: Any = None):
        """
        初始化错误上下文
        
        Args:
            operation_name: 操作名称
            logger: 日志记录器
            reraise: 是否重新抛出异常
            default_return: 异常时的默认返回值
        """
        self.operation_name = operation_name
        self.logger = logger or logging.getLogger(__name__)
        self.reraise = reraise
        self.default_return = default_return
        self.exception_occurred = False
        self.exception = None
    
    def __enter__(self):
        self.logger.debug(f"开始执行操作: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_value, traceback):
        if exc_type is not None:
            self.exception_occurred = True
            self.exception = exc_value
            
            error_msg = f"操作 '{self.operation_name}' 执行失败: {exc_value}"
            self.logger.error(error_msg)
            
            if self.reraise:
                return False  # 重新抛出异常
            else:
                return True  # 抑制异常
        else:
            self.logger.debug(f"操作 '{self.operation_name}' 执行成功")
            return False
    
    def get_result(self):
        """获取操作结果"""
        if self.exception_occurred:
            return self.default_return
        return None


# 装饰器：自动错误处理
def handle_errors(default_return: Any = None, logger: Optional[logging.Logger] = None,
                 reraise: bool = False):
    """
    错误处理装饰器
    
    Args:
        default_return: 异常时的默认返回值
        logger: 日志记录器
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            if logger is None:
                func_logger = logging.getLogger(func.__module__)
            else:
                func_logger = logger
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_logger.error(f"函数 {func.__name__} 执行失败: {e}")
                
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator
