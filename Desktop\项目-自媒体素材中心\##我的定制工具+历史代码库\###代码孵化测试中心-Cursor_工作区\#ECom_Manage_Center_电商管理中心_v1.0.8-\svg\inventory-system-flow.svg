<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#333">多平台库存管理系统架构流程图</text>
  
  <!-- 数据库层 -->
  <g id="database-layer">
    <rect x="50" y="80" width="1300" height="200" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
    <text x="700" y="110" text-anchor="middle" font-size="18" font-weight="bold" fill="#1976d2">本地数据库层</text>
    
    <!-- 产品主表 -->
    <rect x="100" y="130" width="250" height="120" fill="#fff" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="225" y="155" text-anchor="middle" font-size="14" font-weight="bold">产品主表</text>
    <text x="110" y="175" font-size="12">• 产品ID (唯一)</text>
    <text x="110" y="195" font-size="12">• 产品名称</text>
    <text x="110" y="215" font-size="12">• 基础信息</text>
    <text x="110" y="235" font-size="12">• SKU管理</text>
    
    <!-- SKU变体表 -->
    <rect x="380" y="130" width="250" height="120" fill="#fff" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="505" y="155" text-anchor="middle" font-size="14" font-weight="bold">SKU变体表</text>
    <text x="390" y="175" font-size="12">• SKU_ID</text>
    <text x="390" y="195" font-size="12">• 产品ID (外键)</text>
    <text x="390" y="215" font-size="12">• 颜色/规格</text>
    <text x="390" y="235" font-size="12">• 库存数量</text>
    
    <!-- 平台ID映射表 -->
    <rect x="660" y="130" width="250" height="120" fill="#fff" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="785" y="155" text-anchor="middle" font-size="14" font-weight="bold">平台ID映射表</text>
    <text x="670" y="175" font-size="12">• 映射ID</text>
    <text x="670" y="195" font-size="12">• SKU_ID</text>
    <text x="670" y="215" font-size="12">• 平台类型</text>
    <text x="670" y="235" font-size="12">• 平台商品ID</text>
    
    <!-- 订单表 -->
    <rect x="940" y="130" width="250" height="120" fill="#fff" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="1065" y="155" text-anchor="middle" font-size="14" font-weight="bold">订单表</text>
    <text x="950" y="175" font-size="12">• 订单ID</text>
    <text x="950" y="195" font-size="12">• 平台订单号</text>
    <text x="950" y="215" font-size="12">• SKU_ID</text>
    <text x="950" y="235" font-size="12">• 状态/物流</text>
  </g>
  
  <!-- 平台层 -->
  <g id="platform-layer">
    <rect x="50" y="320" width="1300" height="150" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
    <text x="700" y="350" text-anchor="middle" font-size="18" font-weight="bold" fill="#f57c00">电商平台层</text>
    
    <!-- 淘宝 -->
    <rect x="200" y="380" width="200" height="70" fill="#ff6f00" stroke="#e65100" stroke-width="2" rx="5"/>
    <text x="300" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="white">淘宝</text>
    <text x="300" y="430" text-anchor="middle" font-size="12" fill="white">商品ID: TB_xxxxx</text>
    
    <!-- 小红书 -->
    <rect x="500" y="380" width="200" height="70" fill="#ff1744" stroke="#c51162" stroke-width="2" rx="5"/>
    <text x="600" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="white">小红书</text>
    <text x="600" y="430" text-anchor="middle" font-size="12" fill="white">商品ID: XHS_xxxxx</text>
    
    <!-- 抖音 -->
    <rect x="800" y="380" width="200" height="70" fill="#00e676" stroke="#00c853" stroke-width="2" rx="5"/>
    <text x="900" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="white">抖音</text>
    <text x="900" y="430" text-anchor="middle" font-size="12" fill="white">商品ID: DY_xxxxx</text>
    
    <!-- 1688 -->
    <rect x="1050" y="380" width="200" height="70" fill="#2979ff" stroke="#1565c0" stroke-width="2" rx="5"/>
    <text x="1150" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="white">1688代发</text>
    <text x="1150" y="430" text-anchor="middle" font-size="12" fill="white">供应商ID</text>
  </g>
  
  <!-- API处理层 -->
  <g id="api-layer">
    <rect x="50" y="510" width="1300" height="200" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="10"/>
    <text x="700" y="540" text-anchor="middle" font-size="18" font-weight="bold" fill="#7b1fa2">API处理与业务逻辑层</text>
    
    <!-- 订单获取流程 -->
    <rect x="100" y="560" width="300" height="120" fill="#fff" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="250" y="585" text-anchor="middle" font-size="14" font-weight="bold">订单获取流程</text>
    <text x="110" y="605" font-size="12">1. API定时拉取订单</text>
    <text x="110" y="625" font-size="12">2. 解析平台商品ID</text>
    <text x="110" y="645" font-size="12">3. 查找SKU映射</text>
    <text x="110" y="665" font-size="12">4. 创建本地订单</text>
    
    <!-- ID匹配逻辑 -->
    <rect x="450" y="560" width="300" height="120" fill="#fff" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="600" y="585" text-anchor="middle" font-size="14" font-weight="bold">ID匹配逻辑</text>
    <text x="460" y="605" font-size="12">• 自动匹配：通过映射表</text>
    <text x="460" y="625" font-size="12">• 手动匹配：未找到时</text>
    <text x="460" y="645" font-size="12">• 智能推荐：相似度算法</text>
    <text x="460" y="665" font-size="12">• 记录映射：保存关系</text>
    
    <!-- 库存同步 -->
    <rect x="800" y="560" width="300" height="120" fill="#fff" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="950" y="585" text-anchor="middle" font-size="14" font-weight="bold">库存同步逻辑</text>
    <text x="810" y="605" font-size="12">• 订单扣减库存</text>
    <text x="810" y="625" font-size="12">• 进货增加库存</text>
    <text x="810" y="645" font-size="12">• 多平台库存同步</text>
    <text x="810" y="665" font-size="12">• 代发库存特殊处理</text>
  </g>
  
  <!-- 异常处理层 -->
  <g id="exception-layer">
    <rect x="50" y="750" width="1300" height="150" fill="#ffebee" stroke="#d32f2f" stroke-width="2" rx="10"/>
    <text x="700" y="780" text-anchor="middle" font-size="18" font-weight="bold" fill="#d32f2f">异常处理与人工干预</text>
    
    <!-- 异常场景 -->
    <rect x="200" y="810" width="400" height="70" fill="#fff" stroke="#f44336" stroke-width="2" rx="5"/>
    <text x="400" y="835" text-anchor="middle" font-size="14" font-weight="bold">异常场景</text>
    <text x="210" y="855" font-size="12">• 商品ID未匹配 → 手动绑定</text>
    <text x="210" y="870" font-size="12">• 库存不足 → 代发处理/预警</text>
    
    <!-- 手动操作 -->
    <rect x="700" y="810" width="400" height="70" fill="#fff" stroke="#f44336" stroke-width="2" rx="5"/>
    <text x="900" y="835" text-anchor="middle" font-size="14" font-weight="bold">手动操作界面</text>
    <text x="710" y="855" font-size="12">• 商品映射管理</text>
    <text x="710" y="870" font-size="12">• 订单状态调整</text>
  </g>
  
  <!-- 连接线和流程箭头 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- 数据流向箭头 -->
  <line x1="300" y1="450" x2="300" y2="380" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="450" x2="600" y2="380" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="900" y1="450" x2="900" y2="380" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="300" y1="280" x2="300" y2="320" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="280" x2="600" y2="320" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="900" y1="280" x2="900" y2="320" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <line x1="250" y1="680" x2="250" y2="750" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="600" y1="680" x2="600" y2="710" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="950" y1="680" x2="950" y2="710" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 图例 -->
  <g id="legend" transform="translate(1150, 50)">
    <text x="0" y="0" font-size="14" font-weight="bold">图例说明</text>
    <rect x="0" y="10" width="20" height="10" fill="#e3f2fd"/>
    <text x="25" y="19" font-size="12">数据库层</text>
    <rect x="0" y="30" width="20" height="10" fill="#fff3e0"/>
    <text x="25" y="39" font-size="12">平台层</text>
    <rect x="0" y="50" width="20" height="10" fill="#f3e5f5"/>
    <text x="25" y="59" font-size="12">业务逻辑</text>
    <rect x="0" y="70" width="20" height="10" fill="#ffebee"/>
    <text x="25" y="79" font-size="12">异常处理</text>
  </g>
</svg>