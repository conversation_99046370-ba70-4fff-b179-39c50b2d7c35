#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发中模块通用界面

为正在开发中的模块提供统一的展示界面。
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QGroupBox, QTextEdit, QProgressBar, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class DevelopmentWidget(QWidget):
    """开发中模块通用界面"""
    
    def __init__(self, module_info, parent=None):
        """
        初始化开发中界面
        
        Args:
            module_info: 模块信息字典，包含：
                - name: 模块名称
                - icon: 模块图标
                - description: 模块描述
                - progress: 开发进度 (0-100)
                - features: 功能列表
                - phases: 开发阶段列表
        """
        super().__init__(parent)
        self.module_info = module_info
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 标题区域
        title_frame = self.create_title_section()
        scroll_layout.addWidget(title_frame)
        
        # 功能状态区域
        status_frame = self.create_status_section()
        scroll_layout.addWidget(status_frame)
        
        # 计划功能区域
        features_frame = self.create_features_section()
        scroll_layout.addWidget(features_frame)
        
        # 开发进度区域
        progress_frame = self.create_progress_section()
        scroll_layout.addWidget(progress_frame)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
    
    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # 主标题
        title = QLabel(f"{self.module_info['icon']} {self.module_info['name']}")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #4CAF50; 
            margin: 10px;
        """)
        layout.addWidget(title)
        
        # 副标题
        subtitle = QLabel(self.module_info['description'])
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet("""
            font-size: 16px; 
            color: #888; 
            margin-bottom: 10px;
        """)
        layout.addWidget(subtitle)
        
        return frame
    
    def create_status_section(self):
        """创建状态区域"""
        group = QGroupBox("📊 开发状态")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #fff;
                border: 2px solid #444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # 状态信息
        progress = self.module_info.get('progress', 0)
        status_color = "#4CAF50" if progress >= 80 else "#FF9800" if progress >= 50 else "#FFC107"
        status_text = "即将完成" if progress >= 80 else "开发中" if progress >= 20 else "计划中"
        
        status_info = QLabel(f"""
        <div style='line-height: 1.6;'>
        <p><span style='color: #FF9800;'>⚠️ 当前状态：</span> <span style='color: {status_color}; font-weight: bold;'>{status_text}</span></p>
        <p><span style='color: #2196F3;'>📅 预计完成：</span> <span style='color: #fff;'>{self.module_info.get('eta', '待定')}</span></p>
        <p><span style='color: #4CAF50;'>✅ 基础架构：</span> <span style='color: #fff;'>已完成</span></p>
        <p><span style='color: #9C27B0;'>🔧 数据模型：</span> <span style='color: #fff;'>已完成</span></p>
        </div>
        """)
        status_info.setStyleSheet("color: #fff; font-size: 14px; padding: 10px;")
        layout.addWidget(status_info)
        
        # 进度条
        progress_label = QLabel("整体进度：")
        progress_label.setStyleSheet("color: #fff; font-size: 14px; margin-top: 10px;")
        layout.addWidget(progress_label)
        
        progress_bar = QProgressBar()
        progress_bar.setValue(progress)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        layout.addWidget(progress_bar)
        
        return group
    
    def create_features_section(self):
        """创建功能计划区域"""
        group = QGroupBox("🎯 计划功能")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #fff;
                border: 2px solid #444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        features_text = QTextEdit()
        features_text.setReadOnly(True)
        features_text.setMaximumHeight(200)
        features_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 5px;
                color: #fff;
                font-size: 13px;
                padding: 10px;
            }
        """)
        
        features_content = "\n".join(self.module_info.get('features', []))
        features_text.setPlainText(features_content)
        layout.addWidget(features_text)
        
        return group
    
    def create_progress_section(self):
        """创建开发进度区域"""
        group = QGroupBox("🚀 开发计划")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #fff;
                border: 2px solid #444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # 开发阶段
        phases = self.module_info.get('phases', [])
        
        for phase_name, desc, status, color in phases:
            phase_layout = QHBoxLayout()
            
            phase_label = QLabel(f"{phase_name}:")
            phase_label.setStyleSheet(f"color: {color}; font-weight: bold; min-width: 80px;")
            phase_layout.addWidget(phase_label)
            
            desc_label = QLabel(desc)
            desc_label.setStyleSheet("color: #fff;")
            phase_layout.addWidget(desc_label)
            
            phase_layout.addStretch()
            
            status_label = QLabel(status)
            status_label.setStyleSheet(f"color: {color}; font-weight: bold;")
            phase_layout.addWidget(status_label)
            
            layout.addLayout(phase_layout)
        
        # 联系信息
        contact_label = QLabel("""
        <div style='margin-top: 20px; padding: 15px; background-color: #2b2b2b; border-radius: 5px;'>
        <p style='color: #4CAF50; font-weight: bold;'>💡 需要此功能？</p>
        <p style='color: #fff;'>此模块正在积极开发中，如果您急需此功能，请联系开发团队。</p>
        <p style='color: #888;'>我们可以根据您的需求调整开发优先级。</p>
        </div>
        """)
        layout.addWidget(contact_label)
        
        return group
