#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修复 - 简化版启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QStatusBar, QProgressBar
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QIcon
except ImportError as e:
    print(f"错误: 无法导入PyQt6库: {e}")
    print("请运行: pip install PyQt6")
    sys.exit(1)


class TestMainWindow(QMainWindow):
    """测试主窗口 - 专门用于测试状态栏修复"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_stylesheet()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("电商管理系统 - UI修复测试")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1300)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加一些测试内容
        test_label = QLabel("UI布局修复测试")
        test_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        test_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 50px;")
        layout.addWidget(test_label)
        
        info_label = QLabel("检查状态栏是否占用过多空间")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("font-size: 16px; color: #888; margin: 20px;")
        layout.addWidget(info_label)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 设置状态栏高度限制
        self.status_bar.setMaximumHeight(25)
        self.status_bar.setMinimumHeight(20)
        self.status_bar.setSizeGripEnabled(False)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setMaximumHeight(20)
        self.status_bar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setMaximumHeight(16)
        self.progress_bar.setMinimumHeight(12)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # 数据库状态
        self.db_status_label = QLabel("数据库: 已连接")
        self.db_status_label.setMaximumHeight(20)
        self.status_bar.addPermanentWidget(self.db_status_label)
    
    def load_stylesheet(self):
        """加载样式表"""
        try:
            qss_file = project_root / "resources" / "themes" / "dark.qss"
            if qss_file.exists():
                with open(qss_file, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
                self.setStyleSheet(stylesheet)
                print("样式表加载成功")
            else:
                print(f"样式表文件不存在: {qss_file}")
        except Exception as e:
            print(f"加载样式表失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("电商管理系统")
    app.setApplicationVersion("2.0.0")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    print("测试窗口已启动，请检查状态栏高度是否正常")
    
    return app.exec()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"程序异常: {e}")
        sys.exit(1)
