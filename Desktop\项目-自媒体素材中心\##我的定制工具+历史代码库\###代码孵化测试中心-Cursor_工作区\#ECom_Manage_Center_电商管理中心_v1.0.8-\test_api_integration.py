#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API集成功能测试

测试多平台API对接功能，包括客户端创建、订单同步等。
"""

import os
import sys
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager
from core.api.platform_factory import PlatformAPIFactory
from core.services.order_sync_service import OrderSyncService
from core.managers.mapping_manager import MappingManager
from core.managers.product_manager import ProductManager
from core.models.product import Product


def test_api_factory():
    """测试API工厂"""
    print("\n" + "=" * 60)
    print("🏭 API工厂测试")
    print("=" * 60)
    
    try:
        # 创建API工厂
        factory = PlatformAPIFactory()
        print("✅ API工厂创建成功")
        
        # 检查支持的平台
        supported_platforms = factory.get_supported_platforms()
        print(f"✅ 支持的平台: {supported_platforms}")
        
        # 测试平台支持检查
        for platform in ['taobao', 'xiaohongshu', 'douyin', '1688']:
            is_supported = factory.is_platform_supported(platform)
            print(f"✅ {platform} 平台支持: {is_supported}")
        
        # 测试不支持的平台
        is_supported = factory.is_platform_supported('unknown_platform')
        print(f"✅ 未知平台支持检查: {is_supported}")
        
        # 获取缓存信息
        cache_info = factory.get_cache_info()
        print(f"✅ 缓存信息: {cache_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ API工厂测试失败: {e}")
        return False


def test_api_clients():
    """测试API客户端"""
    print("\n" + "=" * 60)
    print("🔌 API客户端测试")
    print("=" * 60)
    
    try:
        factory = PlatformAPIFactory()
        
        # 测试配置
        test_configs = {
            'taobao': {
                'app_key': 'test_app_key',
                'app_secret': 'test_app_secret',
                'session_key': 'test_session_key'
            },
            'xiaohongshu': {
                'app_id': 'test_app_id',
                'app_secret': 'test_app_secret',
                'access_token': 'test_access_token'
            },
            'douyin': {
                'app_key': 'test_app_key',
                'app_secret': 'test_app_secret',
                'access_token': 'test_access_token'
            },
            '1688': {
                'app_key': 'test_app_key',
                'app_secret': 'test_app_secret',
                'access_token': 'test_access_token'
            }
        }
        
        created_clients = 0
        for platform_type, config in test_configs.items():
            try:
                client = factory.create_client(platform_type, config)
                if client:
                    print(f"✅ {platform_type} 客户端创建成功")
                    
                    # 测试基本方法
                    platform = client.get_platform_type()
                    print(f"   - 平台类型: {platform}")
                    
                    # 测试商品信息获取（示例）
                    product_info = client.get_product_info('test_product_123')
                    if product_info:
                        print(f"   - 商品信息获取: {product_info.get('name', 'N/A')}")
                    
                    created_clients += 1
                    client.close()
                else:
                    print(f"❌ {platform_type} 客户端创建失败")
                    
            except Exception as e:
                print(f"❌ {platform_type} 客户端测试异常: {e}")
        
        print(f"✅ 成功创建 {created_clients}/{len(test_configs)} 个客户端")
        
        # 清理缓存
        factory.clear_cache()
        print("✅ 缓存清理完成")
        
        return created_clients > 0
        
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False


def test_order_sync_service():
    """测试订单同步服务"""
    print("\n" + "=" * 60)
    print("🔄 订单同步服务测试")
    print("=" * 60)
    
    # 初始化数据库
    db_path = "data/test_api_integration.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    db_manager = DatabaseManager(db_path)
    
    try:
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 创建订单同步服务
        sync_service = OrderSyncService(db_manager)
        print("✅ 订单同步服务创建成功")
        
        # 测试配置获取
        print(f"   - 同步间隔: {sync_service.sync_interval} 分钟")
        print(f"   - 自动匹配: {sync_service.auto_matching_enabled}")
        print(f"   - 置信度阈值: {sync_service.confidence_threshold}")
        
        # 创建测试商品和映射
        product_manager = ProductManager(db_manager)
        mapping_manager = MappingManager(db_manager)
        
        # 创建测试商品
        test_product = Product(
            product_id=f"API_TEST_{uuid.uuid4().hex[:8]}",
            name="API测试商品",
            category="测试分类",
            selling_price=99.99,
            quantity=100
        )
        
        if product_manager.create_product(test_product):
            print(f"✅ 创建测试商品: {test_product.name}")
            
            # 创建测试映射
            if mapping_manager.create_mapping(
                sku_id=test_product.product_id,
                platform_type="taobao",
                platform_product_id="TB_API_TEST_123",
                notes="API测试映射"
            ):
                print("✅ 创建测试映射成功")
            else:
                print("❌ 创建测试映射失败")
        else:
            print("❌ 创建测试商品失败")
        
        # 测试智能匹配
        matches = mapping_manager.smart_match_products(
            {
                'product_name': 'API测试商品',
                'price': 99.99
            },
            confidence_threshold=0.5
        )
        
        if matches:
            print(f"✅ 智能匹配测试成功: 找到 {len(matches)} 个匹配项")
        else:
            print("⚠️ 智能匹配测试未找到结果")
        
        # 测试订单匹配日志
        log_success = mapping_manager.log_order_matching(
            platform_order_id="API_TEST_ORDER_123",
            platform_type="taobao",
            platform_product_id="TB_API_TEST_123",
            product_name="API测试商品",
            matching_status="auto_matched",
            matched_sku_id=test_product.product_id,
            confidence_score=0.95,
            matching_method="api_test"
        )
        
        if log_success:
            print("✅ 订单匹配日志记录成功")
        else:
            print("❌ 订单匹配日志记录失败")
        
        db_manager.close()
        print("✅ 订单同步服务测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 订单同步服务测试失败: {e}")
        return False


def test_integration_workflow():
    """测试完整的集成工作流"""
    print("\n" + "=" * 60)
    print("🔗 集成工作流测试")
    print("=" * 60)
    
    try:
        # 模拟完整的订单处理流程
        print("📋 模拟订单处理流程:")
        print("1. 平台订单 → 2. 商品匹配 → 3. 本地订单 → 4. 库存扣减")
        
        # 模拟平台订单数据
        mock_order = {
            'tid': 'MOCK_ORDER_12345',
            'buyer_nick': 'test_buyer',
            'total_fee': 99.99,
            'orders': {
                'order': {
                    'num_iid': 'TB_API_TEST_123',
                    'title': 'API测试商品',
                    'price': 99.99,
                    'num': 1
                }
            }
        }
        
        print(f"✅ 模拟订单数据: {mock_order['tid']}")
        
        # 模拟匹配过程
        platform_product_id = mock_order['orders']['order']['num_iid']
        product_name = mock_order['orders']['order']['title']
        
        print(f"✅ 商品匹配: {platform_product_id} -> {product_name}")
        
        # 模拟库存扣减
        quantity_to_deduct = mock_order['orders']['order']['num']
        print(f"✅ 库存扣减: {quantity_to_deduct} 件")
        
        print("✅ 集成工作流模拟完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成工作流测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 API集成功能测试")
    print("=" * 80)
    
    tests = [
        ("API工厂", test_api_factory),
        ("API客户端", test_api_clients),
        ("订单同步服务", test_order_sync_service),
        ("集成工作流", test_integration_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有API集成功能测试通过！")
        print("\n📋 API集成功能已就绪:")
        print("1. 多平台API客户端框架")
        print("2. 平台API工厂管理")
        print("3. 订单自动同步服务")
        print("4. 智能商品匹配算法")
        print("5. 完整的集成工作流")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关功能。")
    
    input("\n按任意键退出...")


if __name__ == "__main__":
    main()
