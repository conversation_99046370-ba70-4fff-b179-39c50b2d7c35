#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台管理仪表板

提供多平台库存管理系统的统计概览和快速操作界面。
"""

import json
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QGroupBox, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QSizePolicy, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPalette

from core.database import DatabaseManager
from core.managers.product_manager import ProductManager
from core.managers.order_manager import OrderManager
from core.managers.mapping_manager import MappingManager
from utils.logger import LoggerMixin


class StatCard(QFrame):
    """统计卡片组件"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", 
                 color: str = "#2196F3", parent=None):
        super().__init__(parent)
        self.init_ui(title, value, subtitle, color)
    
    def init_ui(self, title: str, value: str, subtitle: str, color: str):
        """初始化界面"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                margin: 2px;
            }}
            QFrame:hover {{
                border-color: {color};
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(title_label)
        
        # 数值
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # 副标题
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("color: #999; font-size: 11px;")
            layout.addWidget(subtitle_label)
        
        layout.addStretch()


class PlatformStatusWidget(QWidget):
    """平台状态组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("平台状态")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 平台状态表格
        self.status_table = QTableWidget(0, 4)
        self.status_table.setHorizontalHeaderLabels(['平台', '状态', '商品数', '最后同步'])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        self.status_table.setMaximumHeight(150)
        
        # 设置表格样式
        self.status_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.status_table)
        
        # 更新平台状态
        self.update_platform_status()
    
    def update_platform_status(self):
        """更新平台状态"""
        platforms = [
            {'name': 'Taobao', 'status': '✅ 已连接', 'products': 156, 'sync': '2分钟前'},
            {'name': 'XiaoHongShu', 'status': '⚪ 未配置', 'products': 0, 'sync': '从未'},
            {'name': 'Douyin', 'status': '⚪ 未配置', 'products': 0, 'sync': '从未'},
            {'name': '1688', 'status': '✅ 已连接', 'products': 89, 'sync': '5分钟前'},
        ]
        
        self.status_table.setRowCount(len(platforms))
        
        for i, platform in enumerate(platforms):
            self.status_table.setItem(i, 0, QTableWidgetItem(platform['name']))
            self.status_table.setItem(i, 1, QTableWidgetItem(platform['status']))
            self.status_table.setItem(i, 2, QTableWidgetItem(str(platform['products'])))
            self.status_table.setItem(i, 3, QTableWidgetItem(platform['sync']))


class RecentActivityWidget(QWidget):
    """最近活动组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("最近活动")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 活动列表
        self.activity_table = QTableWidget(0, 3)
        self.activity_table.setHorizontalHeaderLabels(['时间', '活动', '详情'])
        self.activity_table.horizontalHeader().setStretchLastSection(True)
        self.activity_table.setMaximumHeight(200)
        
        # 设置表格样式
        self.activity_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #eee;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 6px;
                border: none;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.activity_table)
        
        # 更新活动记录
        self.update_recent_activity()
    
    def update_recent_activity(self):
        """更新最近活动"""
        activities = [
            {'time': '2分钟前', 'activity': '订单同步', 'detail': '淘宝平台同步了3个新订单'},
            {'time': '5分钟前', 'activity': '商品映射', 'detail': '创建了智能手机的平台映射'},
            {'time': '10分钟前', 'activity': '库存更新', 'detail': '手机壳库存自动扣减50个'},
            {'time': '15分钟前', 'activity': '代发处理', 'detail': '1688代发订单已提交'},
            {'time': '30分钟前', 'activity': '状态同步', 'detail': '订单状态已同步到各平台'},
        ]
        
        self.activity_table.setRowCount(len(activities))
        
        for i, activity in enumerate(activities):
            self.activity_table.setItem(i, 0, QTableWidgetItem(activity['time']))
            self.activity_table.setItem(i, 1, QTableWidgetItem(activity['activity']))
            self.activity_table.setItem(i, 2, QTableWidgetItem(activity['detail']))


class MultiplatformDashboard(QWidget, LoggerMixin):
    """多平台管理仪表板"""
    
    # 信号定义
    refresh_requested = pyqtSignal()
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_manager = ProductManager(db_manager)
        self.order_manager = OrderManager(db_manager)
        self.mapping_manager = MappingManager(db_manager)
        
        self.init_ui()
        self.setup_timer()
        self.load_statistics()
    
    def init_ui(self):
        """初始化界面"""
        # 主滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 主容器
        main_widget = QWidget()
        scroll_area.setWidget(main_widget)
        
        layout = QVBoxLayout(self)
        layout.addWidget(scroll_area)
        
        # 主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        title_label = QLabel("多平台管理仪表板")
        title_font = QFont()
        title_font.setPointSize(20)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        refresh_button = QPushButton("🔄 刷新")
        refresh_button.clicked.connect(self.refresh_data)
        refresh_button.setMaximumWidth(100)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(refresh_button)
        
        main_layout.addLayout(title_layout)
        
        # 统计卡片区域
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # 创建统计卡片
        self.total_products_card = StatCard("总商品数", "0", "多平台商品", "#2196F3")
        self.mapped_products_card = StatCard("已映射商品", "0", "完成平台映射", "#4CAF50")
        self.pending_orders_card = StatCard("待处理订单", "0", "未匹配订单", "#FF9800")
        self.dropship_orders_card = StatCard("代发订单", "0", "今日代发", "#9C27B0")
        
        stats_layout.addWidget(self.total_products_card, 0, 0)
        stats_layout.addWidget(self.mapped_products_card, 0, 1)
        stats_layout.addWidget(self.pending_orders_card, 0, 2)
        stats_layout.addWidget(self.dropship_orders_card, 0, 3)
        
        main_layout.addLayout(stats_layout)
        
        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        
        # 左侧：平台状态
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        self.platform_status_widget = PlatformStatusWidget()
        left_layout.addWidget(self.platform_status_widget)
        
        # 快速操作按钮
        quick_actions_group = QGroupBox("快速操作")
        quick_actions_layout = QVBoxLayout(quick_actions_group)
        
        sync_button = QPushButton("🔄 同步所有平台")
        sync_button.clicked.connect(self.sync_all_platforms)
        
        match_button = QPushButton("🧠 智能匹配订单")
        match_button.clicked.connect(self.smart_match_orders)
        
        export_button = QPushButton("📊 导出报告")
        export_button.clicked.connect(self.export_report)
        
        quick_actions_layout.addWidget(sync_button)
        quick_actions_layout.addWidget(match_button)
        quick_actions_layout.addWidget(export_button)
        
        left_layout.addWidget(quick_actions_group)
        left_layout.addStretch()
        
        content_layout.addWidget(left_widget)
        
        # 右侧：最近活动
        self.recent_activity_widget = RecentActivityWidget()
        content_layout.addWidget(self.recent_activity_widget)
        
        # 设置比例
        content_layout.setStretch(0, 1)
        content_layout.setStretch(1, 1)
        
        main_layout.addLayout(content_layout)
    
    def setup_timer(self):
        """设置定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_statistics)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def load_statistics(self):
        """加载统计数据"""
        try:
            # 获取商品统计
            product_stats = self.product_manager.get_statistics()
            
            # 获取订单统计
            order_stats = self.order_manager.get_statistics()
            
            # 获取未匹配订单
            unmatched_orders = self.order_manager.get_unmatched_orders()
            
            # 更新统计卡片
            self.total_products_card.findChild(QLabel).setText(str(product_stats.get('total_products', 0)))
            
            # 获取多平台商品数量
            multi_platform_products = self.product_manager.get_multi_platform_products()
            self.mapped_products_card.findChild(QLabel).setText(str(len(multi_platform_products)))
            
            self.pending_orders_card.findChild(QLabel).setText(str(len(unmatched_orders)))
            self.dropship_orders_card.findChild(QLabel).setText(str(order_stats.get('dropship_orders', 0)))
            
        except Exception as e:
            self.logger.error(f"加载统计数据失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_statistics()
        self.platform_status_widget.update_platform_status()
        self.recent_activity_widget.update_recent_activity()
        self.refresh_requested.emit()
    
    def sync_all_platforms(self):
        """同步所有平台"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "同步中", "正在同步所有平台数据...")
    
    def smart_match_orders(self):
        """智能匹配订单"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "匹配中", "正在执行智能订单匹配...")
    
    def export_report(self):
        """导出报告"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "导出中", "正在生成多平台管理报告...")
