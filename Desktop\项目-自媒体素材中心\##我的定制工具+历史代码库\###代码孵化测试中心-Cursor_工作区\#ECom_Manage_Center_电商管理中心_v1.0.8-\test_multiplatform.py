#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台库存管理系统测试

测试新增的多平台功能，包括数据库扩展、映射管理器等。
"""

import os
import sys
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager
from core.managers.mapping_manager import MappingManager
from core.managers.product_manager import ProductManager
from core.models.product import Product


def test_database_extension():
    """测试数据库扩展"""
    print("\n" + "=" * 60)
    print("🗄️ 多平台数据库扩展测试")
    print("=" * 60)
    
    # 删除现有数据库
    db_path = "data/test_multiplatform.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print("✅ 删除现有数据库文件")
    
    # 初始化数据库
    db_manager = DatabaseManager(db_path)
    
    try:
        # 连接和初始化
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 检查表是否创建成功
        tables_sql = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        tables = db_manager.fetch_all(tables_sql)
        table_names = [table[0] for table in tables]
        
        print(f"✅ 创建了 {len(table_names)} 个表: {table_names}")
        
        # 检查多平台相关表
        expected_tables = [
            'platform_product_mapping',
            'order_matching_logs', 
            'inventory_sync_records'
        ]
        
        missing_tables = []
        for table in expected_tables:
            if table in table_names:
                print(f"✅ 多平台表存在: {table}")
            else:
                missing_tables.append(table)
                print(f"❌ 多平台表缺失: {table}")
        
        if missing_tables:
            print(f"❌ 缺失多平台表: {missing_tables}")
            return False
        
        # 检查products表的新字段
        products_info = db_manager.fetch_all("PRAGMA table_info(products)")
        column_names = [col[1] for col in products_info]
        
        expected_columns = [
            'is_multi_platform',
            'platform_count',
            'sync_status',
            'inventory_type'
        ]
        
        missing_columns = []
        for column in expected_columns:
            if column in column_names:
                print(f"✅ 商品表新字段存在: {column}")
            else:
                missing_columns.append(column)
                print(f"❌ 商品表新字段缺失: {column}")
        
        if missing_columns:
            print(f"❌ 缺失商品表字段: {missing_columns}")
            return False
        
        # 检查系统设置
        settings = db_manager.fetch_all(
            "SELECT setting_key FROM system_settings WHERE category = 'multi_platform'"
        )
        setting_keys = [setting[0] for setting in settings]
        
        if setting_keys:
            print(f"✅ 多平台系统设置: {len(setting_keys)}个")
        else:
            print("❌ 多平台系统设置缺失")
            return False
        
        db_manager.close()
        print("✅ 数据库关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_mapping_manager():
    """测试映射管理器"""
    print("\n" + "=" * 60)
    print("🔗 映射管理器测试")
    print("=" * 60)
    
    # 初始化数据库和管理器
    db_path = "data/test_multiplatform.db"
    db_manager = DatabaseManager(db_path)
    
    try:
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        # 创建管理器
        mapping_manager = MappingManager(db_manager)
        product_manager = ProductManager(db_manager)
        
        print("✅ 管理器创建成功")
        
        # 创建测试商品
        test_product = Product(
            product_id=f"TEST_{uuid.uuid4().hex[:8]}",
            name="测试多平台商品",
            category="测试分类",
            selling_price=99.99,
            quantity=100
        )
        
        if not product_manager.create_product(test_product):
            print("❌ 创建测试商品失败")
            return False
        
        print(f"✅ 创建测试商品: {test_product.name}")
        
        # 测试创建映射
        mapping_success = mapping_manager.create_mapping(
            sku_id=test_product.product_id,
            platform_type="taobao",
            platform_product_id="TB_123456789",
            notes="测试映射"
        )
        
        if mapping_success:
            print("✅ 创建平台映射成功")
        else:
            print("❌ 创建平台映射失败")
            return False
        
        # 测试获取映射
        mapping = mapping_manager.get_mapping_by_platform_id(
            platform_type="taobao",
            platform_product_id="TB_123456789"
        )
        
        if mapping:
            print(f"✅ 获取映射成功: {mapping['sku_id']} -> {mapping['platform_product_id']}")
        else:
            print("❌ 获取映射失败")
            return False
        
        # 测试智能匹配
        platform_order_data = {
            'product_name': '测试多平台商品',
            'price': 99.99
        }
        
        matches = mapping_manager.smart_match_products(platform_order_data, 0.5)
        
        if matches:
            print(f"✅ 智能匹配成功: 找到{len(matches)}个匹配项")
            for match in matches[:3]:  # 显示前3个
                print(f"   - {match['product_name']} (置信度: {match['confidence']:.2f})")
        else:
            print("⚠️ 智能匹配未找到结果")
        
        # 测试订单匹配日志
        log_success = mapping_manager.log_order_matching(
            platform_order_id="ORDER_123456",
            platform_type="taobao",
            platform_product_id="TB_123456789",
            product_name="测试多平台商品",
            matching_status="auto_matched",
            matched_sku_id=test_product.product_id,
            confidence_score=0.95,
            matching_method="smart_algorithm"
        )
        
        if log_success:
            print("✅ 订单匹配日志记录成功")
        else:
            print("❌ 订单匹配日志记录失败")
            return False
        
        db_manager.close()
        print("✅ 映射管理器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 映射管理器测试失败: {e}")
        return False


def test_product_multiplatform_features():
    """测试商品多平台功能"""
    print("\n" + "=" * 60)
    print("📦 商品多平台功能测试")
    print("=" * 60)
    
    # 初始化数据库和管理器
    db_path = "data/test_multiplatform.db"
    db_manager = DatabaseManager(db_path)
    
    try:
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        product_manager = ProductManager(db_manager)
        
        # 创建多平台商品
        multiplatform_product = Product(
            product_id=f"MP_{uuid.uuid4().hex[:8]}",
            name="多平台测试商品",
            category="电子产品",
            selling_price=299.99,
            quantity=50,
            is_multi_platform=True,
            inventory_type="physical",
            sync_status="synced"
        )
        
        if not product_manager.create_product(multiplatform_product):
            print("❌ 创建多平台商品失败")
            return False
        
        print(f"✅ 创建多平台商品: {multiplatform_product.name}")
        
        # 获取商品并检查多平台字段
        retrieved_product = product_manager.get_product(multiplatform_product.product_id)
        
        if retrieved_product:
            print(f"✅ 获取商品成功")
            print(f"   - 是否多平台: {retrieved_product.is_multi_platform}")
            print(f"   - 库存类型: {retrieved_product.inventory_type}")
            print(f"   - 同步状态: {retrieved_product.sync_status}")
        else:
            print("❌ 获取商品失败")
            return False
        
        db_manager.close()
        print("✅ 商品多平台功能测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 商品多平台功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 多平台库存管理系统测试")
    print("=" * 80)
    
    tests = [
        ("数据库扩展", test_database_extension),
        ("映射管理器", test_mapping_manager),
        ("商品多平台功能", test_product_multiplatform_features),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有多平台功能测试通过！系统升级成功。")
        print("\n📋 多平台功能已就绪:")
        print("1. 平台商品ID映射管理")
        print("2. 智能商品匹配算法")
        print("3. 订单匹配日志记录")
        print("4. 商品多平台状态管理")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，请检查相关功能。")
    
    input("\n按任意键退出...")


if __name__ == "__main__":
    main()
