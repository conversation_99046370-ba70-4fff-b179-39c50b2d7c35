#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置模块主界面

系统配置和设置功能的完整实现。
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QGroupBox,
    QLabel,
    QLineEdit,
    QComboBox,
    QCheckBox,
    QSpinBox,
    QDoubleSpinBox,
    QPushButton,
    QTextEdit,
    QFileDialog,
    QMessageBox,
    QFormLayout,
    QScrollArea,
    QFrame,
    QSlider,
    QProgressBar,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap

from core.config_manager import ConfigManager
from utils.logger import LoggerMixin


class SettingsWidget(QWidget, LoggerMixin):
    """系统设置主界面"""

    # 设置变更信号
    settings_changed = pyqtSignal(str, object)  # 设置键, 新值

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.config_manager = ConfigManager(db_manager)

        # 设置缓存
        self.settings_cache = {}

        # 初始化界面
        self.init_ui()

        # 加载设置
        self.load_settings()

        self.logger.info("系统设置界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标题
        title_frame = self.create_title_section()
        layout.addWidget(title_frame)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 10px 20px;
                margin-right: 2px;
                border: 1px solid #555;
                border-bottom: none;
                font-size: 13px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """
        )

        # 基础设置标签页
        general_tab = self.create_general_settings()
        tab_widget.addTab(general_tab, "🔧 基础设置")

        # 界面设置标签页
        ui_tab = self.create_ui_settings()
        tab_widget.addTab(ui_tab, "🎨 界面设置")

        # 数据库设置标签页
        db_tab = self.create_database_settings()
        tab_widget.addTab(db_tab, "🗄️ 数据库")

        # API设置标签页
        api_tab = self.create_api_settings()
        tab_widget.addTab(api_tab, "🔗 API配置")

        # 系统监控标签页
        monitor_tab = self.create_monitor_settings()
        tab_widget.addTab(monitor_tab, "📊 系统监控")

        layout.addWidget(tab_widget)

        # 底部按钮
        button_frame = self.create_button_section()
        layout.addWidget(button_frame)

    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: #2b2b2b;
                border-radius: 10px;
                padding: 15px;
            }
        """
        )

        layout = QHBoxLayout(frame)

        # 标题
        title = QLabel("⚙️ 系统设置")
        title.setStyleSheet(
            """
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        """
        )
        layout.addWidget(title)

        layout.addStretch()

        # 版本信息
        version_label = QLabel("v2.0.0")
        version_label.setStyleSheet("color: #888; font-size: 14px;")
        layout.addWidget(version_label)

        return frame

    def create_general_settings(self):
        """创建基础设置标签页"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)

        # 应用程序设置
        app_group = QGroupBox("应用程序设置")
        app_group.setStyleSheet(self.get_group_style())
        app_layout = QFormLayout(app_group)

        # 应用名称
        self.app_name_edit = QLineEdit()
        self.app_name_edit.setStyleSheet(self.get_input_style())
        app_layout.addRow("应用名称:", self.app_name_edit)

        # 语言设置
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English", "繁體中文"])
        self.language_combo.setStyleSheet(self.get_input_style())
        app_layout.addRow("界面语言:", self.language_combo)

        # 自动保存间隔
        self.autosave_spin = QSpinBox()
        self.autosave_spin.setRange(1, 60)
        self.autosave_spin.setSuffix(" 分钟")
        self.autosave_spin.setStyleSheet(self.get_input_style())
        app_layout.addRow("自动保存间隔:", self.autosave_spin)

        # 启动时检查更新
        self.check_update_cb = QCheckBox("启动时检查更新")
        self.check_update_cb.setStyleSheet("color: white;")
        app_layout.addRow("", self.check_update_cb)

        layout.addWidget(app_group)

        # 数据设置
        data_group = QGroupBox("数据设置")
        data_group.setStyleSheet(self.get_group_style())
        data_layout = QFormLayout(data_group)

        # 默认货币
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(
            ["CNY (人民币)", "USD (美元)", "EUR (欧元)", "JPY (日元)"]
        )
        self.currency_combo.setStyleSheet(self.get_input_style())
        data_layout.addRow("默认货币:", self.currency_combo)

        # 小数位数
        self.decimal_spin = QSpinBox()
        self.decimal_spin.setRange(0, 6)
        self.decimal_spin.setStyleSheet(self.get_input_style())
        data_layout.addRow("价格小数位数:", self.decimal_spin)

        # 数据保留天数
        self.retention_spin = QSpinBox()
        self.retention_spin.setRange(30, 3650)
        self.retention_spin.setSuffix(" 天")
        self.retention_spin.setStyleSheet(self.get_input_style())
        data_layout.addRow("日志保留天数:", self.retention_spin)

        layout.addWidget(data_group)

        # 性能设置
        perf_group = QGroupBox("性能设置")
        perf_group.setStyleSheet(self.get_group_style())
        perf_layout = QFormLayout(perf_group)

        # 每页显示数量
        self.page_size_spin = QSpinBox()
        self.page_size_spin.setRange(10, 1000)
        self.page_size_spin.setStyleSheet(self.get_input_style())
        perf_layout.addRow("每页显示数量:", self.page_size_spin)

        # 缓存大小
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setSuffix(" MB")
        self.cache_size_spin.setStyleSheet(self.get_input_style())
        perf_layout.addRow("缓存大小:", self.cache_size_spin)

        # 启用多线程
        self.multithread_cb = QCheckBox("启用多线程处理")
        self.multithread_cb.setStyleSheet("color: white;")
        perf_layout.addRow("", self.multithread_cb)

        layout.addWidget(perf_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area

    def create_ui_settings(self):
        """创建界面设置标签页"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)

        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_group.setStyleSheet(self.get_group_style())
        theme_layout = QFormLayout(theme_group)

        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["暗黑主题", "明亮主题", "自动切换"])
        self.theme_combo.setStyleSheet(self.get_input_style())
        theme_layout.addRow("界面主题:", self.theme_combo)

        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setSuffix(" pt")
        self.font_size_spin.setStyleSheet(self.get_input_style())
        theme_layout.addRow("字体大小:", self.font_size_spin)

        # 界面缩放
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(80, 200)
        self.scale_slider.setValue(100)
        self.scale_slider.setStyleSheet(
            """
            QSlider::groove:horizontal {
                border: 1px solid #555;
                height: 8px;
                background: #333;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #4CAF50;
                border: 1px solid #555;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
        """
        )

        scale_layout = QHBoxLayout()
        scale_layout.addWidget(self.scale_slider)
        self.scale_label = QLabel("100%")
        self.scale_label.setStyleSheet("color: white; min-width: 40px;")
        scale_layout.addWidget(self.scale_label)
        self.scale_slider.valueChanged.connect(
            lambda v: self.scale_label.setText(f"{v}%")
        )

        theme_layout.addRow("界面缩放:", scale_layout)

        layout.addWidget(theme_group)

        # 窗口设置
        window_group = QGroupBox("窗口设置")
        window_group.setStyleSheet(self.get_group_style())
        window_layout = QFormLayout(window_group)

        # 记住窗口位置
        self.remember_pos_cb = QCheckBox("记住窗口位置和大小")
        self.remember_pos_cb.setStyleSheet("color: white;")
        window_layout.addRow("", self.remember_pos_cb)

        # 最小化到托盘
        self.minimize_tray_cb = QCheckBox("最小化到系统托盘")
        self.minimize_tray_cb.setStyleSheet("color: white;")
        window_layout.addRow("", self.minimize_tray_cb)

        # 启动时最大化
        self.start_maximized_cb = QCheckBox("启动时最大化窗口")
        self.start_maximized_cb.setStyleSheet("color: white;")
        window_layout.addRow("", self.start_maximized_cb)

        layout.addWidget(window_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area

    def create_database_settings(self):
        """创建数据库设置标签页"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)

        # 数据库连接
        db_group = QGroupBox("数据库连接")
        db_group.setStyleSheet(self.get_group_style())
        db_layout = QFormLayout(db_group)

        # 数据库路径
        db_path_layout = QHBoxLayout()
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setStyleSheet(self.get_input_style())
        self.db_path_edit.setReadOnly(True)
        db_path_layout.addWidget(self.db_path_edit)

        browse_btn = QPushButton("浏览...")
        browse_btn.setStyleSheet(self.get_button_style())
        browse_btn.clicked.connect(self.browse_database_path)
        db_path_layout.addWidget(browse_btn)

        db_layout.addRow("数据库文件:", db_path_layout)

        # 连接池大小
        self.pool_size_spin = QSpinBox()
        self.pool_size_spin.setRange(1, 50)
        self.pool_size_spin.setStyleSheet(self.get_input_style())
        db_layout.addRow("连接池大小:", self.pool_size_spin)

        # 查询超时
        self.query_timeout_spin = QSpinBox()
        self.query_timeout_spin.setRange(5, 300)
        self.query_timeout_spin.setSuffix(" 秒")
        self.query_timeout_spin.setStyleSheet(self.get_input_style())
        db_layout.addRow("查询超时:", self.query_timeout_spin)

        layout.addWidget(db_group)

        # 备份设置
        backup_group = QGroupBox("备份设置")
        backup_group.setStyleSheet(self.get_group_style())
        backup_layout = QFormLayout(backup_group)

        # 自动备份
        self.auto_backup_cb = QCheckBox("启用自动备份")
        self.auto_backup_cb.setStyleSheet("color: white;")
        backup_layout.addRow("", self.auto_backup_cb)

        # 备份间隔
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 168)
        self.backup_interval_spin.setSuffix(" 小时")
        self.backup_interval_spin.setStyleSheet(self.get_input_style())
        backup_layout.addRow("备份间隔:", self.backup_interval_spin)

        # 备份保留数量
        self.backup_keep_spin = QSpinBox()
        self.backup_keep_spin.setRange(1, 100)
        self.backup_keep_spin.setSuffix(" 个")
        self.backup_keep_spin.setStyleSheet(self.get_input_style())
        backup_layout.addRow("保留备份数:", self.backup_keep_spin)

        layout.addWidget(backup_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area

    def create_api_settings(self):
        """创建API设置标签页"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)

        # 1688 API设置
        api_1688_group = QGroupBox("1688 API配置")
        api_1688_group.setStyleSheet(self.get_group_style())
        api_1688_layout = QFormLayout(api_1688_group)

        # App Key
        self.api_1688_key_edit = QLineEdit()
        self.api_1688_key_edit.setStyleSheet(self.get_input_style())
        self.api_1688_key_edit.setPlaceholderText("请输入1688 App Key")
        api_1688_layout.addRow("App Key:", self.api_1688_key_edit)

        # App Secret
        self.api_1688_secret_edit = QLineEdit()
        self.api_1688_secret_edit.setStyleSheet(self.get_input_style())
        self.api_1688_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_1688_secret_edit.setPlaceholderText("请输入1688 App Secret")
        api_1688_layout.addRow("App Secret:", self.api_1688_secret_edit)

        # 测试连接按钮
        test_1688_btn = QPushButton("测试连接")
        test_1688_btn.setStyleSheet(self.get_button_style())
        test_1688_btn.clicked.connect(lambda: self.test_api_connection("1688"))
        api_1688_layout.addRow("", test_1688_btn)

        layout.addWidget(api_1688_group)

        # 淘宝API设置
        api_taobao_group = QGroupBox("淘宝 API配置")
        api_taobao_group.setStyleSheet(self.get_group_style())
        api_taobao_layout = QFormLayout(api_taobao_group)

        # App Key
        self.api_taobao_key_edit = QLineEdit()
        self.api_taobao_key_edit.setStyleSheet(self.get_input_style())
        self.api_taobao_key_edit.setPlaceholderText("请输入淘宝 App Key")
        api_taobao_layout.addRow("App Key:", self.api_taobao_key_edit)

        # App Secret
        self.api_taobao_secret_edit = QLineEdit()
        self.api_taobao_secret_edit.setStyleSheet(self.get_input_style())
        self.api_taobao_secret_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_taobao_secret_edit.setPlaceholderText("请输入淘宝 App Secret")
        api_taobao_layout.addRow("App Secret:", self.api_taobao_secret_edit)

        # 测试连接按钮
        test_taobao_btn = QPushButton("测试连接")
        test_taobao_btn.setStyleSheet(self.get_button_style())
        test_taobao_btn.clicked.connect(lambda: self.test_api_connection("taobao"))
        api_taobao_layout.addRow("", test_taobao_btn)

        layout.addWidget(api_taobao_group)

        # API通用设置
        api_general_group = QGroupBox("API通用设置")
        api_general_group.setStyleSheet(self.get_group_style())
        api_general_layout = QFormLayout(api_general_group)

        # 请求超时
        self.api_timeout_spin = QSpinBox()
        self.api_timeout_spin.setRange(5, 120)
        self.api_timeout_spin.setSuffix(" 秒")
        self.api_timeout_spin.setStyleSheet(self.get_input_style())
        api_general_layout.addRow("请求超时:", self.api_timeout_spin)

        # 重试次数
        self.api_retry_spin = QSpinBox()
        self.api_retry_spin.setRange(0, 10)
        self.api_retry_spin.setStyleSheet(self.get_input_style())
        api_general_layout.addRow("重试次数:", self.api_retry_spin)

        # 限流设置
        self.api_rate_limit_spin = QSpinBox()
        self.api_rate_limit_spin.setRange(1, 1000)
        self.api_rate_limit_spin.setSuffix(" 次/分钟")
        self.api_rate_limit_spin.setStyleSheet(self.get_input_style())
        api_general_layout.addRow("请求限制:", self.api_rate_limit_spin)

        layout.addWidget(api_general_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area

    def create_monitor_settings(self):
        """创建系统监控标签页"""
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)

        # 性能监控
        perf_group = QGroupBox("性能监控")
        perf_group.setStyleSheet(self.get_group_style())
        perf_layout = QFormLayout(perf_group)

        # 启用性能监控
        self.enable_monitor_cb = QCheckBox("启用性能监控")
        self.enable_monitor_cb.setStyleSheet("color: white;")
        perf_layout.addRow("", self.enable_monitor_cb)

        # 监控间隔
        self.monitor_interval_spin = QSpinBox()
        self.monitor_interval_spin.setRange(1, 60)
        self.monitor_interval_spin.setSuffix(" 秒")
        self.monitor_interval_spin.setStyleSheet(self.get_input_style())
        perf_layout.addRow("监控间隔:", self.monitor_interval_spin)

        # 内存警告阈值
        self.memory_threshold_spin = QSpinBox()
        self.memory_threshold_spin.setRange(50, 95)
        self.memory_threshold_spin.setSuffix(" %")
        self.memory_threshold_spin.setStyleSheet(self.get_input_style())
        perf_layout.addRow("内存警告阈值:", self.memory_threshold_spin)

        layout.addWidget(perf_group)

        # 日志设置
        log_group = QGroupBox("日志设置")
        log_group.setStyleSheet(self.get_group_style())
        log_layout = QFormLayout(log_group)

        # 日志级别
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.setStyleSheet(self.get_input_style())
        log_layout.addRow("日志级别:", self.log_level_combo)

        # 日志文件大小限制
        self.log_size_spin = QSpinBox()
        self.log_size_spin.setRange(1, 1000)
        self.log_size_spin.setSuffix(" MB")
        self.log_size_spin.setStyleSheet(self.get_input_style())
        log_layout.addRow("单文件大小限制:", self.log_size_spin)

        # 日志文件数量
        self.log_count_spin = QSpinBox()
        self.log_count_spin.setRange(1, 50)
        self.log_count_spin.setStyleSheet(self.get_input_style())
        log_layout.addRow("保留文件数量:", self.log_count_spin)

        layout.addWidget(log_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        return scroll_area

    def create_button_section(self):
        """创建底部按钮区域"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: #2b2b2b;
                border-top: 1px solid #555;
                padding: 10px;
            }
        """
        )

        layout = QHBoxLayout(frame)

        # 重置按钮
        reset_btn = QPushButton("🔄 重置默认")
        reset_btn.setStyleSheet(self.get_button_style())
        reset_btn.clicked.connect(self.reset_to_defaults)
        layout.addWidget(reset_btn)

        # 导入导出按钮
        import_btn = QPushButton("📥 导入设置")
        import_btn.setStyleSheet(self.get_button_style())
        import_btn.clicked.connect(self.import_settings)
        layout.addWidget(import_btn)

        export_btn = QPushButton("📤 导出设置")
        export_btn.setStyleSheet(self.get_button_style())
        export_btn.clicked.connect(self.export_settings)
        layout.addWidget(export_btn)

        layout.addStretch()

        # 取消和保存按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setStyleSheet(self.get_button_style())
        cancel_btn.clicked.connect(self.cancel_changes)
        layout.addWidget(cancel_btn)

        save_btn = QPushButton("💾 保存设置")
        save_btn.setStyleSheet(self.get_button_style("primary"))
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)

        return frame

    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                color: white;
                font-size: 14px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #1e1e1e;
            }
        """

    def get_input_style(self):
        """获取输入控件样式"""
        return """
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #333;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
                color: white;
                font-size: 13px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #4CAF50;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #555;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
        """

    def get_button_style(self, style_type="normal"):
        """获取按钮样式"""
        if style_type == "primary":
            return """
                QPushButton {
                    background-color: #4CAF50;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    color: white;
                    font-weight: bold;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3d8b40;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    border-radius: 6px;
                    padding: 10px 20px;
                    color: white;
                    font-weight: bold;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #353535;
                }
            """

    # 功能方法
    def load_settings(self):
        """加载设置"""
        try:
            # 加载基础设置
            self.app_name_edit.setText(
                self.config_manager.get("app_name", "电商管理系统")
            )
            self.language_combo.setCurrentText(
                self.config_manager.get("language", "简体中文")
            )
            self.autosave_spin.setValue(self.config_manager.get("autosave_interval", 5))
            self.check_update_cb.setChecked(
                self.config_manager.get("check_update", True)
            )

            # 加载数据设置
            self.currency_combo.setCurrentText(
                self.config_manager.get("currency", "CNY (人民币)")
            )
            self.decimal_spin.setValue(self.config_manager.get("decimal_places", 2))
            self.retention_spin.setValue(
                self.config_manager.get("log_retention_days", 90)
            )

            # 加载性能设置
            self.page_size_spin.setValue(self.config_manager.get("page_size", 50))
            self.cache_size_spin.setValue(self.config_manager.get("cache_size", 100))
            self.multithread_cb.setChecked(
                self.config_manager.get("enable_multithread", True)
            )

            # 加载界面设置
            self.theme_combo.setCurrentText(
                self.config_manager.get("theme", "暗黑主题")
            )
            self.font_size_spin.setValue(self.config_manager.get("font_size", 12))
            self.scale_slider.setValue(self.config_manager.get("ui_scale", 100))
            self.remember_pos_cb.setChecked(
                self.config_manager.get("remember_window_pos", True)
            )
            self.minimize_tray_cb.setChecked(
                self.config_manager.get("minimize_to_tray", False)
            )
            self.start_maximized_cb.setChecked(
                self.config_manager.get("start_maximized", False)
            )

            # 加载数据库设置
            self.db_path_edit.setText(
                self.config_manager.get("database_path", "data/inventory.db")
            )
            self.pool_size_spin.setValue(self.config_manager.get("db_pool_size", 5))
            self.query_timeout_spin.setValue(
                self.config_manager.get("query_timeout", 30)
            )
            self.auto_backup_cb.setChecked(self.config_manager.get("auto_backup", True))
            self.backup_interval_spin.setValue(
                self.config_manager.get("backup_interval", 24)
            )
            self.backup_keep_spin.setValue(
                self.config_manager.get("backup_keep_count", 7)
            )

            # 加载API设置
            self.api_1688_key_edit.setText(self.config_manager.get("api_1688_key", ""))
            self.api_1688_secret_edit.setText(
                self.config_manager.get("api_1688_secret", "")
            )
            self.api_taobao_key_edit.setText(
                self.config_manager.get("api_taobao_key", "")
            )
            self.api_taobao_secret_edit.setText(
                self.config_manager.get("api_taobao_secret", "")
            )
            self.api_timeout_spin.setValue(self.config_manager.get("api_timeout", 30))
            self.api_retry_spin.setValue(self.config_manager.get("api_retry_count", 3))
            self.api_rate_limit_spin.setValue(
                self.config_manager.get("api_rate_limit", 100)
            )

            # 加载监控设置
            self.enable_monitor_cb.setChecked(
                self.config_manager.get("enable_monitor", True)
            )
            self.monitor_interval_spin.setValue(
                self.config_manager.get("monitor_interval", 10)
            )
            self.memory_threshold_spin.setValue(
                self.config_manager.get("memory_threshold", 80)
            )
            self.log_level_combo.setCurrentText(
                self.config_manager.get("log_level", "INFO")
            )
            self.log_size_spin.setValue(self.config_manager.get("log_file_size", 10))
            self.log_count_spin.setValue(self.config_manager.get("log_file_count", 5))

            self.logger.info("设置加载完成")

        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            QMessageBox.warning(self, "警告", f"加载设置失败:\n{e}")

    def save_settings(self):
        """保存设置"""
        try:
            # 保存基础设置
            self.config_manager.set("app_name", self.app_name_edit.text())
            self.config_manager.set("language", self.language_combo.currentText())
            self.config_manager.set("autosave_interval", self.autosave_spin.value())
            self.config_manager.set("check_update", self.check_update_cb.isChecked())

            # 保存数据设置
            self.config_manager.set("currency", self.currency_combo.currentText())
            self.config_manager.set("decimal_places", self.decimal_spin.value())
            self.config_manager.set("log_retention_days", self.retention_spin.value())

            # 保存性能设置
            self.config_manager.set("page_size", self.page_size_spin.value())
            self.config_manager.set("cache_size", self.cache_size_spin.value())
            self.config_manager.set(
                "enable_multithread", self.multithread_cb.isChecked()
            )

            # 保存界面设置
            self.config_manager.set("theme", self.theme_combo.currentText())
            self.config_manager.set("font_size", self.font_size_spin.value())
            self.config_manager.set("ui_scale", self.scale_slider.value())
            self.config_manager.set(
                "remember_window_pos", self.remember_pos_cb.isChecked()
            )
            self.config_manager.set(
                "minimize_to_tray", self.minimize_tray_cb.isChecked()
            )
            self.config_manager.set(
                "start_maximized", self.start_maximized_cb.isChecked()
            )

            # 保存数据库设置
            self.config_manager.set("database_path", self.db_path_edit.text())
            self.config_manager.set("db_pool_size", self.pool_size_spin.value())
            self.config_manager.set("query_timeout", self.query_timeout_spin.value())
            self.config_manager.set("auto_backup", self.auto_backup_cb.isChecked())
            self.config_manager.set(
                "backup_interval", self.backup_interval_spin.value()
            )
            self.config_manager.set("backup_keep_count", self.backup_keep_spin.value())

            # 保存API设置
            self.config_manager.set("api_1688_key", self.api_1688_key_edit.text())
            self.config_manager.set("api_1688_secret", self.api_1688_secret_edit.text())
            self.config_manager.set("api_taobao_key", self.api_taobao_key_edit.text())
            self.config_manager.set(
                "api_taobao_secret", self.api_taobao_secret_edit.text()
            )
            self.config_manager.set("api_timeout", self.api_timeout_spin.value())
            self.config_manager.set("api_retry_count", self.api_retry_spin.value())
            self.config_manager.set("api_rate_limit", self.api_rate_limit_spin.value())

            # 保存监控设置
            self.config_manager.set(
                "enable_monitor", self.enable_monitor_cb.isChecked()
            )
            self.config_manager.set(
                "monitor_interval", self.monitor_interval_spin.value()
            )
            self.config_manager.set(
                "memory_threshold", self.memory_threshold_spin.value()
            )
            self.config_manager.set("log_level", self.log_level_combo.currentText())
            self.config_manager.set("log_file_size", self.log_size_spin.value())
            self.config_manager.set("log_file_count", self.log_count_spin.value())

            QMessageBox.information(
                self, "成功", "设置已保存！\n部分设置需要重启应用后生效。"
            )
            self.logger.info("设置保存完成")

        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败:\n{e}")

    def cancel_changes(self):
        """取消更改"""
        reply = QMessageBox.question(
            self,
            "确认",
            "确定要取消所有更改吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.load_settings()  # 重新加载设置

    def reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self,
            "确认",
            "确定要重置为默认设置吗？\n这将清除所有自定义配置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.config_manager.reset_to_defaults()
                self.load_settings()
                QMessageBox.information(self, "成功", "已重置为默认设置！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"重置设置失败:\n{e}")

    def import_settings(self):
        """导入设置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入设置", "", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                self.config_manager.import_from_file(file_path)
                self.load_settings()
                QMessageBox.information(self, "成功", "设置导入成功！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入设置失败:\n{e}")

    def export_settings(self):
        """导出设置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出设置", "settings.json", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                self.config_manager.export_to_file(file_path)
                QMessageBox.information(self, "成功", "设置导出成功！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出设置失败:\n{e}")

    def browse_database_path(self):
        """浏览数据库路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据库文件", "", "SQLite数据库 (*.db);;所有文件 (*)"
        )

        if file_path:
            self.db_path_edit.setText(file_path)

    def test_api_connection(self, platform: str):
        """测试API连接"""
        QMessageBox.information(
            self, "提示", f"{platform} API连接测试功能正在开发中..."
        )
