#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商数据模型

用于代发管理模块的供应商信息管理。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import json


@dataclass
class SupplierProduct:
    """供应商商品"""
    
    supplier_product_id: str  # 供应商平台的商品ID
    product_name: str
    product_code: Optional[str] = None
    category: Optional[str] = None
    supplier_price: float = 0.0
    min_order_quantity: int = 1
    available_stock: int = 0
    product_url: Optional[str] = None
    image_url: Optional[str] = None
    description: Optional[str] = None
    shipping_info: Dict[str, Any] = field(default_factory=dict)
    last_sync: Optional[datetime] = None
    status: str = "available"  # available, unavailable, discontinued
    
    def __post_init__(self):
        if self.last_sync is None:
            self.last_sync = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'supplier_product_id': self.supplier_product_id,
            'product_name': self.product_name,
            'product_code': self.product_code,
            'category': self.category,
            'supplier_price': self.supplier_price,
            'min_order_quantity': self.min_order_quantity,
            'available_stock': self.available_stock,
            'product_url': self.product_url,
            'image_url': self.image_url,
            'description': self.description,
            'shipping_info': json.dumps(self.shipping_info),
            'last_sync': self.last_sync.isoformat() if self.last_sync else None,
            'status': self.status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SupplierProduct':
        if isinstance(data.get('shipping_info'), str):
            data['shipping_info'] = json.loads(data['shipping_info']) if data['shipping_info'] else {}
        
        if isinstance(data.get('last_sync'), str):
            data['last_sync'] = datetime.fromisoformat(data['last_sync'])
        
        return cls(**data)


@dataclass
class Supplier:
    """
    供应商数据模型
    
    管理供应商信息，支持多平台供应商、商品管理、信用评级等功能
    """
    
    # 基础信息
    supplier_id: Optional[str] = None
    supplier_name: str = ""
    platform_id: Optional[str] = None  # 关联平台
    supplier_code: Optional[str] = None  # 平台上的供应商编码
    
    # 联系信息
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    address: Optional[str] = None
    
    # API和认证信息
    api_credentials: Dict[str, Any] = field(default_factory=dict)
    
    # 评级和状态
    credit_rating: int = 5  # 1-10分
    cooperation_status: str = "active"  # active, inactive, blacklist
    
    # 商品管理
    products: List[SupplierProduct] = field(default_factory=list)
    
    # 统计信息
    total_products: int = 0
    total_orders: int = 0
    total_amount: float = 0.0
    success_rate: float = 100.0  # 成功率(%)
    
    # 备注和元数据
    notes: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.supplier_id is None:
            self.supplier_id = self.generate_supplier_id()
        
        if self.created_at is None:
            self.created_at = datetime.now()
        
        self.updated_at = datetime.now()
        
        # 更新统计信息
        self.update_statistics()
    
    def generate_supplier_id(self) -> str:
        """生成供应商ID"""
        return f"S{uuid.uuid4().hex[:8].upper()}"
    
    def add_product(self, supplier_product: SupplierProduct) -> bool:
        """添加供应商商品"""
        # 检查商品是否已存在
        for existing_product in self.products:
            if existing_product.supplier_product_id == supplier_product.supplier_product_id:
                # 更新现有商品信息
                existing_product.__dict__.update(supplier_product.__dict__)
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        
        # 添加新商品
        self.products.append(supplier_product)
        self.update_statistics()
        self.updated_at = datetime.now()
        return True
    
    def remove_product(self, supplier_product_id: str) -> bool:
        """移除供应商商品"""
        for i, product in enumerate(self.products):
            if product.supplier_product_id == supplier_product_id:
                del self.products[i]
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_product(self, supplier_product_id: str) -> Optional[SupplierProduct]:
        """获取供应商商品"""
        for product in self.products:
            if product.supplier_product_id == supplier_product_id:
                return product
        return None
    
    def update_product_stock(self, supplier_product_id: str, new_stock: int) -> bool:
        """更新商品库存"""
        product = self.get_product(supplier_product_id)
        if product:
            product.available_stock = new_stock
            product.last_sync = datetime.now()
            self.updated_at = datetime.now()
            return True
        return False
    
    def update_product_price(self, supplier_product_id: str, new_price: float) -> bool:
        """更新商品价格"""
        product = self.get_product(supplier_product_id)
        if product:
            product.supplier_price = new_price
            product.last_sync = datetime.now()
            self.updated_at = datetime.now()
            return True
        return False
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_products = len(self.products)
        # 其他统计信息需要从订单数据中计算
    
    def set_credit_rating(self, rating: int, reason: str = ""):
        """设置信用评级"""
        if not 1 <= rating <= 10:
            raise ValueError("信用评级必须在1-10之间")
        
        old_rating = self.credit_rating
        self.credit_rating = rating
        self.updated_at = datetime.now()
        
        # 记录评级变化
        if 'rating_changes' not in self.metadata:
            self.metadata['rating_changes'] = []
        
        self.metadata['rating_changes'].append({
            'timestamp': datetime.now().isoformat(),
            'old_rating': old_rating,
            'new_rating': rating,
            'reason': reason
        })
    
    def set_cooperation_status(self, status: str, reason: str = ""):
        """设置合作状态"""
        if status not in ["active", "inactive", "blacklist"]:
            raise ValueError("无效的合作状态")
        
        old_status = self.cooperation_status
        self.cooperation_status = status
        self.updated_at = datetime.now()
        
        # 记录状态变化
        if 'status_changes' not in self.metadata:
            self.metadata['status_changes'] = []
        
        self.metadata['status_changes'].append({
            'timestamp': datetime.now().isoformat(),
            'old_status': old_status,
            'new_status': status,
            'reason': reason
        })
    
    def add_api_credential(self, key: str, value: str, encrypted: bool = True):
        """添加API凭证"""
        self.api_credentials[key] = {
            'value': value,
            'encrypted': encrypted,
            'updated_at': datetime.now().isoformat()
        }
        self.updated_at = datetime.now()
    
    def get_api_credential(self, key: str) -> Optional[str]:
        """获取API凭证"""
        credential = self.api_credentials.get(key)
        if credential:
            return credential['value']
        return None
    
    def is_active(self) -> bool:
        """检查供应商是否活跃"""
        return self.cooperation_status == "active"
    
    def is_blacklisted(self) -> bool:
        """检查供应商是否被拉黑"""
        return self.cooperation_status == "blacklist"
    
    def get_available_products(self) -> List[SupplierProduct]:
        """获取可用商品列表"""
        return [p for p in self.products if p.status == "available" and p.available_stock > 0]
    
    def search_products(self, keyword: str) -> List[SupplierProduct]:
        """搜索商品"""
        keyword = keyword.lower()
        results = []
        
        for product in self.products:
            if (keyword in product.product_name.lower() or
                (product.product_code and keyword in product.product_code.lower()) or
                (product.category and keyword in product.category.lower())):
                results.append(product)
        
        return results
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'supplier_id': self.supplier_id,
            'supplier_name': self.supplier_name,
            'platform_id': self.platform_id,
            'supplier_code': self.supplier_code,
            'contact_person': self.contact_person,
            'contact_phone': self.contact_phone,
            'contact_email': self.contact_email,
            'address': self.address,
            'api_credentials': json.dumps(self.api_credentials),
            'credit_rating': self.credit_rating,
            'cooperation_status': self.cooperation_status,
            'products': json.dumps([p.to_dict() for p in self.products]),
            'total_products': self.total_products,
            'total_orders': self.total_orders,
            'total_amount': self.total_amount,
            'success_rate': self.success_rate,
            'notes': self.notes,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Supplier':
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get('api_credentials'), str):
            data['api_credentials'] = json.loads(data['api_credentials']) if data['api_credentials'] else {}
        
        if isinstance(data.get('products'), str):
            products_data = json.loads(data['products']) if data['products'] else []
            data['products'] = [SupplierProduct.from_dict(pd) for pd in products_data]
        
        if isinstance(data.get('metadata'), str):
            data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        # 处理时间字段
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    @classmethod
    def from_db_row(cls, row, columns: List[str]) -> Optional['Supplier']:
        """从数据库行创建对象"""
        if not row:
            return None
        
        data = dict(zip(columns, row))
        return cls.from_dict(data)
    
    def validate(self) -> bool:
        """验证数据有效性"""
        if not self.supplier_name:
            raise ValueError("供应商名称不能为空")
        
        if not 1 <= self.credit_rating <= 10:
            raise ValueError("信用评级必须在1-10之间")
        
        if self.cooperation_status not in ["active", "inactive", "blacklist"]:
            raise ValueError("无效的合作状态")
        
        if self.contact_email and "@" not in self.contact_email:
            raise ValueError("邮箱格式无效")
        
        return True
    
    def __str__(self) -> str:
        return f"{self.supplier_id} - {self.supplier_name} ({self.cooperation_status})"
    
    def __repr__(self) -> str:
        return f"Supplier(supplier_id='{self.supplier_id}', name='{self.supplier_name}', status='{self.cooperation_status}')"
