#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电商管理系统整合版 v2.0.0 - 主程序入口

整合三个项目的功能：
- Inventory_Management_v1.7.3 (库存管理)
- Agent_Order_Management_v1.0.1 (代发管理)  
- Ecom_Supply_Comparison (供应链对比)

Author: AI Assistant
Date: 2024-12
Version: 2.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt, QDir
    from PyQt6.QtGui import QIcon
except ImportError as e:
    print(f"错误: 无法导入PyQt6库: {e}")
    print("请运行: pip install PyQt6")
    sys.exit(1)

# 导入项目模块
try:
    from core.config import Config
    from core.database import DatabaseManager
    from gui.main_window import MainWindow
    from utils.logger import setup_logger
    from utils.error_handler import GlobalExceptionHandler
except ImportError as e:
    print(f"错误: 无法导入项目模块: {e}")
    print("请确保所有必要的文件都存在")
    sys.exit(1)


class EcommerceManagementApp:
    """电商管理系统应用程序主类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.config = None
        self.db_manager = None
        self.logger = None
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 1. 创建QApplication实例
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("电商管理系统")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("EcommerceMgmt")
            
            # 2. 设置应用程序图标
            icon_path = project_root / "resources" / "icons" / "app_icon.ico"
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))
            
            # 3. 初始化配置
            self.config = Config()
            
            # 4. 设置日志系统
            self.logger = setup_logger(
                log_level=self.config.get('logging.level', 'INFO'),
                log_file=self.config.get('logging.file', 'logs/app.log')
            )
            self.logger.info("应用程序启动中...")
            
            # 5. 设置全局异常处理器
            exception_handler = GlobalExceptionHandler(self.logger)
            sys.excepthook = exception_handler.handle_exception
            
            # 6. 初始化数据库
            self.db_manager = DatabaseManager(
                db_path=self.config.get('database.path', 'data/ecommerce.db')
            )
            
            if not self.db_manager.initialize():
                raise Exception("数据库初始化失败")
            
            # 7. 创建主窗口
            self.main_window = MainWindow(
                config=self.config,
                db_manager=self.db_manager,
                logger=self.logger
            )
            
            # 8. 应用主题
            self.apply_theme()
            
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            error_msg = f"应用程序初始化失败: {str(e)}"
            print(error_msg)
            if self.logger:
                self.logger.error(error_msg)
            
            # 显示错误对话框
            if self.app:
                QMessageBox.critical(
                    None,
                    "初始化错误",
                    f"应用程序初始化失败:\n\n{str(e)}\n\n请检查配置和依赖项。"
                )
            return False
    
    def apply_theme(self):
        """应用主题样式"""
        try:
            theme_name = self.config.get('ui.theme', 'dark')
            theme_file = project_root / "resources" / "themes" / f"{theme_name}.qss"
            
            if theme_file.exists():
                with open(theme_file, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
                self.app.setStyleSheet(stylesheet)
                self.logger.info(f"已应用主题: {theme_name}")
            else:
                self.logger.warning(f"主题文件不存在: {theme_file}")
                
        except Exception as e:
            self.logger.error(f"应用主题失败: {str(e)}")
    
    def run(self):
        """运行应用程序"""
        if not self.initialize():
            return 1
        
        try:
            # 显示主窗口
            self.main_window.show()
            
            # 检查是否是首次运行
            if self.config.get('app.first_run', True):
                self.show_welcome_dialog()
                self.config.set('app.first_run', False)
                self.config.save()
            
            self.logger.info("应用程序开始运行")
            
            # 进入事件循环
            return self.app.exec()
            
        except Exception as e:
            error_msg = f"应用程序运行时错误: {str(e)}"
            self.logger.error(error_msg)
            
            QMessageBox.critical(
                self.main_window,
                "运行时错误",
                f"应用程序遇到错误:\n\n{str(e)}"
            )
            return 1
        
        finally:
            self.cleanup()
    
    def show_welcome_dialog(self):
        """显示欢迎对话框"""
        try:
            welcome_msg = """
欢迎使用电商管理系统整合版 v2.0.0！

本系统整合了以下功能模块：
• 📦 库存管理 - 商品、批次、库存跟踪
• 🚚 代发管理 - 供应商、订单、利润分析  
• 📊 供应链对比 - 多平台价格对比分析
• 📈 数据分析 - 综合报表和趋势分析

首次使用建议：
1. 先在"系统设置"中配置基本参数
2. 添加供应商和平台信息
3. 导入现有商品数据
4. 开始使用各项功能

如需帮助，请查看"帮助"菜单中的用户手册。
            """
            
            QMessageBox.information(
                self.main_window,
                "欢迎使用",
                welcome_msg.strip()
            )
            
        except Exception as e:
            self.logger.error(f"显示欢迎对话框失败: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.db_manager:
                self.db_manager.close()
                
            if self.logger:
                self.logger.info("应用程序正常退出")
                
        except Exception as e:
            print(f"清理资源时出错: {str(e)}")


def check_environment():
    """检查运行环境"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    # 检查必要目录
    required_dirs = ['core', 'gui', 'utils', 'resources', 'data', 'logs']
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"创建目录: {dir_path}")
    
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("电商管理系统整合版 v2.0.0")
    print("=" * 60)
    
    # 检查运行环境
    if not check_environment():
        return 1
    
    # 创建并运行应用程序
    app = EcommerceManagementApp()
    return app.run()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"程序异常退出: {str(e)}")
        sys.exit(1)
