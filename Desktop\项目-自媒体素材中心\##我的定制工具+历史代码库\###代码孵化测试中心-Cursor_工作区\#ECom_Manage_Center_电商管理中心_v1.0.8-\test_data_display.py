#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据分析显示

验证数据分析模块是否能正确显示数据。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_sample_data():
    """测试示例数据"""
    print("🧪 测试数据分析示例数据...")
    
    try:
        from gui.modules.analytics.analytics_widget import AnalyticsWidget
        from core.database import DatabaseManager
        
        # 创建数据库管理器
        db_manager = DatabaseManager("data/test.db")
        db_manager.initialize()
        
        # 创建数据分析组件（不需要QApplication）
        analytics_widget = AnalyticsWidget(db_manager)
        
        # 直接测试示例数据
        sample_data = analytics_widget.get_sample_data()
        
        print("✅ 示例数据获取成功:")
        print(f"   📦 商品总数: {sample_data['total_products']}")
        print(f"   💰 库存价值: ¥{sample_data['total_inventory_value']:,.2f}")
        print(f"   💸 总成本: ¥{sample_data['total_cost']:,.2f}")
        print(f"   💎 预期利润: ¥{sample_data['total_expected_profit']:,.2f}")
        print(f"   📈 平均利润率: {sample_data['avg_profit_margin']:.1f}%")
        print(f"   ⚠️ 低库存预警: {sample_data['low_stock_count']} 个")
        print(f"   🏪 供应商数量: {sample_data['total_suppliers']}")
        print(f"   📋 活跃批次: {sample_data['active_batches']}")
        
        print(f"\n📊 商品分类统计:")
        for category, data in sample_data['categories'].items():
            profit_margin = (data['total_profit'] / data['total_cost'] * 100) if data['total_cost'] > 0 else 0
            print(f"   - {category}: {data['count']} 个商品, 利润率 {profit_margin:.1f}%")
        
        # 测试数据计算
        analytics_data = analytics_widget.calculate_analytics()
        print(f"\n✅ 数据计算成功，返回 {len(analytics_data)} 个指标")
        
        # 关闭数据库
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_formatting():
    """测试数据格式化"""
    print("\n🎨 测试数据格式化...")
    
    try:
        # 测试数据格式化
        test_data = {
            'total_products': 25,
            'total_inventory_value': 15680.50,
            'total_cost': 12340.00,
            'avg_profit_margin': 27.1,
        }
        
        print("✅ 数据格式化测试:")
        print(f"   商品数量: {test_data['total_products']} 个")
        print(f"   库存价值: ¥{test_data['total_inventory_value']:,.2f}")
        print(f"   总成本: ¥{test_data['total_cost']:,.2f}")
        print(f"   利润率: {test_data['avg_profit_margin']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式化测试失败: {e}")
        return False

def test_module_structure():
    """测试模块结构"""
    print("\n🏗️ 测试模块结构...")
    
    try:
        from gui.modules.analytics import AnalyticsWidget
        from gui.modules.analytics.analytics_widget import AnalyticsWidget as AnalyticsMainWidget
        
        print("✅ 模块结构正确:")
        print("   - gui.modules.analytics (主模块)")
        print("   - gui.modules.analytics.analytics_widget (具体实现)")
        
        # 检查方法是否存在
        methods = [
            'calculate_analytics',
            'get_sample_data',
            'update_dashboard',
            'update_category_analysis',
            'update_trend_analysis'
        ]
        
        for method in methods:
            if hasattr(AnalyticsMainWidget, method):
                print(f"   ✅ {method} 方法存在")
            else:
                print(f"   ❌ {method} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块结构测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 数据分析显示测试")
    print("=" * 40)
    
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("模块结构", test_module_structure),
        ("数据格式化", test_data_formatting),
        ("示例数据", test_sample_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 40)
    print("测试结果汇总")
    print("=" * 40)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("\n📋 数据分析模块状态:")
        print("✅ 模块结构正确")
        print("✅ 示例数据可用")
        print("✅ 数据格式化正常")
        print("✅ 界面应该能正常显示内容")
        print("\n💡 如果界面仍显示空白，请检查:")
        print("1. 是否正确切换到数据分析标签页")
        print("2. 窗口大小是否足够大 (建议1600x1300)")
        print("3. 是否有JavaScript或渲染错误")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
    
    sys.exit(exit_code)
