#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析模块界面

数据分析和报表功能的主界面。
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from .analytics.analytics_widget_new import AnalyticsWidget as AnalyticsMainWidget
from .development_widget import DevelopmentWidget


class AnalyticsWidget(QWidget):
    """数据分析界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """
        )

        # 数据分析标签页 - 完整功能
        analytics_widget = AnalyticsMainWidget(self.db_manager, self)
        tab_widget.addTab(analytics_widget, "📊 数据分析")

        # 销售分析标签页 - 开发中
        sales_info = {
            "name": "销售分析功能",
            "icon": "💰",
            "description": "Sales Analysis & Performance",
            "progress": 30,
            "eta": "2-3周",
            "features": [
                "📈 销售趋势分析",
                "   • 日/周/月销售趋势",
                "   • 同比环比分析",
                "   • 季节性销售模式",
                "   • 销售预测模型",
                "",
                "🏆 商品销售排行",
                "   • 热销商品排行",
                "   • 滞销商品识别",
                "   • 商品生命周期分析",
                "   • 库存周转率",
                "",
                "👥 客户分析",
                "   • 客户购买行为",
                "   • 客户价值分析",
                "   • 复购率统计",
                "   • 客户细分",
            ],
            "phases": [
                ("第一阶段", "基础销售统计", "⏳ 计划中", "#666"),
                ("第二阶段", "趋势分析算法", "⏳ 计划中", "#666"),
                ("第三阶段", "客户行为分析", "⏳ 计划中", "#666"),
            ],
        }
        sales_widget = DevelopmentWidget(sales_info, self)
        tab_widget.addTab(sales_widget, "💰 销售分析")

        # 预测分析标签页 - 开发中
        forecast_info = {
            "name": "预测分析功能",
            "icon": "🔮",
            "description": "Predictive Analytics & Forecasting",
            "progress": 10,
            "eta": "4-6周",
            "features": [
                "🔮 销量预测",
                "   • 基于历史数据的销量预测",
                "   • 季节性调整算法",
                "   • 多变量预测模型",
                "   • 预测准确度评估",
                "",
                "💹 价格趋势预测",
                "   • 市场价格走势预测",
                "   • 成本变化预测",
                "   • 最优定价建议",
                "   • 竞争对手价格分析",
                "",
                "⚠️ 风险评估",
                "   • 库存风险评估",
                "   • 供应链风险分析",
                "   • 市场风险预警",
                "   • 财务风险控制",
            ],
            "phases": [
                ("第一阶段", "基础预测模型", "⏳ 计划中", "#666"),
                ("第二阶段", "高级算法集成", "⏳ 计划中", "#666"),
                ("第三阶段", "风险评估系统", "⏳ 计划中", "#666"),
            ],
        }
        forecast_widget = DevelopmentWidget(forecast_info, self)
        tab_widget.addTab(forecast_widget, "🔮 预测分析")

        layout.addWidget(tab_widget)
