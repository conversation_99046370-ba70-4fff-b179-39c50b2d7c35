#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存操作对话框

用于入库和出库操作的对话框界面。
"""

import logging
from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QSpinBox, QPushButton, QLabel,
    QGroupBox, QComboBox, QMessageBox, QDateTimeEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont

from core.models.product import Product
from utils.logger import LoggerMixin


class StockDialog(QDialog, LoggerMixin):
    """库存操作对话框"""
    
    # 信号定义
    stock_updated = pyqtSignal(str, int, str)  # product_id, quantity_change, reason
    
    def __init__(self, product: Product, operation_type: str = "in", parent=None):
        """
        初始化库存操作对话框
        
        Args:
            product: 商品对象
            operation_type: 操作类型 ("in" 入库, "out" 出库)
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.product = product
        self.operation_type = operation_type
        self.is_stock_in = operation_type == "in"
        
        # 初始化界面
        self.init_ui()
        
        self.logger.info(f"库存操作对话框初始化完成 - {operation_type}")
    
    def init_ui(self):
        """初始化用户界面"""
        operation_name = "入库" if self.is_stock_in else "出库"
        self.setWindowTitle(f"{operation_name}操作 - {self.product.name}")
        self.setModal(True)
        self.resize(450, 500)
        
        layout = QVBoxLayout(self)
        
        # 商品信息组
        product_group = QGroupBox("商品信息")
        product_layout = QFormLayout(product_group)
        
        # 商品名称
        name_label = QLabel(self.product.name)
        name_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        product_layout.addRow("商品名称:", name_label)
        
        # 商品编码
        code_label = QLabel(self.product.code or "无")
        product_layout.addRow("商品编码:", code_label)
        
        # 当前库存
        current_stock_label = QLabel(f"{self.product.quantity} {self.product.unit}")
        current_stock_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        if self.product.quantity <= 10:
            current_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #F44336;")
        elif self.product.quantity <= 50:
            current_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #FF9800;")
        else:
            current_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #4CAF50;")
        product_layout.addRow("当前库存:", current_stock_label)
        
        layout.addWidget(product_group)
        
        # 操作信息组
        operation_group = QGroupBox(f"{operation_name}信息")
        operation_layout = QFormLayout(operation_group)
        
        # 操作数量
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 999999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.setSuffix(f" {self.product.unit}")
        self.quantity_spin.valueChanged.connect(self.update_preview)
        operation_layout.addRow(f"{operation_name}数量:", self.quantity_spin)
        
        # 操作原因
        self.reason_combo = QComboBox()
        self.reason_combo.setEditable(True)
        if self.is_stock_in:
            self.reason_combo.addItems([
                "采购入库",
                "退货入库", 
                "调拨入库",
                "盘点调整",
                "生产入库",
                "其他入库"
            ])
        else:
            self.reason_combo.addItems([
                "销售出库",
                "调拨出库",
                "损耗出库",
                "盘点调整",
                "退货出库",
                "其他出库"
            ])
        operation_layout.addRow("操作原因:", self.reason_combo)
        
        # 操作时间
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setDateTime(QDateTime.currentDateTime())
        self.datetime_edit.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        operation_layout.addRow("操作时间:", self.datetime_edit)
        
        # 操作员
        self.operator_edit = QLineEdit()
        self.operator_edit.setPlaceholderText("操作员姓名（可选）")
        operation_layout.addRow("操作员:", self.operator_edit)
        
        layout.addWidget(operation_group)
        
        # 预览信息组
        preview_group = QGroupBox("操作预览")
        preview_layout = QFormLayout(preview_group)
        
        # 操作后库存
        self.new_stock_label = QLabel()
        self.new_stock_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        preview_layout.addRow("操作后库存:", self.new_stock_label)
        
        # 库存变化
        self.change_label = QLabel()
        self.change_label.setStyleSheet("font-weight: bold;")
        preview_layout.addRow("库存变化:", self.change_label)
        
        layout.addWidget(preview_group)
        
        # 备注信息组
        notes_group = QGroupBox("备注信息")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("备注信息（可选）")
        notes_layout.addWidget(self.notes_edit)
        
        layout.addWidget(notes_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.confirm_btn = QPushButton(f"✅ 确认{operation_name}")
        self.confirm_btn.clicked.connect(self.confirm_operation)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.confirm_btn)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化预览
        self.update_preview()
    
    def update_preview(self):
        """更新操作预览"""
        try:
            quantity = self.quantity_spin.value()
            current_stock = self.product.quantity
            
            if self.is_stock_in:
                new_stock = current_stock + quantity
                change_text = f"+{quantity} {self.product.unit}"
                self.change_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
            else:
                new_stock = current_stock - quantity
                change_text = f"-{quantity} {self.product.unit}"
                self.change_label.setStyleSheet("font-weight: bold; color: #F44336;")
                
                # 检查库存是否足够
                if new_stock < 0:
                    self.new_stock_label.setText(f"{new_stock} {self.product.unit} (库存不足!)")
                    self.new_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #F44336;")
                    self.confirm_btn.setEnabled(False)
                    return
                else:
                    self.confirm_btn.setEnabled(True)
            
            # 更新预览标签
            self.new_stock_label.setText(f"{new_stock} {self.product.unit}")
            self.change_label.setText(change_text)
            
            # 设置库存颜色
            if new_stock <= 10:
                self.new_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #F44336;")
            elif new_stock <= 50:
                self.new_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #FF9800;")
            else:
                self.new_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #4CAF50;")
                
        except Exception as e:
            self.logger.error(f"更新操作预览失败: {e}")
    
    def confirm_operation(self):
        """确认操作"""
        try:
            quantity = self.quantity_spin.value()
            reason = self.reason_combo.currentText().strip()
            operator = self.operator_edit.text().strip()
            notes = self.notes_edit.toPlainText().strip()
            operation_time = self.datetime_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss")
            
            # 验证输入
            if not reason:
                QMessageBox.warning(self, "警告", "请选择或输入操作原因")
                self.reason_combo.setFocus()
                return
            
            # 构建完整的操作原因
            full_reason = reason
            if operator:
                full_reason += f" (操作员: {operator})"
            if notes:
                full_reason += f" - {notes}"
            full_reason += f" [时间: {operation_time}]"
            
            # 计算库存变化量
            if self.is_stock_in:
                quantity_change = quantity
            else:
                quantity_change = -quantity
                
                # 再次检查库存
                if self.product.quantity + quantity_change < 0:
                    QMessageBox.critical(self, "错误", "库存不足，无法完成出库操作")
                    return
            
            # 确认对话框
            operation_name = "入库" if self.is_stock_in else "出库"
            new_stock = self.product.quantity + quantity_change
            
            confirm_msg = f"""
确认{operation_name}操作：

商品名称: {self.product.name}
{operation_name}数量: {abs(quantity_change)} {self.product.unit}
当前库存: {self.product.quantity} {self.product.unit}
操作后库存: {new_stock} {self.product.unit}
操作原因: {reason}

确定要执行此操作吗？
            """.strip()
            
            reply = QMessageBox.question(
                self, f"确认{operation_name}",
                confirm_msg,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 发送库存更新信号
                self.stock_updated.emit(self.product.product_id, quantity_change, full_reason)
                
                # 关闭对话框
                self.accept()
                
        except Exception as e:
            self.logger.error(f"确认操作失败: {e}")
            QMessageBox.critical(self, "错误", f"操作失败:\n{e}")


class QuickStockDialog(QDialog, LoggerMixin):
    """快速库存调整对话框"""
    
    # 信号定义
    stock_updated = pyqtSignal(str, int, str)  # product_id, new_quantity, reason
    
    def __init__(self, product: Product, parent=None):
        """
        初始化快速库存调整对话框
        
        Args:
            product: 商品对象
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.product = product
        
        # 初始化界面
        self.init_ui()
        
        self.logger.info("快速库存调整对话框初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"库存调整 - {self.product.name}")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 商品信息
        product_group = QGroupBox("商品信息")
        product_layout = QFormLayout(product_group)
        
        name_label = QLabel(self.product.name)
        name_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        product_layout.addRow("商品名称:", name_label)
        
        current_stock_label = QLabel(f"{self.product.quantity} {self.product.unit}")
        current_stock_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #FF9800;")
        product_layout.addRow("当前库存:", current_stock_label)
        
        layout.addWidget(product_group)
        
        # 调整信息
        adjust_group = QGroupBox("库存调整")
        adjust_layout = QFormLayout(adjust_group)
        
        # 新库存数量
        self.new_quantity_spin = QSpinBox()
        self.new_quantity_spin.setRange(0, 999999)
        self.new_quantity_spin.setValue(self.product.quantity)
        self.new_quantity_spin.setSuffix(f" {self.product.unit}")
        self.new_quantity_spin.valueChanged.connect(self.update_change_preview)
        adjust_layout.addRow("新库存数量:", self.new_quantity_spin)
        
        # 变化量预览
        self.change_preview_label = QLabel("变化: 0")
        self.change_preview_label.setStyleSheet("font-weight: bold;")
        adjust_layout.addRow("库存变化:", self.change_preview_label)
        
        # 调整原因
        self.reason_edit = QLineEdit()
        self.reason_edit.setPlaceholderText("请输入调整原因")
        self.reason_edit.setText("库存盘点调整")
        adjust_layout.addRow("调整原因:", self.reason_edit)
        
        layout.addWidget(adjust_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.confirm_btn = QPushButton("✅ 确认调整")
        self.confirm_btn.clicked.connect(self.confirm_adjustment)
        button_layout.addWidget(self.confirm_btn)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化预览
        self.update_change_preview()
    
    def update_change_preview(self):
        """更新变化预览"""
        try:
            new_quantity = self.new_quantity_spin.value()
            current_quantity = self.product.quantity
            change = new_quantity - current_quantity
            
            if change > 0:
                self.change_preview_label.setText(f"变化: +{change} {self.product.unit}")
                self.change_preview_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
            elif change < 0:
                self.change_preview_label.setText(f"变化: {change} {self.product.unit}")
                self.change_preview_label.setStyleSheet("font-weight: bold; color: #F44336;")
            else:
                self.change_preview_label.setText("变化: 无变化")
                self.change_preview_label.setStyleSheet("font-weight: bold; color: #666;")
                
        except Exception as e:
            self.logger.error(f"更新变化预览失败: {e}")
    
    def confirm_adjustment(self):
        """确认调整"""
        try:
            new_quantity = self.new_quantity_spin.value()
            reason = self.reason_edit.text().strip()
            
            # 验证输入
            if not reason:
                QMessageBox.warning(self, "警告", "请输入调整原因")
                self.reason_edit.setFocus()
                return
            
            # 计算变化量
            change = new_quantity - self.product.quantity
            
            if change == 0:
                QMessageBox.information(self, "提示", "库存数量没有变化")
                return
            
            # 确认对话框
            confirm_msg = f"""
确认库存调整：

商品名称: {self.product.name}
当前库存: {self.product.quantity} {self.product.unit}
调整后库存: {new_quantity} {self.product.unit}
变化量: {'+' if change > 0 else ''}{change} {self.product.unit}
调整原因: {reason}

确定要执行此调整吗？
            """.strip()
            
            reply = QMessageBox.question(
                self, "确认库存调整",
                confirm_msg,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                # 发送库存更新信号
                self.stock_updated.emit(self.product.product_id, change, f"库存调整: {reason}")
                
                # 关闭对话框
                self.accept()
                
        except Exception as e:
            self.logger.error(f"确认调整失败: {e}")
            QMessageBox.critical(self, "错误", f"调整失败:\n{e}")
