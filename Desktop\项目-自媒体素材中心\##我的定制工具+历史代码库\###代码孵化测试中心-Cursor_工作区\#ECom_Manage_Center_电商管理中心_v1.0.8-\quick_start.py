#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本

用于快速启动应用程序，包含依赖检查和错误处理。
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    missing_packages = []
    
    # 检查PyQt6
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        missing_packages.append("PyQt6")
        print("❌ PyQt6 未安装")
    
    # 检查其他依赖
    required_packages = ["decimal", "sqlite3", "json", "logging", "datetime"]
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("正在尝试安装...")
        
        try:
            import subprocess
            for package in missing_packages:
                if package == "PyQt6":
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
                    print(f"✅ {package} 安装成功")
        except Exception as e:
            print(f"❌ 自动安装失败: {e}")
            print("请手动运行: pip install PyQt6")
            return False
    
    return True

def create_directories():
    """创建必要的目录"""
    print("📁 创建必要目录...")
    
    directories = [
        "data",
        "logs", 
        "config",
        "resources/themes"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试基础功能...")
    
    try:
        # 测试配置管理
        from core.config import Config
        config = Config("config/test_config.json")
        config.set("test.key", "test_value")
        
        # 测试数据库
        from core.database import DatabaseManager
        db_manager = DatabaseManager("data/test.db")
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        db_manager.close()
        
        # 测试商品管理器
        from core.managers.product_manager import ProductManager
        
        print("✅ 基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 电商管理系统整合版 v2.0.0 - 快速启动")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请手动安装依赖包")
        input("按任意键退出...")
        return 1
    
    # 2. 创建目录
    create_directories()
    
    # 3. 测试基础功能
    if not test_basic_functionality():
        print("\n❌ 基础功能测试失败")
        input("按任意键退出...")
        return 1
    
    # 4. 启动应用
    print("\n🚀 启动应用程序...")
    try:
        from main import ECommerceApp
        
        app = ECommerceApp()
        if app.initialize():
            print("✅ 应用程序初始化成功")
            exit_code = app.run()
            print(f"应用程序退出，代码: {exit_code}")
            return exit_code
        else:
            print("❌ 应用程序初始化失败")
            return 1
            
    except Exception as e:
        print(f"❌ 启动应用程序失败: {e}")
        import traceback
        traceback.print_exc()
        input("按任意键退出...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
