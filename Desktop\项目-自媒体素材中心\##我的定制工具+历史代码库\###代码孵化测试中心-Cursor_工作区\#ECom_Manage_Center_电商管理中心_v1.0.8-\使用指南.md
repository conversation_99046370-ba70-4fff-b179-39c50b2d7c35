# 📚 电商管理系统整合版 v2.0.0 - 使用指南

## 🚀 快速开始

### 第一步：验证系统
```bash
# 运行基础测试，确保系统正常
python test_simple.py
```
**预期结果**: 所有测试通过，显示"🎉 所有测试通过！系统基本功能正常。"

### 第二步：启动应用
```bash
# 方式1：双击启动脚本
run.bat

# 方式2：Python启动
python main.py

# 方式3：快速启动（包含依赖检查）
python quick_start.py
```

### 第三步：开始使用
1. 应用启动后会显示主界面
2. 点击"库存管理"标签页
3. 开始添加您的第一个商品

## 📦 库存管理功能

### 添加商品
1. **点击"添加商品"按钮**
2. **填写基础信息**：
   - 商品名称（必填）
   - 商品分类（必填）
   - 商品编码（可选）
   - 商品描述（可选）

3. **设置库存信息**：
   - 库存数量
   - 计量单位
   - 商品状态
   - 存放位置

4. **配置价格信息**：
   - 采购价格
   - 运费成本
   - 其他成本
   - 销售价格
   - 折扣率
   - 系统会自动计算利润和利润率

5. **添加供应商信息**：
   - 供应商名称
   - 采购链接
   - 销售链接
   - 采购员信息
   - 商品图片

6. **设置扩展信息**：
   - 品牌、型号、颜色、尺寸
   - 商品标签
   - 备注信息

7. **点击"保存"完成添加**

### 编辑商品
1. 在商品列表中双击要编辑的商品
2. 或选中商品后点击"编辑商品"按钮
3. 修改信息后点击"保存"

### 库存操作

#### 入库操作
1. 选中商品，点击"入库"按钮
2. 填写入库信息：
   - 入库数量
   - 操作原因（采购入库、退货入库等）
   - 操作时间
   - 操作员
   - 备注信息
3. 系统会显示操作预览
4. 确认后完成入库

#### 出库操作
1. 选中商品，点击"出库"按钮
2. 填写出库信息：
   - 出库数量
   - 操作原因（销售出库、调拨出库等）
   - 操作时间
   - 操作员
   - 备注信息
3. 系统会检查库存是否足够
4. 确认后完成出库

### 搜索和筛选
1. **关键词搜索**：在搜索框输入商品名称、编码或分类
2. **分类筛选**：选择特定分类查看商品
3. **状态筛选**：按商品状态筛选
4. **排序功能**：点击列标题进行排序

### 统计信息
- 查看总商品数量
- 查看各分类商品统计
- 查看库存预警信息
- 查看利润统计

## 🔧 系统设置

### 数据库位置
- 默认位置：`data/inventory.db`
- 可在配置文件中修改：`config/app_config.json`

### 日志文件
- 位置：`logs/app.log`
- 包含所有操作记录和错误信息

### 配置文件
- 主配置：`config/app_config.json`
- 用户偏好：自动保存在数据库中

## 🛠️ 故障排除

### 常见问题

#### 1. 应用无法启动
**解决方案**：
```bash
# 检查Python环境
python --version

# 安装依赖
pip install PyQt6

# 运行测试
python test_simple.py
```

#### 2. 数据库错误
**解决方案**：
```bash
# 删除数据库文件重新创建
del data\inventory.db

# 重新启动应用
python main.py
```

#### 3. 界面显示异常
**解决方案**：
- 确保PyQt6正确安装
- 检查系统DPI设置
- 尝试重启应用

#### 4. 商品保存失败
**解决方案**：
- 检查必填字段是否完整
- 确保商品名称和编码不重复
- 查看日志文件了解详细错误

### 日志查看
```bash
# 查看最新日志
type logs\app.log

# 查看错误信息
findstr "ERROR" logs\app.log
```

## 📊 数据管理

### 数据备份
1. **手动备份**：
   - 复制 `data` 文件夹
   - 复制 `config` 文件夹

2. **定期备份**：
   - 建议每天备份数据库文件
   - 重要操作前先备份

### 数据恢复
1. 停止应用程序
2. 将备份的数据库文件复制到 `data` 文件夹
3. 重新启动应用程序

### 数据导出
- 当前版本支持查看和编辑
- 后续版本将支持CSV导出功能

## 🔄 系统维护

### 定期维护
1. **清理日志文件**：定期删除旧的日志文件
2. **数据库优化**：重启应用程序会自动优化数据库
3. **检查更新**：关注项目更新

### 性能优化
1. **定期重启**：长时间运行后建议重启应用
2. **清理数据**：删除不需要的测试数据
3. **磁盘空间**：确保有足够的磁盘空间

## 📞 技术支持

### 获取帮助
1. **查看文档**：
   - `README.md` - 项目概述
   - `DEVELOPMENT_PROGRESS.md` - 开发进度
   - `PROJECT_COMPLETION.md` - 项目完成报告

2. **运行测试**：
   - `python test_simple.py` - 基础测试
   - `python test_basic.py` - 完整测试

3. **调试工具**：
   - `python debug_product.py` - 商品功能调试

### 错误报告
如果遇到问题，请提供：
1. 错误信息截图
2. 日志文件内容
3. 操作步骤描述
4. 系统环境信息

## 🎯 最佳实践

### 商品管理
1. **规范命名**：使用统一的商品命名规则
2. **分类管理**：建立清晰的分类体系
3. **编码规则**：制定商品编码标准
4. **定期盘点**：定期检查库存准确性

### 数据安全
1. **定期备份**：每天备份重要数据
2. **权限控制**：限制数据文件访问权限
3. **操作记录**：重要操作前记录原因
4. **版本控制**：保留历史版本备份

### 效率提升
1. **快捷键**：熟悉界面快捷键
2. **批量操作**：合理使用批量功能
3. **模板使用**：建立商品信息模板
4. **流程优化**：根据业务需求调整流程

---

## 🎉 开始您的电商管理之旅！

现在您已经掌握了电商管理系统的基本使用方法，可以开始高效管理您的商品库存了！

**祝您使用愉快！** 🚀
