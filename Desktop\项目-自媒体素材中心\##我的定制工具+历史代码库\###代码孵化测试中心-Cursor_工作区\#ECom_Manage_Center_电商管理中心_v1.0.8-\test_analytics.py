#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析模块测试脚本

测试数据分析功能是否正常工作。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_analytics_data():
    """测试数据分析数据计算"""
    print("🧪 测试数据分析功能...")
    
    try:
        from core.database import DatabaseManager
        from gui.modules.analytics.analytics_widget import AnalyticsWidget
        
        # 创建数据库管理器
        db_manager = DatabaseManager("data/test_analytics.db")
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        print("✅ 数据库初始化成功")
        
        # 创建数据分析组件
        analytics_widget = AnalyticsWidget(db_manager)
        
        # 测试数据计算
        analytics_data = analytics_widget.calculate_analytics()
        
        print("✅ 数据分析计算成功")
        print(f"📊 数据分析结果:")
        print(f"   商品总数: {analytics_data.get('total_products', 0)}")
        print(f"   库存价值: ¥{analytics_data.get('total_inventory_value', 0):,.2f}")
        print(f"   总成本: ¥{analytics_data.get('total_cost', 0):,.2f}")
        print(f"   预期利润: ¥{analytics_data.get('total_expected_profit', 0):,.2f}")
        print(f"   平均利润率: {analytics_data.get('avg_profit_margin', 0):.1f}%")
        print(f"   低库存预警: {analytics_data.get('low_stock_count', 0)} 个")
        print(f"   供应商数量: {analytics_data.get('total_suppliers', 0)}")
        print(f"   活跃批次: {analytics_data.get('active_batches', 0)}")
        
        categories = analytics_data.get('categories', {})
        print(f"   商品分类: {len(categories)} 个")
        for category, data in categories.items():
            print(f"     - {category}: {data['count']} 个商品")
        
        # 测试界面更新
        analytics_widget.update_dashboard()
        analytics_widget.update_category_analysis()
        analytics_widget.update_trend_analysis()
        
        print("✅ 界面更新成功")
        
        # 关闭数据库
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analytics_ui():
    """测试数据分析界面"""
    print("\n🎨 测试数据分析界面...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from core.database import DatabaseManager
        from gui.modules.analytics import AnalyticsWidget
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建数据库管理器
        db_manager = DatabaseManager("data/test_analytics.db")
        db_manager.initialize()
        
        # 创建数据分析界面
        analytics_widget = AnalyticsWidget(db_manager)
        
        print("✅ 数据分析界面创建成功")
        print("✅ 界面包含标签页:")
        
        # 检查标签页
        tab_widget = analytics_widget.findChild(analytics_widget.__class__, "")
        if hasattr(analytics_widget, 'children'):
            for child in analytics_widget.children():
                if hasattr(child, 'tabText'):
                    for i in range(child.count()):
                        print(f"   - {child.tabText(i)}")
        
        # 关闭数据库
        db_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_module_integration():
    """测试模块集成"""
    print("\n🔗 测试模块集成...")
    
    try:
        # 测试所有模块导入
        from gui.modules.inventory import InventoryWidget
        from gui.modules.analytics import AnalyticsWidget
        from gui.modules.dropship import DropshipWidget
        from gui.modules.comparison import ComparisonWidget
        from gui.modules.settings import SettingsWidget
        
        print("✅ 所有模块导入成功:")
        print("   - 📦 库存管理模块")
        print("   - 📊 数据分析模块")
        print("   - 🚚 代发管理模块")
        print("   - 📊 供应链对比模块")
        print("   - ⚙️ 系统设置模块")
        
        # 测试数据库管理器
        from core.database import DatabaseManager
        from core.managers.product_manager import ProductManager
        from core.managers.supplier_manager import SupplierManager
        from core.managers.batch_manager import BatchManager
        
        print("✅ 所有管理器导入成功:")
        print("   - 🗄️ 数据库管理器")
        print("   - 📦 商品管理器")
        print("   - 🏪 供应商管理器")
        print("   - 📋 批次管理器")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 数据分析模块测试")
    print("=" * 50)
    
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("模块集成", test_module_integration),
        ("数据分析功能", test_analytics_data),
        ("数据分析界面", test_analytics_ui),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！数据分析模块工作正常。")
        print("\n📋 数据分析模块功能:")
        print("1. ✅ 实时数据概览仪表板")
        print("2. ✅ 8个关键业务指标")
        print("3. ✅ 分类分析表格")
        print("4. ✅ 趋势分析和预警")
        print("5. ✅ 示例数据展示")
        print("6. ✅ 界面响应和更新")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
    
    sys.exit(exit_code)
