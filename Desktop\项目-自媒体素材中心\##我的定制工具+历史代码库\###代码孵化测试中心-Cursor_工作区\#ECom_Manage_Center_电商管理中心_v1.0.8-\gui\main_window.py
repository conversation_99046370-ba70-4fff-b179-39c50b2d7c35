#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块

电商管理系统的主界面，整合所有功能模块。
"""

import logging
from typing import Optional
from PyQt6.QtWidgets import (
    QMainWindow,
    QTabWidget,
    QVBoxLayout,
    QHBoxLayout,
    QWidget,
    QMenuBar,
    QStatusBar,
    QToolBar,
    QLabel,
    QMessageBox,
    QSplashScreen,
    QProgressBar,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QAction, QIcon, QPixmap

from core.config import Config
from core.database import DatabaseManager
from core.managers.data_integration_manager import DataIntegrationManager
from utils.logger import LoggerMixin


class MainWindow(QMainWindow, LoggerMixin):
    """主窗口类"""

    # 信号定义
    status_message = pyqtSignal(str)
    progress_update = pyqtSignal(int)

    def __init__(
        self,
        config: Config,
        db_manager: DatabaseManager,
        logger: logging.Logger,
        parent=None,
    ):
        """
        初始化主窗口

        Args:
            config: 配置管理器
            db_manager: 数据库管理器
            logger: 日志记录器
            parent: 父窗口
        """
        super().__init__(parent)

        self.config = config
        self.db_manager = db_manager
        self._logger = logger

        # 初始化数据集成管理器
        self.data_integration_manager = DataIntegrationManager(db_manager)

        # 窗口状态
        self.is_initialized = False

        # 子模块
        self.tab_widget = None
        self.status_bar = None
        self.progress_bar = None

        # 初始化界面
        self.init_ui()
        self.load_window_state()

        # 连接信号
        self.connect_signals()

        self.logger.info("主窗口初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置窗口属性
            self.setWindowTitle("电商管理系统整合版 v2.0.0")
            self.setMinimumSize(1200, 800)
            # 设置默认窗口大小为1600x1300，提供更充足的操作空间
            self.resize(1600, 1300)

            # 设置窗口图标
            self.set_window_icon()

            # 创建中央控件
            self.create_central_widget()

            # 创建菜单栏
            self.create_menu_bar()

            # 创建工具栏
            self.create_tool_bar()

            # 创建状态栏
            self.create_status_bar()

            # 创建标签页
            self.create_tab_pages()

            self.is_initialized = True

        except Exception as e:
            self.logger.error(f"初始化界面失败: {e}")
            QMessageBox.critical(self, "错误", f"初始化界面失败:\n{e}")

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            from pathlib import Path

            icon_path = (
                Path(__file__).parent.parent / "resources" / "icons" / "app_icon.ico"
            )
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")

    def create_central_widget(self):
        """创建中央控件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        self.tab_widget.setTabsClosable(False)

        layout.addWidget(self.tab_widget)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入数据
        import_action = QAction("导入数据(&I)", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        # 导出数据
        export_action = QAction("导出数据(&E)", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings)
        edit_menu.addAction(settings_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 刷新
        refresh_action = QAction("刷新(&R)", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 数据库管理
        db_action = QAction("数据库管理(&D)", self)
        db_action.triggered.connect(self.show_database_manager)
        tools_menu.addAction(db_action)

        # 日志查看
        log_action = QAction("日志查看(&L)", self)
        log_action.triggered.connect(self.show_log_viewer)
        tools_menu.addAction(log_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 用户手册
        manual_action = QAction("用户手册(&M)", self)
        manual_action.triggered.connect(self.show_manual)
        help_menu.addAction(manual_action)

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)

        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setToolTip("刷新当前数据 (F5)")
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # 导入按钮
        import_action = QAction("导入", self)
        import_action.setToolTip("导入数据 (Ctrl+I)")
        import_action.triggered.connect(self.import_data)
        toolbar.addAction(import_action)

        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setToolTip("导出数据 (Ctrl+E)")
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setToolTip("系统设置 (Ctrl+,)")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 强制设置状态栏高度限制为更小的值
        self.status_bar.setFixedHeight(20)
        self.status_bar.setSizeGripEnabled(False)

        # 设置状态栏样式，确保高度不会被子控件撑大
        self.status_bar.setStyleSheet(
            """
            QStatusBar {
                height: 20px;
                max-height: 20px;
                min-height: 20px;
                border-top: 1px solid #555;
                background-color: #2b2b2b;
                color: white;
                font-size: 10px;
                padding: 0px;
                margin: 0px;
            }
            QStatusBar QLabel {
                height: 16px;
                max-height: 16px;
                min-height: 16px;
                padding: 1px 4px;
                border: none;
                background: transparent;
                font-size: 10px;
            }
            QStatusBar QProgressBar {
                height: 12px;
                max-height: 12px;
                min-height: 12px;
                border: 1px solid #555;
                border-radius: 2px;
                background-color: #404040;
                margin: 2px;
            }
        """
        )

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setFixedHeight(16)
        self.status_bar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedSize(180, 12)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # 数据库状态
        self.db_status_label = QLabel("数据库: 已连接")
        self.db_status_label.setFixedHeight(16)
        self.status_bar.addPermanentWidget(self.db_status_label)

    def create_tab_pages(self):
        """创建标签页"""
        try:
            # 库存管理页面
            inventory_widget = self.create_inventory_page()
            self.tab_widget.addTab(inventory_widget, "📦 库存管理")

            # 多平台管理页面 (新增)
            multiplatform_widget = self.create_multiplatform_page()
            self.tab_widget.addTab(multiplatform_widget, "🌐 多平台管理")

            # 代发管理页面
            dropship_widget = self.create_dropship_page()
            self.tab_widget.addTab(dropship_widget, "🚚 代发管理")

            # 供应链对比页面
            comparison_widget = self.create_comparison_page()
            self.tab_widget.addTab(comparison_widget, "📊 供应链对比")

            # 数据分析页面
            analytics_widget = self.create_analytics_page()
            self.tab_widget.addTab(analytics_widget, "📈 数据分析")

            # 系统设置页面
            settings_widget = self.create_settings_page()
            self.tab_widget.addTab(settings_widget, "⚙️ 系统设置")

        except Exception as e:
            self.logger.error(f"创建标签页失败: {e}")

    def create_inventory_page(self) -> QWidget:
        """创建库存管理页面"""
        try:
            from gui.modules.inventory import InventoryWidget

            inventory_widget = InventoryWidget(self.db_manager, self)

            # 连接信号
            inventory_widget.status_message.connect(self.update_status_message)
            inventory_widget.progress_update.connect(self.update_progress)

            return inventory_widget

        except Exception as e:
            self.logger.error(f"创建库存管理页面失败: {e}")

            # 返回错误页面
            widget = QWidget()
            layout = QVBoxLayout(widget)

            error_label = QLabel("❌ 库存管理模块加载失败")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("font-size: 24px; color: #f44336; margin: 50px;")
            layout.addWidget(error_label)

            info_label = QLabel(f"错误信息: {str(e)}")
            info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_label.setStyleSheet("font-size: 14px; color: #888;")
            layout.addWidget(info_label)

            return widget

    def create_multiplatform_page(self) -> QWidget:
        """创建多平台管理页面"""
        try:
            # 创建多平台管理的主容器
            main_widget = QWidget()
            layout = QVBoxLayout(main_widget)

            # 创建标签页控件
            multiplatform_tabs = QTabWidget()

            # 仪表板标签页
            from gui.modules.multiplatform import MultiplatformDashboard

            dashboard = MultiplatformDashboard(self.db_manager, self)
            multiplatform_tabs.addTab(dashboard, "📊 仪表板")

            # 平台管理标签页
            from gui.modules.multiplatform import PlatformManagerWidget

            platform_manager = PlatformManagerWidget(self.db_manager, self)
            multiplatform_tabs.addTab(platform_manager, "🔧 平台配置")

            # 商品映射标签页
            from gui.modules.multiplatform import MappingManagerWidget

            mapping_manager = MappingManagerWidget(self.db_manager, self)
            multiplatform_tabs.addTab(mapping_manager, "🔗 商品映射")

            # 订单处理标签页
            from gui.modules.multiplatform import OrderProcessorWidget

            order_processor = OrderProcessorWidget(self.db_manager, self)
            multiplatform_tabs.addTab(order_processor, "📋 订单处理")

            layout.addWidget(multiplatform_tabs)

            return main_widget

        except Exception as e:
            self.logger.error(f"创建多平台管理页面失败: {e}")
            return self._create_error_page("多平台管理", str(e))

    def create_dropship_page(self) -> QWidget:
        """创建代发管理页面"""
        try:
            from gui.modules.dropship import DropshipWidget

            return DropshipWidget(self.db_manager, self)
        except Exception as e:
            self.logger.error(f"创建代发管理页面失败: {e}")
            return self._create_error_page("代发管理", str(e))

    def create_comparison_page(self) -> QWidget:
        """创建供应链对比页面"""
        try:
            from gui.modules.comparison import ComparisonWidget

            return ComparisonWidget(self.db_manager, self)
        except Exception as e:
            self.logger.error(f"创建供应链对比页面失败: {e}")
            return self._create_error_page("供应链对比", str(e))

    def create_analytics_page(self) -> QWidget:
        """创建数据分析页面"""
        try:
            from gui.modules.analytics import AnalyticsWidget

            return AnalyticsWidget(self.db_manager, self)
        except Exception as e:
            self.logger.error(f"创建数据分析页面失败: {e}")
            return self._create_error_page("数据分析", str(e))

    def create_settings_page(self) -> QWidget:
        """创建系统设置页面"""
        try:
            from gui.modules.settings import SettingsWidget

            return SettingsWidget(self.db_manager, self)
        except Exception as e:
            self.logger.error(f"创建系统设置页面失败: {e}")
            return self._create_error_page("系统设置", str(e))

    def _create_error_page(self, module_name: str, error_msg: str) -> QWidget:
        """创建错误页面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        error_label = QLabel(f"❌ {module_name}模块加载失败")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet("font-size: 24px; color: #f44336; margin: 50px;")
        layout.addWidget(error_label)

        info_label = QLabel(f"错误信息: {error_msg}")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; color: #888;")
        layout.addWidget(info_label)

        return widget

    def connect_signals(self):
        """连接信号和槽"""
        self.status_message.connect(self.update_status_message)
        self.progress_update.connect(self.update_progress)

    def load_window_state(self):
        """加载窗口状态"""
        try:
            if self.config.get("ui.remember_window_state", True):
                # 窗口大小 - 默认使用更大的尺寸
                size = self.config.get("ui.window_size", [1600, 1300])
                self.resize(size[0], size[1])

                # 窗口位置
                position = self.config.get("ui.window_position", [100, 100])
                self.move(position[0], position[1])

        except Exception as e:
            self.logger.warning(f"加载窗口状态失败: {e}")

    def save_window_state(self):
        """保存窗口状态"""
        try:
            if self.config.get("ui.remember_window_state", True):
                # 保存窗口大小
                size = [self.width(), self.height()]
                self.config.set("ui.window_size", size)

                # 保存窗口位置
                position = [self.x(), self.y()]
                self.config.set("ui.window_position", position)

        except Exception as e:
            self.logger.warning(f"保存窗口状态失败: {e}")

    def update_status_message(self, message: str):
        """更新状态消息"""
        self.status_label.setText(message)
        self.logger.debug(f"状态更新: {message}")

    def update_progress(self, value: int):
        """更新进度条"""
        if value < 0:
            self.progress_bar.setVisible(False)
        else:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(value)

    # 菜单动作处理方法
    def import_data(self):
        """导入数据"""
        self.status_message.emit("导入数据功能开发中...")
        QMessageBox.information(self, "提示", "导入数据功能正在开发中")

    def export_data(self):
        """导出数据"""
        self.status_message.emit("导出数据功能开发中...")
        QMessageBox.information(self, "提示", "导出数据功能正在开发中")

    def refresh_data(self):
        """刷新数据"""
        self.status_message.emit("刷新数据...")
        # TODO: 实现数据刷新逻辑
        QTimer.singleShot(1000, lambda: self.status_message.emit("数据刷新完成"))

    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, "提示", "设置对话框正在开发中")

    def show_database_manager(self):
        """显示数据库管理器"""
        QMessageBox.information(self, "提示", "数据库管理器正在开发中")

    def show_log_viewer(self):
        """显示日志查看器"""
        QMessageBox.information(self, "提示", "日志查看器正在开发中")

    def show_manual(self):
        """显示用户手册"""
        QMessageBox.information(self, "提示", "用户手册正在编写中")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h3>电商管理系统整合版</h3>
        <p><b>版本:</b> 2.0.0</p>
        <p><b>开发:</b> AI Assistant</p>
        <p><b>技术栈:</b> Python + PyQt6 + SQLite</p>
        <br>
        <p>整合三个项目的功能：</p>
        <ul>
        <li>📦 库存管理 (基于 Inventory_Management_v1.7.3)</li>
        <li>🚚 代发管理 (基于 Agent_Order_Management_v1.0.1)</li>
        <li>📊 供应链对比 (基于 Ecom_Supply_Comparison)</li>
        </ul>
        """
        QMessageBox.about(self, "关于", about_text)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 保存窗口状态
            self.save_window_state()

            # 关闭数据库连接
            if self.db_manager:
                self.db_manager.close()

            self.logger.info("主窗口正常关闭")
            event.accept()

        except Exception as e:
            self.logger.error(f"关闭窗口时出错: {e}")
            event.accept()
