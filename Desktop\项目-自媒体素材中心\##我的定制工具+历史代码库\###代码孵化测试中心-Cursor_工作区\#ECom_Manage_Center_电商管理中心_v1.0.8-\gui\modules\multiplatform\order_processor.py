#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单处理界面

提供未匹配订单处理和代发订单管理的用户界面。
"""

import json
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QLabel,
    QLineEdit,
    QComboBox,
    QTextEdit,
    QGroupBox,
    QFormLayout,
    QMessageBox,
    QHeaderView,
    QSplitter,
    QTabWidget,
    QCheckBox,
    QProgressBar,
    QDialog,
    QDialogButtonBox,
    QFrame,
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QColor

from core.database import DatabaseManager
from core.managers.mapping_manager import MappingManager
from core.managers.order_manager import OrderManager
from core.managers.product_manager import ProductManager
from utils.logger import LoggerMixin


class OrderMatchDialog(QDialog):
    """订单匹配对话框"""

    def __init__(
        self,
        mapping_manager: MappingManager,
        product_manager: ProductManager,
        order_log: Dict[str, Any],
        parent=None,
    ):
        super().__init__(parent)
        self.mapping_manager = mapping_manager
        self.product_manager = product_manager
        self.order_log = order_log
        self.selected_product_id = None
        self.init_ui()
        self.load_order_info()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("订单商品匹配")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # 订单信息
        order_group = QGroupBox("订单信息")
        order_layout = QFormLayout(order_group)

        self.platform_label = QLabel()
        self.order_id_label = QLabel()
        self.product_name_label = QLabel()
        self.product_id_label = QLabel()

        order_layout.addRow("平台:", self.platform_label)
        order_layout.addRow("订单号:", self.order_id_label)
        order_layout.addRow("商品名称:", self.product_name_label)
        order_layout.addRow("平台商品ID:", self.product_id_label)

        layout.addWidget(order_group)

        # 匹配选项
        match_group = QGroupBox("匹配选项")
        match_layout = QVBoxLayout(match_group)

        # 智能匹配按钮
        smart_match_layout = QHBoxLayout()
        self.smart_match_button = QPushButton("智能匹配")
        self.smart_match_button.clicked.connect(self.smart_match)

        self.confidence_edit = QLineEdit("0.7")
        self.confidence_edit.setMaximumWidth(80)

        smart_match_layout.addWidget(QLabel("置信度阈值:"))
        smart_match_layout.addWidget(self.confidence_edit)
        smart_match_layout.addWidget(self.smart_match_button)
        smart_match_layout.addStretch()

        match_layout.addLayout(smart_match_layout)

        # 手动选择
        manual_layout = QHBoxLayout()
        self.product_combo = QComboBox()
        self.load_products()

        manual_layout.addWidget(QLabel("手动选择:"))
        manual_layout.addWidget(self.product_combo)

        match_layout.addLayout(manual_layout)

        layout.addWidget(match_group)

        # 匹配结果表格
        result_group = QGroupBox("匹配结果")
        result_layout = QVBoxLayout(result_group)

        self.result_table = QTableWidget(0, 5)
        self.result_table.setHorizontalHeaderLabels(
            ["商品名称", "商品ID", "置信度", "名称相似度", "价格相似度"]
        )
        self.result_table.horizontalHeader().setStretchLastSection(True)
        self.result_table.itemSelectionChanged.connect(self.on_result_selection_changed)

        result_layout.addWidget(self.result_table)
        layout.addWidget(result_group)

        # 按钮
        button_layout = QHBoxLayout()

        self.match_button = QPushButton("确认匹配")
        self.match_button.clicked.connect(self.confirm_match)
        self.match_button.setEnabled(False)

        self.skip_button = QPushButton("跳过")
        self.skip_button.clicked.connect(self.skip_match)

        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.match_button)
        button_layout.addWidget(self.skip_button)
        button_layout.addWidget(cancel_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

    def load_order_info(self):
        """加载订单信息"""
        self.platform_label.setText(self.order_log.get("platform_type", ""))
        self.order_id_label.setText(self.order_log.get("platform_order_id", ""))
        self.product_name_label.setText(self.order_log.get("product_name", ""))
        self.product_id_label.setText(self.order_log.get("platform_product_id", ""))

    def load_products(self):
        """加载商品列表"""
        try:
            products = self.product_manager.get_all_products()
            self.product_combo.clear()
            self.product_combo.addItem("请选择商品...", None)

            for product in products:
                self.product_combo.addItem(
                    f"{product.name} ({product.product_id})", product.product_id
                )

        except Exception as e:
            QMessageBox.warning(self, "加载失败", f"加载商品列表失败：{str(e)}")

    def smart_match(self):
        """智能匹配"""
        try:
            confidence = float(self.confidence_edit.text() or 0.7)

            order_data = {
                "product_name": self.order_log.get("product_name", ""),
                "price": 0,  # 这里可以从订单数据中提取价格
            }

            matches = self.mapping_manager.smart_match_products(order_data, confidence)

            # 显示结果
            self.result_table.setRowCount(len(matches))

            for i, match in enumerate(matches):
                self.result_table.setItem(i, 0, QTableWidgetItem(match["product_name"]))
                self.result_table.setItem(i, 1, QTableWidgetItem(match["product_id"]))
                self.result_table.setItem(
                    i, 2, QTableWidgetItem(f"{match['confidence']:.3f}")
                )
                self.result_table.setItem(
                    i, 3, QTableWidgetItem(f"{match['name_similarity']:.3f}")
                )
                self.result_table.setItem(
                    i, 4, QTableWidgetItem(f"{match['price_similarity']:.3f}")
                )

                # 根据置信度设置行颜色
                if match["confidence"] >= 0.9:
                    color = QColor(200, 255, 200)  # 绿色
                elif match["confidence"] >= 0.7:
                    color = QColor(255, 255, 200)  # 黄色
                else:
                    color = QColor(255, 200, 200)  # 红色

                for j in range(5):
                    item = self.result_table.item(i, j)
                    if item:
                        item.setBackground(color)

            if not matches:
                QMessageBox.information(self, "匹配结果", "未找到匹配的商品。")

        except ValueError:
            QMessageBox.warning(self, "输入错误", "置信度必须是数字！")
        except Exception as e:
            QMessageBox.critical(self, "匹配失败", f"智能匹配失败：{str(e)}")

    def on_result_selection_changed(self):
        """结果选择变化"""
        current_row = self.result_table.currentRow()
        if current_row >= 0:
            self.selected_product_id = self.result_table.item(current_row, 1).text()
            self.match_button.setEnabled(True)
        else:
            self.selected_product_id = None
            self.match_button.setEnabled(False)

    def confirm_match(self):
        """确认匹配"""
        # 优先使用表格选择的商品
        if self.selected_product_id:
            product_id = self.selected_product_id
        else:
            # 使用下拉框选择的商品
            product_id = self.product_combo.currentData()

        if not product_id:
            QMessageBox.warning(self, "选择错误", "请选择要匹配的商品！")
            return

        try:
            # 创建映射关系
            success = self.mapping_manager.create_mapping(
                sku_id=product_id,
                platform_type=self.order_log["platform_type"],
                platform_product_id=self.order_log["platform_product_id"],
                notes=f"订单匹配创建: {self.order_log['platform_order_id']}",
            )

            if success:
                # 更新匹配日志
                from core.managers.order_manager import OrderManager

                order_manager = OrderManager(self.mapping_manager.db_manager)

                order_manager.manual_match_order(
                    self.order_log["log_id"], product_id, "手动匹配"
                )

                QMessageBox.information(self, "成功", "商品匹配成功！")
                self.accept()
            else:
                QMessageBox.warning(self, "失败", "创建映射失败！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"匹配失败：{str(e)}")

    def skip_match(self):
        """跳过匹配"""
        reply = QMessageBox.question(
            self,
            "确认跳过",
            "确定要跳过这个订单的匹配吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.reject()


class OrderProcessorWidget(QWidget, LoggerMixin):
    """订单处理主界面"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.mapping_manager = MappingManager(db_manager)
        self.order_manager = OrderManager(db_manager)
        self.product_manager = ProductManager(db_manager)
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("订单处理中心")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 标签页
        self.tab_widget = QTabWidget()

        # 未匹配订单标签页
        self.unmatched_tab = self.create_unmatched_tab()
        self.tab_widget.addTab(self.unmatched_tab, "未匹配订单")

        # 平台订单标签页
        self.platform_orders_tab = self.create_platform_orders_tab()
        self.tab_widget.addTab(self.platform_orders_tab, "平台订单")

        # 代发订单标签页
        self.dropship_tab = self.create_dropship_tab()
        self.tab_widget.addTab(self.dropship_tab, "代发订单")

        layout.addWidget(self.tab_widget)

    def create_unmatched_tab(self) -> QWidget:
        """创建未匹配订单标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.process_button = QPushButton("处理选中")
        self.process_button.clicked.connect(self.process_unmatched)
        self.process_button.setEnabled(False)

        self.batch_process_button = QPushButton("批量处理")
        self.batch_process_button.clicked.connect(self.batch_process_unmatched)

        self.refresh_unmatched_button = QPushButton("刷新")
        self.refresh_unmatched_button.clicked.connect(self.load_unmatched_orders)

        toolbar_layout.addWidget(self.process_button)
        toolbar_layout.addWidget(self.batch_process_button)
        toolbar_layout.addWidget(self.refresh_unmatched_button)
        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # 未匹配订单表格
        self.unmatched_table = QTableWidget(0, 7)
        self.unmatched_table.setHorizontalHeaderLabels(
            ["平台", "订单号", "商品名称", "平台商品ID", "状态", "创建时间", "错误原因"]
        )

        header = self.unmatched_table.horizontalHeader()
        header.setStretchLastSection(True)

        self.unmatched_table.itemSelectionChanged.connect(
            self.on_unmatched_selection_changed
        )
        self.unmatched_table.itemDoubleClicked.connect(self.process_unmatched)

        layout.addWidget(self.unmatched_table)

        return widget

    def create_platform_orders_tab(self) -> QWidget:
        """创建平台订单标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 过滤器
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("平台:"))
        self.platform_order_filter = QComboBox()
        self.platform_order_filter.addItems(
            ["全部", "taobao", "xiaohongshu", "douyin", "1688"]
        )
        self.platform_order_filter.currentTextChanged.connect(
            self.apply_platform_order_filter
        )

        filter_layout.addWidget(self.platform_order_filter)

        filter_layout.addWidget(QLabel("状态:"))
        self.order_status_filter = QComboBox()
        self.order_status_filter.addItems(
            ["全部", "pending", "processing", "shipped", "completed"]
        )
        self.order_status_filter.currentTextChanged.connect(
            self.apply_platform_order_filter
        )

        filter_layout.addWidget(self.order_status_filter)

        self.refresh_orders_button = QPushButton("刷新")
        self.refresh_orders_button.clicked.connect(self.load_platform_orders)

        filter_layout.addWidget(self.refresh_orders_button)
        filter_layout.addStretch()

        layout.addLayout(filter_layout)

        # 平台订单表格
        self.platform_orders_table = QTableWidget(0, 8)
        self.platform_orders_table.setHorizontalHeaderLabels(
            ["订单号", "平台", "客户", "金额", "状态", "下单时间", "物流信息", "操作"]
        )

        header = self.platform_orders_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.platform_orders_table)

        return widget

    def create_dropship_tab(self) -> QWidget:
        """创建代发订单标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.process_dropship_button = QPushButton("处理代发")
        self.process_dropship_button.clicked.connect(self.process_dropship)

        self.refresh_dropship_button = QPushButton("刷新")
        self.refresh_dropship_button.clicked.connect(self.load_dropship_orders)

        toolbar_layout.addWidget(self.process_dropship_button)
        toolbar_layout.addWidget(self.refresh_dropship_button)
        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # 代发订单表格
        self.dropship_table = QTableWidget(0, 8)
        self.dropship_table.setHorizontalHeaderLabels(
            ["代发ID", "原订单", "供应商", "成本", "售价", "利润", "状态", "创建时间"]
        )

        header = self.dropship_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.dropship_table)

        return widget

    def load_data(self):
        """加载所有数据"""
        self.load_unmatched_orders()
        self.load_platform_orders()
        self.load_dropship_orders()

    def load_unmatched_orders(self):
        """加载未匹配订单"""
        try:
            unmatched_orders = self.order_manager.get_unmatched_orders()

            self.unmatched_table.setRowCount(len(unmatched_orders))

            for i, order in enumerate(unmatched_orders):
                self.unmatched_table.setItem(
                    i, 0, QTableWidgetItem(order.get("platform_type", ""))
                )
                self.unmatched_table.setItem(
                    i, 1, QTableWidgetItem(order.get("platform_order_id", ""))
                )
                self.unmatched_table.setItem(
                    i, 2, QTableWidgetItem(order.get("product_name", ""))
                )
                self.unmatched_table.setItem(
                    i, 3, QTableWidgetItem(order.get("platform_product_id", ""))
                )
                self.unmatched_table.setItem(
                    i, 4, QTableWidgetItem(order.get("matching_status", ""))
                )
                self.unmatched_table.setItem(
                    i, 5, QTableWidgetItem(order.get("created_at", ""))
                )
                self.unmatched_table.setItem(
                    i, 6, QTableWidgetItem(order.get("error_reason", ""))
                )

                # 存储完整数据
                self.unmatched_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, order)

        except Exception as e:
            self.logger.error(f"加载未匹配订单失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载未匹配订单失败：{str(e)}")

    def load_platform_orders(self):
        """加载平台订单"""
        try:
            platform_orders = self.order_manager.get_platform_orders()

            self.platform_orders_table.setRowCount(len(platform_orders))

            for i, order in enumerate(platform_orders):
                self.platform_orders_table.setItem(
                    i, 0, QTableWidgetItem(order.order_id)
                )
                self.platform_orders_table.setItem(
                    i, 1, QTableWidgetItem(order.source_platform or "")
                )
                self.platform_orders_table.setItem(
                    i, 2, QTableWidgetItem(order.customer_name)
                )
                self.platform_orders_table.setItem(
                    i, 3, QTableWidgetItem(f"¥{order.total_amount:.2f}")
                )
                self.platform_orders_table.setItem(
                    i, 4, QTableWidgetItem(order.order_status)
                )
                self.platform_orders_table.setItem(
                    i, 5, QTableWidgetItem(str(order.order_time or ""))
                )
                self.platform_orders_table.setItem(
                    i, 6, QTableWidgetItem(order.tracking_info or "")
                )

                # 操作按钮
                action_button = QPushButton("查看详情")
                action_button.clicked.connect(
                    lambda checked, o=order: self.view_order_details(o)
                )
                self.platform_orders_table.setCellWidget(i, 7, action_button)

        except Exception as e:
            self.logger.error(f"加载平台订单失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载平台订单失败：{str(e)}")

    def load_dropship_orders(self):
        """加载代发订单"""
        try:
            # 这里需要实现获取代发订单的方法
            # dropship_orders = self.order_manager.get_dropship_orders()

            # 暂时显示空表格
            self.dropship_table.setRowCount(0)

        except Exception as e:
            self.logger.error(f"加载代发订单失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载代发订单失败：{str(e)}")

    def on_unmatched_selection_changed(self):
        """未匹配订单选择变化"""
        has_selection = len(self.unmatched_table.selectedItems()) > 0
        self.process_button.setEnabled(has_selection)

    def process_unmatched(self):
        """处理未匹配订单"""
        current_row = self.unmatched_table.currentRow()
        if current_row >= 0:
            order_log = self.unmatched_table.item(current_row, 0).data(
                Qt.ItemDataRole.UserRole
            )

            dialog = OrderMatchDialog(
                self.mapping_manager, self.product_manager, order_log, parent=self
            )

            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_unmatched_orders()

    def batch_process_unmatched(self):
        """批量处理未匹配订单"""
        QMessageBox.information(self, "功能开发中", "批量处理功能正在开发中...")

    def apply_platform_order_filter(self):
        """应用平台订单过滤器"""
        platform_filter = self.platform_order_filter.currentText()
        status_filter = self.order_status_filter.currentText()

        for i in range(self.platform_orders_table.rowCount()):
            show_row = True

            # 平台过滤
            if platform_filter != "全部":
                platform = self.platform_orders_table.item(i, 1).text()
                if platform != platform_filter:
                    show_row = False

            # 状态过滤
            if status_filter != "全部":
                status = self.platform_orders_table.item(i, 4).text()
                if status != status_filter:
                    show_row = False

            self.platform_orders_table.setRowHidden(i, not show_row)

    def view_order_details(self, order):
        """查看订单详情"""
        QMessageBox.information(
            self,
            "订单详情",
            f"订单号: {order.order_id}\n"
            f"客户: {order.customer_name}\n"
            f"金额: ¥{order.total_amount:.2f}\n"
            f"状态: {order.order_status}\n"
            f"地址: {order.shipping_address}",
        )

    def process_dropship(self):
        """处理代发订单"""
        QMessageBox.information(self, "功能开发中", "代发订单处理功能正在开发中...")
