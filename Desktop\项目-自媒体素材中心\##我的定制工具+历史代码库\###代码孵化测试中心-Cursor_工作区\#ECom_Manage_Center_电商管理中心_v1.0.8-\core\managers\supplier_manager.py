#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商管理器

负责供应商的CRUD操作、商品管理、信用评级等业务逻辑。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json

from core.database import DatabaseManager
from core.models.supplier import Supplier, SupplierProduct
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class SupplierManager(LoggerMixin):
    """供应商管理器"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化供应商管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger.info("供应商管理器初始化完成")

    @handle_errors(default_return=False)
    def create_supplier(self, supplier: Supplier) -> bool:
        """
        创建供应商

        Args:
            supplier: 供应商对象

        Returns:
            bool: 创建是否成功
        """
        try:
            # 验证供应商数据
            supplier.validate()

            # 检查供应商名称是否重复
            if self.get_supplier_by_name(supplier.supplier_name):
                raise ValueError(f"供应商名称 {supplier.supplier_name} 已存在")

            # 插入供应商基础信息
            sql = """
            INSERT INTO suppliers (
                supplier_id, supplier_name, platform_id, supplier_code,
                contact_person, contact_phone, contact_email, address,
                api_credentials, credit_rating, cooperation_status,
                notes, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            data = supplier.to_dict()
            params = (
                data["supplier_id"],
                data["supplier_name"],
                data["platform_id"],
                data["supplier_code"],
                data["contact_person"],
                data["contact_phone"],
                data["contact_email"],
                data["address"],
                data["api_credentials"],
                data["credit_rating"],
                data["cooperation_status"],
                data["notes"],
                data["created_at"],
                data["updated_at"],
            )

            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"供应商创建失败: {supplier.supplier_name}")
                return False

            # 插入供应商商品
            if supplier.products:
                for product in supplier.products:
                    if not self._add_supplier_product_to_db(
                        supplier.supplier_id, product
                    ):
                        self.logger.error(
                            f"添加供应商商品失败: {product.supplier_product_id}"
                        )
                        return False

            self.logger.info(
                f"供应商创建成功: {supplier.supplier_id} - {supplier.supplier_name}"
            )
            return True

        except Exception as e:
            self.logger.error(f"创建供应商时出错: {e}")
            return False

    def _add_supplier_product_to_db(
        self, supplier_id: str, product: SupplierProduct
    ) -> bool:
        """添加供应商商品到数据库"""
        try:
            sql = """
            INSERT INTO supplier_products (
                id, supplier_id, supplier_product_id, product_name, product_code,
                category, supplier_price, min_order_quantity, available_stock,
                product_url, image_url, description, shipping_info, last_sync, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            import uuid

            sp_id = f"SP{uuid.uuid4().hex[:8].upper()}"

            data = product.to_dict()
            params = (
                sp_id,
                supplier_id,
                data["supplier_product_id"],
                data["product_name"],
                data["product_code"],
                data["category"],
                data["supplier_price"],
                data["min_order_quantity"],
                data["available_stock"],
                data["product_url"],
                data["image_url"],
                data["description"],
                data["shipping_info"],
                data["last_sync"],
                data["status"],
            )

            cursor = self.db_manager.execute(sql, params)
            return cursor is not None

        except Exception as e:
            self.logger.error(f"添加供应商商品到数据库失败: {e}")
            return False

    @handle_errors(default_return=None)
    def get_supplier(self, supplier_id: str) -> Optional[Supplier]:
        """
        获取供应商

        Args:
            supplier_id: 供应商ID

        Returns:
            Supplier: 供应商对象
        """
        # 获取供应商基础信息
        sql = "SELECT * FROM suppliers WHERE supplier_id = ?"
        row = self.db_manager.fetch_one(sql, (supplier_id,))

        if not row:
            return None

        # 构建供应商对象
        columns = [description[0] for description in self.db_manager.cursor.description]
        supplier_data = dict(zip(columns, row))

        # 获取供应商商品
        supplier_products = self._get_supplier_products(supplier_id)
        supplier_data["products"] = supplier_products

        return Supplier.from_dict(supplier_data)

    def _get_supplier_products(self, supplier_id: str) -> List[SupplierProduct]:
        """获取供应商商品列表"""
        sql = """
        SELECT supplier_product_id, product_name, product_code, category,
               supplier_price, min_order_quantity, available_stock, product_url,
               image_url, description, shipping_info, last_sync, status
        FROM supplier_products 
        WHERE supplier_id = ?
        ORDER BY last_sync DESC
        """

        rows = self.db_manager.fetch_all(sql, (supplier_id,))
        products = []

        if rows:
            for row in rows:
                product_data = {
                    "supplier_product_id": row[0],
                    "product_name": row[1],
                    "product_code": row[2],
                    "category": row[3],
                    "supplier_price": row[4],
                    "min_order_quantity": row[5],
                    "available_stock": row[6],
                    "product_url": row[7],
                    "image_url": row[8],
                    "description": row[9],
                    "shipping_info": row[10],
                    "last_sync": row[11],
                    "status": row[12],
                }
                product = SupplierProduct.from_dict(product_data)
                products.append(product)

        return products

    @handle_errors(default_return=None)
    def get_supplier_by_name(self, supplier_name: str) -> Optional[Supplier]:
        """
        根据供应商名称获取供应商

        Args:
            supplier_name: 供应商名称

        Returns:
            Supplier: 供应商对象
        """
        sql = "SELECT * FROM suppliers WHERE supplier_name = ?"
        row = self.db_manager.fetch_one(sql, (supplier_name,))

        if not row:
            return None

        columns = [description[0] for description in self.db_manager.cursor.description]
        supplier_data = dict(zip(columns, row))

        # 获取供应商商品
        supplier_products = self._get_supplier_products(supplier_data["supplier_id"])
        supplier_data["products"] = supplier_products

        return Supplier.from_dict(supplier_data)

    @handle_errors(default_return=[])
    def get_suppliers(
        self,
        platform_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Supplier]:
        """
        获取供应商列表

        Args:
            platform_id: 平台ID
            status: 合作状态
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Supplier]: 供应商列表
        """
        sql = "SELECT * FROM suppliers WHERE 1=1"
        params = []

        if platform_id:
            sql += " AND platform_id = ?"
            params.append(platform_id)

        if status:
            sql += " AND cooperation_status = ?"
            params.append(status)

        sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        rows = self.db_manager.fetch_all(sql, tuple(params))
        suppliers = []

        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            for row in rows:
                supplier_data = dict(zip(columns, row))

                # 获取供应商商品
                supplier_products = self._get_supplier_products(
                    supplier_data["supplier_id"]
                )
                supplier_data["products"] = supplier_products

                supplier = Supplier.from_dict(supplier_data)
                suppliers.append(supplier)

        return suppliers

    @handle_errors(default_return=[])
    def get_all_suppliers(self) -> List[Supplier]:
        """
        获取所有供应商

        Returns:
            List[Supplier]: 所有供应商列表
        """
        return self.get_suppliers(limit=1000)  # 获取所有供应商，设置较大的限制

    @handle_errors(default_return=[])
    def search_suppliers(self, keyword: str, limit: int = 100) -> List[Supplier]:
        """
        搜索供应商

        Args:
            keyword: 搜索关键词
            limit: 限制数量

        Returns:
            List[Supplier]: 供应商列表
        """
        sql = """
        SELECT * FROM suppliers 
        WHERE supplier_name LIKE ? OR supplier_code LIKE ? OR contact_person LIKE ?
        ORDER BY created_at DESC LIMIT ?
        """

        search_term = f"%{keyword}%"
        params = (search_term, search_term, search_term, limit)

        rows = self.db_manager.fetch_all(sql, params)
        suppliers = []

        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            for row in rows:
                supplier_data = dict(zip(columns, row))

                # 获取供应商商品
                supplier_products = self._get_supplier_products(
                    supplier_data["supplier_id"]
                )
                supplier_data["products"] = supplier_products

                supplier = Supplier.from_dict(supplier_data)
                suppliers.append(supplier)

        return suppliers

    @handle_errors(default_return=False)
    def update_supplier(self, supplier: Supplier) -> bool:
        """
        更新供应商

        Args:
            supplier: 供应商对象

        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证供应商数据
            supplier.validate()
            supplier.updated_at = datetime.now()

            # 更新供应商基础信息
            sql = """
            UPDATE suppliers SET
                supplier_name = ?, platform_id = ?, supplier_code = ?,
                contact_person = ?, contact_phone = ?, contact_email = ?,
                address = ?, api_credentials = ?, credit_rating = ?,
                cooperation_status = ?, notes = ?, updated_at = ?
            WHERE supplier_id = ?
            """

            data = supplier.to_dict()
            params = (
                data["supplier_name"],
                data["platform_id"],
                data["supplier_code"],
                data["contact_person"],
                data["contact_phone"],
                data["contact_email"],
                data["address"],
                data["api_credentials"],
                data["credit_rating"],
                data["cooperation_status"],
                data["notes"],
                data["updated_at"],
                data["supplier_id"],
            )

            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"供应商更新失败: {supplier.supplier_name}")
                return False

            # 更新供应商商品（先删除再重新插入）
            self._delete_supplier_products(supplier.supplier_id)

            if supplier.products:
                for product in supplier.products:
                    if not self._add_supplier_product_to_db(
                        supplier.supplier_id, product
                    ):
                        self.logger.error(
                            f"更新供应商商品失败: {product.supplier_product_id}"
                        )
                        return False

            self.logger.info(
                f"供应商更新成功: {supplier.supplier_id} - {supplier.supplier_name}"
            )
            return True

        except Exception as e:
            self.logger.error(f"更新供应商时出错: {e}")
            return False

    def _delete_supplier_products(self, supplier_id: str) -> bool:
        """删除供应商商品"""
        try:
            sql = "DELETE FROM supplier_products WHERE supplier_id = ?"
            cursor = self.db_manager.execute(sql, (supplier_id,))
            return cursor is not None
        except Exception as e:
            self.logger.error(f"删除供应商商品失败: {e}")
            return False

    @handle_errors(default_return=False)
    def delete_supplier(self, supplier_id: str) -> bool:
        """
        删除供应商

        Args:
            supplier_id: 供应商ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查供应商是否存在
            supplier = self.get_supplier(supplier_id)
            if not supplier:
                self.logger.warning(f"要删除的供应商不存在: {supplier_id}")
                return False

            # 检查是否有关联的代发订单
            # TODO: 添加关联检查逻辑

            # 删除供应商商品
            self._delete_supplier_products(supplier_id)

            # 删除供应商
            sql = "DELETE FROM suppliers WHERE supplier_id = ?"
            cursor = self.db_manager.execute(sql, (supplier_id,))

            if cursor:
                self.logger.info(
                    f"供应商删除成功: {supplier_id} - {supplier.supplier_name}"
                )
                return True
            else:
                self.logger.error(f"供应商删除失败: {supplier_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除供应商时出错: {e}")
            return False

    @handle_errors(default_return=False)
    def update_credit_rating(
        self, supplier_id: str, rating: int, reason: str = ""
    ) -> bool:
        """
        更新供应商信用评级

        Args:
            supplier_id: 供应商ID
            rating: 新评级
            reason: 评级原因

        Returns:
            bool: 更新是否成功
        """
        try:
            supplier = self.get_supplier(supplier_id)
            if not supplier:
                self.logger.error(f"供应商不存在: {supplier_id}")
                return False

            supplier.set_credit_rating(rating, reason)
            return self.update_supplier(supplier)

        except Exception as e:
            self.logger.error(f"更新信用评级时出错: {e}")
            return False

    @handle_errors(default_return=0)
    def get_supplier_count(
        self, platform_id: Optional[str] = None, status: Optional[str] = None
    ) -> int:
        """
        获取供应商数量

        Args:
            platform_id: 平台ID
            status: 合作状态

        Returns:
            int: 供应商数量
        """
        sql = "SELECT COUNT(*) FROM suppliers WHERE 1=1"
        params = []

        if platform_id:
            sql += " AND platform_id = ?"
            params.append(platform_id)

        if status:
            sql += " AND cooperation_status = ?"
            params.append(status)

        result = self.db_manager.fetch_one(sql, tuple(params))
        return result[0] if result else 0

    @handle_errors(default_return={})
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取供应商统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_suppliers": 0,
            "active_suppliers": 0,
            "inactive_suppliers": 0,
            "blacklisted_suppliers": 0,
            "avg_credit_rating": 0.0,
            "total_products": 0,
        }

        # 总供应商数
        stats["total_suppliers"] = self.get_supplier_count()

        # 按状态统计
        stats["active_suppliers"] = self.get_supplier_count(status="active")
        stats["inactive_suppliers"] = self.get_supplier_count(status="inactive")
        stats["blacklisted_suppliers"] = self.get_supplier_count(status="blacklist")

        # 平均信用评级
        sql = "SELECT AVG(credit_rating) FROM suppliers WHERE cooperation_status = 'active'"
        result = self.db_manager.fetch_one(sql)
        if result and result[0]:
            stats["avg_credit_rating"] = float(result[0])

        # 总商品数
        sql = "SELECT COUNT(*) FROM supplier_products"
        result = self.db_manager.fetch_one(sql)
        if result:
            stats["total_products"] = result[0]

        return stats
