#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析模块 - 备份版本

简化的数据分析界面，确保能正常显示。
"""

import logging
from datetime import datetime
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QGridLayout, QPushButton, QTextEdit, QTableWidget,
    QTableWidgetItem, QHeaderView, QGroupBox, QToolBar
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from core.managers.product_manager import ProductManager
from core.managers.batch_manager import BatchManager
from core.managers.supplier_manager import SupplierManager
from utils.logger import LoggerMixin


class AnalyticsWidget(QWidget, LoggerMixin):
    """数据分析主界面 - 简化版"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_manager = ProductManager(db_manager)
        self.batch_manager = BatchManager(db_manager)
        self.supplier_manager = SupplierManager(db_manager)
        
        # 数据缓存
        self.analytics_data = {}
        
        # 初始化界面
        self.init_ui()
        
        # 延迟加载数据
        QTimer.singleShot(100, self.load_sample_data)
        
        self.logger.info("数据分析界面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title = QLabel("📊 数据分析仪表板")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            padding: 20px;
            background-color: #2b2b2b;
            border-radius: 10px;
            margin-bottom: 20px;
        """)
        layout.addWidget(title)
        
        # 工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # 指标卡片
        cards_frame = self.create_cards()
        layout.addWidget(cards_frame)
        
        # 分析表格
        table_frame = self.create_table()
        layout.addWidget(table_frame)
        
        # 状态栏
        status_frame = self.create_status()
        layout.addWidget(status_frame)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #2b2b2b;
                border: none;
                padding: 10px;
            }
            QToolBar QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin-right: 10px;
            }
            QToolBar QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.clicked.connect(self.load_sample_data)
        toolbar.addWidget(refresh_btn)
        
        export_btn = QPushButton("📄 导出报告")
        export_btn.clicked.connect(self.export_report)
        toolbar.addWidget(export_btn)
        
        return toolbar
    
    def create_cards(self):
        """创建指标卡片"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)
        
        # 创建8个指标卡片
        self.cards = {}
        
        cards_data = [
            ("products", "📦", "商品总数", "0", "#4CAF50"),
            ("inventory_value", "💰", "库存价值", "¥0", "#2196F3"),
            ("low_stock", "⚠️", "低库存预警", "0", "#FF9800"),
            ("suppliers", "🏪", "供应商数量", "0", "#9C27B0"),
            ("profit_margin", "📈", "平均利润率", "0%", "#4CAF50"),
            ("batches", "📋", "活跃批次", "0", "#2196F3"),
            ("total_cost", "💸", "总投资成本", "¥0", "#FF5722"),
            ("profit", "💎", "预期利润", "¥0", "#4CAF50"),
        ]
        
        for i, (key, icon, title, value, color) in enumerate(cards_data):
            card = self.create_card(icon, title, value, color)
            self.cards[key] = card
            row = i // 4
            col = i % 4
            layout.addWidget(card, row, col)
        
        return frame
    
    def create_card(self, icon: str, title: str, value: str, color: str):
        """创建单个指标卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: #2b2b2b;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 15px;
                min-height: 100px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; margin-bottom: 5px;")
        layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; font-size: 12px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # 数值
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold; margin-top: 5px;")
        layout.addWidget(value_label)
        
        # 存储值标签以便更新
        card.value_label = value_label
        
        return card
    
    def create_table(self):
        """创建分析表格"""
        group = QGroupBox("📊 分类分析")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: white;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            "分类", "商品数量", "库存价值", "平均利润率", "状态"
        ])
        
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                border: 1px solid #555;
                color: white;
                gridline-color: #555;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """)
        
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        for i in range(1, 5):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.table)
        
        return group
    
    def create_status(self):
        """创建状态栏"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-top: 1px solid #555;
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        self.status_label = QLabel("数据分析就绪")
        self.status_label.setStyleSheet("color: white; font-size: 14px;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        self.update_time_label = QLabel("")
        self.update_time_label.setStyleSheet("color: #888; font-size: 12px;")
        layout.addWidget(self.update_time_label)
        
        return frame
    
    def load_sample_data(self):
        """加载示例数据"""
        try:
            self.status_label.setText("正在加载数据...")
            
            # 示例数据
            sample_data = {
                "total_products": 25,
                "total_inventory_value": 15680.50,
                "total_cost": 12340.00,
                "total_expected_profit": 3340.50,
                "low_stock_count": 3,
                "total_suppliers": 8,
                "active_batches": 12,
                "avg_profit_margin": 27.1,
                "categories": {
                    "电子产品": {"count": 8, "inventory_value": 6800.00, "total_cost": 5200.00, "total_profit": 1600.00},
                    "服装配饰": {"count": 10, "inventory_value": 5200.50, "total_cost": 4100.00, "total_profit": 1100.50},
                    "家居用品": {"count": 5, "inventory_value": 2680.00, "total_cost": 2040.00, "total_profit": 640.00},
                    "未分类": {"count": 2, "inventory_value": 1000.00, "total_cost": 1000.00, "total_profit": 0.00}
                }
            }
            
            # 更新卡片
            self.cards["products"].value_label.setText(str(sample_data["total_products"]))
            self.cards["inventory_value"].value_label.setText(f"¥{sample_data['total_inventory_value']:,.2f}")
            self.cards["low_stock"].value_label.setText(str(sample_data["low_stock_count"]))
            self.cards["suppliers"].value_label.setText(str(sample_data["total_suppliers"]))
            self.cards["profit_margin"].value_label.setText(f"{sample_data['avg_profit_margin']:.1f}%")
            self.cards["batches"].value_label.setText(str(sample_data["active_batches"]))
            self.cards["total_cost"].value_label.setText(f"¥{sample_data['total_cost']:,.2f}")
            self.cards["profit"].value_label.setText(f"¥{sample_data['total_expected_profit']:,.2f}")
            
            # 更新表格
            categories = sample_data["categories"]
            self.table.setRowCount(len(categories))
            
            for row, (category, data) in enumerate(categories.items()):
                self.table.setItem(row, 0, QTableWidgetItem(category))
                self.table.setItem(row, 1, QTableWidgetItem(str(data["count"])))
                self.table.setItem(row, 2, QTableWidgetItem(f"¥{data['inventory_value']:,.2f}"))
                
                margin = (data["total_profit"] / data["total_cost"] * 100) if data["total_cost"] > 0 else 0
                self.table.setItem(row, 3, QTableWidgetItem(f"{margin:.1f}%"))
                
                status = "优秀" if margin > 20 else "良好" if margin > 10 else "一般" if margin > 0 else "亏损"
                status_item = QTableWidgetItem(status)
                if margin > 20:
                    status_item.setForeground(Qt.GlobalColor.green)
                elif margin > 10:
                    status_item.setForeground(Qt.GlobalColor.blue)
                elif margin > 0:
                    status_item.setForeground(Qt.GlobalColor.yellow)
                else:
                    status_item.setForeground(Qt.GlobalColor.red)
                self.table.setItem(row, 4, status_item)
            
            self.status_label.setText("数据加载完成")
            self.update_time_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
            
            self.logger.info("示例数据加载完成")
            
        except Exception as e:
            self.status_label.setText("数据加载失败")
            self.logger.error(f"加载数据失败: {e}")
    
    def export_report(self):
        """导出报告"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "提示", "报告导出功能正在开发中...")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_sample_data()
