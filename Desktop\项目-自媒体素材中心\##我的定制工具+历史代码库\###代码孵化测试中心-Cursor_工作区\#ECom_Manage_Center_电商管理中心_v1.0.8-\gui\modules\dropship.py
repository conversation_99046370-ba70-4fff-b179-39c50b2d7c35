#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代发管理模块界面

代发管理功能的主界面。
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from .dropship.dropship_widget import DropshipWidget as DropshipMainWidget
from .development_widget import DevelopmentWidget


class DropshipWidget(QWidget):
    """代发管理界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """
        )

        # 代发管理标签页 - 完整功能
        dropship_widget = DropshipMainWidget(self.db_manager, self)
        tab_widget.addTab(dropship_widget, "🚚 代发管理")

        # 自动化流程标签页 - 开发中
        automation_info = {
            "name": "自动化流程",
            "icon": "🔄",
            "description": "Automated Dropshipping Process",
            "progress": 20,
            "eta": "3-4周",
            "features": [
                "🔄 自动下单流程",
                "   • 订单自动分配给供应商",
                "   • 自动生成采购订单",
                "   • 库存自动更新",
                "   • 异常订单处理",
                "",
                "📦 物流自动化",
                "   • 物流信息自动同步",
                "   • 发货状态自动更新",
                "   • 快递单号自动获取",
                "   • 客户通知自动发送",
                "",
                "💰 财务自动化",
                "   • 成本自动计算",
                "   • 利润自动统计",
                "   • 对账自动处理",
                "   • 结算自动生成",
            ],
            "phases": [
                ("第一阶段", "基础自动化流程", "⏳ 计划中", "#666"),
                ("第二阶段", "物流信息同步", "⏳ 计划中", "#666"),
                ("第三阶段", "财务自动化", "⏳ 计划中", "#666"),
            ],
        }
        automation_widget = DevelopmentWidget(automation_info, self)
        tab_widget.addTab(automation_widget, "🔄 自动化")

        # 利润分析标签页 - 开发中
        profit_info = {
            "name": "利润分析功能",
            "icon": "📊",
            "description": "Profit Analysis & Optimization",
            "progress": 15,
            "eta": "2-3周",
            "features": [
                "📊 利润分析",
                "   • 实时利润计算",
                "   • 成本结构分析",
                "   • 供应商利润对比",
                "   • 商品利润排行",
                "",
                "📈 趋势分析",
                "   • 利润趋势图表",
                "   • 成本变化分析",
                "   • 价格优化建议",
                "   • 市场竞争分析",
                "",
                "🎯 优化建议",
                "   • 最优供应商推荐",
                "   • 价格调整建议",
                "   • 成本控制方案",
                "   • 利润提升策略",
            ],
            "phases": [
                ("第一阶段", "基础利润统计", "⏳ 计划中", "#666"),
                ("第二阶段", "趋势分析算法", "⏳ 计划中", "#666"),
                ("第三阶段", "优化建议系统", "⏳ 计划中", "#666"),
            ],
        }
        profit_widget = DevelopmentWidget(profit_info, self)
        tab_widget.addTab(profit_widget, "📊 利润分析")

        layout.addWidget(tab_widget)
