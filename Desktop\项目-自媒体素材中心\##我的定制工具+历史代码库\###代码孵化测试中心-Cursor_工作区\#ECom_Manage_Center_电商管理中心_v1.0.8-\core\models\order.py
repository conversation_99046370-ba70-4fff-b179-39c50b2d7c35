#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单数据模型

用于订单管理和代发管理的订单信息。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid
import json


@dataclass
class OrderItem:
    """订单商品项"""

    item_id: Optional[str] = None
    order_id: Optional[str] = None
    product_id: Optional[str] = None
    product_name: str = ""
    product_code: Optional[str] = None
    quantity: int = 1
    unit_price: Decimal = field(default_factory=lambda: Decimal("0"))
    total_price: Decimal = field(default_factory=lambda: Decimal("0"))
    product_image: Optional[str] = None
    specifications: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        # 确保价格字段为Decimal类型
        if not isinstance(self.unit_price, Decimal):
            self.unit_price = Decimal(str(self.unit_price or 0))

        if not isinstance(self.total_price, Decimal):
            self.total_price = Decimal(str(self.total_price or 0))

        # 计算总价
        self.calculate_total()

    def calculate_total(self):
        """计算总价"""
        self.total_price = self.unit_price * Decimal(str(self.quantity))

    def to_dict(self) -> Dict[str, Any]:
        return {
            "item_id": self.item_id,
            "order_id": self.order_id,
            "product_id": self.product_id,
            "product_name": self.product_name,
            "product_code": self.product_code,
            "quantity": self.quantity,
            "unit_price": float(self.unit_price),
            "total_price": float(self.total_price),
            "product_image": self.product_image,
            "specifications": json.dumps(self.specifications),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "OrderItem":
        if isinstance(data.get("specifications"), str):
            data["specifications"] = (
                json.loads(data["specifications"]) if data["specifications"] else {}
            )

        return cls(**data)


@dataclass
class DropshipOrder:
    """代发订单"""

    dropship_id: Optional[str] = None
    original_order_id: Optional[str] = None  # 原始订单ID
    supplier_id: str = ""
    supplier_platform: str = "1688"  # 供应商平台
    supplier_order_id: Optional[str] = None  # 供应商平台的订单号
    supplier_product_id: Optional[str] = None
    quantity: int = 1
    cost_amount: Decimal = field(default_factory=lambda: Decimal("0"))  # 成本金额
    selling_amount: Decimal = field(default_factory=lambda: Decimal("0"))  # 销售金额
    profit_amount: Decimal = field(default_factory=lambda: Decimal("0"))  # 利润金额
    profit_rate: Decimal = field(default_factory=lambda: Decimal("0"))  # 利润率
    tracking_number: Optional[str] = None
    shipping_company: Optional[str] = None
    status: str = (
        "pending"  # pending, confirmed, processing, shipped, delivered, cancelled
    )
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        if self.dropship_id is None:
            self.dropship_id = f"D{uuid.uuid4().hex[:8].upper()}"

        if self.created_at is None:
            self.created_at = datetime.now()

        self.updated_at = datetime.now()

        # 确保价格字段为Decimal类型
        self._ensure_decimal_fields()

        # 计算利润
        self.calculate_profit()

    def _ensure_decimal_fields(self):
        """确保价格字段为Decimal类型"""
        decimal_fields = [
            "cost_amount",
            "selling_amount",
            "profit_amount",
            "profit_rate",
        ]

        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if not isinstance(value, Decimal):
                setattr(self, field_name, Decimal(str(value or 0)))

    def calculate_profit(self):
        """计算利润和利润率"""
        self.profit_amount = self.selling_amount - self.cost_amount

        if self.cost_amount > 0:
            self.profit_rate = (self.profit_amount / self.cost_amount) * Decimal("100")
        else:
            self.profit_rate = Decimal("0")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "dropship_id": self.dropship_id,
            "original_order_id": self.original_order_id,
            "supplier_id": self.supplier_id,
            "supplier_platform": self.supplier_platform,
            "supplier_order_id": self.supplier_order_id,
            "supplier_product_id": self.supplier_product_id,
            "quantity": self.quantity,
            "cost_amount": float(self.cost_amount),
            "selling_amount": float(self.selling_amount),
            "profit_amount": float(self.profit_amount),
            "profit_rate": float(self.profit_rate),
            "tracking_number": self.tracking_number,
            "shipping_company": self.shipping_company,
            "status": self.status,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DropshipOrder":
        # 处理时间字段
        if isinstance(data.get("created_at"), str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])

        if isinstance(data.get("updated_at"), str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"])

        return cls(**data)


@dataclass
class Order:
    """
    订单数据模型

    管理客户订单信息，支持普通订单和代发订单
    """

    # 基础信息
    order_id: Optional[str] = None
    store_id: Optional[str] = None
    platform_order_id: Optional[str] = None  # 平台订单号

    # 多平台扩展字段
    source_platform: Optional[str] = (
        None  # 来源平台 (taobao, xiaohongshu, douyin, 1688)
    )
    platform_store_id: Optional[str] = None  # 平台店铺ID
    order_data: Optional[str] = None  # 原始订单数据 (JSON格式)
    tracking_info: Optional[str] = None  # 物流信息 (JSON格式)

    # 客户信息
    customer_name: str = ""
    customer_phone: Optional[str] = None
    customer_email: Optional[str] = None
    shipping_address: str = ""

    # 金额信息
    total_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    shipping_fee: Decimal = field(default_factory=lambda: Decimal("0"))
    discount_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    final_amount: Decimal = field(default_factory=lambda: Decimal("0"))
    currency: str = "CNY"

    # 状态信息
    order_status: str = (
        "pending"  # pending, confirmed, processing, shipped, delivered, cancelled, refunded
    )
    payment_status: str = "unpaid"  # unpaid, paid, partial, refunded
    shipping_status: str = "unshipped"  # unshipped, processing, shipped, delivered

    # 订单商品
    items: List[OrderItem] = field(default_factory=list)

    # 代发订单
    dropship_orders: List[DropshipOrder] = field(default_factory=list)

    # 时间信息
    order_time: Optional[datetime] = None
    payment_time: Optional[datetime] = None
    shipping_time: Optional[datetime] = None
    delivery_time: Optional[datetime] = None

    # 备注和元数据
    notes: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.order_id is None:
            self.order_id = self.generate_order_id()

        if self.created_at is None:
            self.created_at = datetime.now()

        if self.order_time is None:
            self.order_time = datetime.now()

        self.updated_at = datetime.now()

        # 确保价格字段为Decimal类型
        self._ensure_decimal_fields()

        # 计算最终金额
        self.calculate_final_amount()

    def generate_order_id(self) -> str:
        """生成订单ID"""
        return f"O{uuid.uuid4().hex[:8].upper()}"

    def _ensure_decimal_fields(self):
        """确保价格字段为Decimal类型"""
        decimal_fields = [
            "total_amount",
            "shipping_fee",
            "discount_amount",
            "final_amount",
        ]

        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if not isinstance(value, Decimal):
                setattr(self, field_name, Decimal(str(value or 0)))

    def add_item(self, item: OrderItem):
        """添加订单商品"""
        self.items.append(item)
        self.calculate_total_amount()
        self.updated_at = datetime.now()

    def remove_item(self, product_id: str) -> bool:
        """移除订单商品"""
        for i, item in enumerate(self.items):
            if item.product_id == product_id:
                del self.items[i]
                self.calculate_total_amount()
                self.updated_at = datetime.now()
                return True
        return False

    def get_item(self, product_id: str) -> Optional[OrderItem]:
        """获取订单商品"""
        for item in self.items:
            if item.product_id == product_id:
                return item
        return None

    def calculate_total_amount(self):
        """计算订单总金额"""
        self.total_amount = sum(item.total_price for item in self.items)
        self.calculate_final_amount()

    def calculate_final_amount(self):
        """计算最终金额"""
        self.final_amount = self.total_amount + self.shipping_fee - self.discount_amount

    def add_dropship_order(self, dropship_order: DropshipOrder):
        """添加代发订单"""
        dropship_order.order_id = self.order_id
        self.dropship_orders.append(dropship_order)
        self.updated_at = datetime.now()

    def get_dropship_order(self, dropship_id: str) -> Optional[DropshipOrder]:
        """获取代发订单"""
        for dropship_order in self.dropship_orders:
            if dropship_order.dropship_id == dropship_id:
                return dropship_order
        return None

    def set_order_status(self, status: str, reason: str = ""):
        """设置订单状态"""
        old_status = self.order_status
        self.order_status = status
        self.updated_at = datetime.now()

        # 更新时间戳
        if status == "shipped" and self.shipping_time is None:
            self.shipping_time = datetime.now()
            self.shipping_status = "shipped"
        elif status == "delivered" and self.delivery_time is None:
            self.delivery_time = datetime.now()
            self.shipping_status = "delivered"

        # 记录状态变化
        if "status_changes" not in self.metadata:
            self.metadata["status_changes"] = []

        self.metadata["status_changes"].append(
            {
                "timestamp": datetime.now().isoformat(),
                "old_status": old_status,
                "new_status": status,
                "reason": reason,
            }
        )

    def set_payment_status(self, status: str, reason: str = ""):
        """设置支付状态"""
        old_status = self.payment_status
        self.payment_status = status
        self.updated_at = datetime.now()

        if status == "paid" and self.payment_time is None:
            self.payment_time = datetime.now()

        # 记录支付状态变化
        if "payment_changes" not in self.metadata:
            self.metadata["payment_changes"] = []

        self.metadata["payment_changes"].append(
            {
                "timestamp": datetime.now().isoformat(),
                "old_status": old_status,
                "new_status": status,
                "reason": reason,
            }
        )

    def is_dropship_order(self) -> bool:
        """检查是否为代发订单"""
        return len(self.dropship_orders) > 0

    def get_total_profit(self) -> Decimal:
        """获取代发订单总利润"""
        return sum(do.profit_amount for do in self.dropship_orders)

    def get_total_cost(self) -> Decimal:
        """获取代发订单总成本"""
        return sum(do.cost_amount for do in self.dropship_orders)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "order_id": self.order_id,
            "store_id": self.store_id,
            "platform_order_id": self.platform_order_id,
            # 多平台字段
            "source_platform": self.source_platform,
            "platform_store_id": self.platform_store_id,
            "order_data": self.order_data,
            "tracking_info": self.tracking_info,
            "customer_name": self.customer_name,
            "customer_phone": self.customer_phone,
            "customer_email": self.customer_email,
            "shipping_address": self.shipping_address,
            "total_amount": float(self.total_amount),
            "shipping_fee": float(self.shipping_fee),
            "discount_amount": float(self.discount_amount),
            "final_amount": float(self.final_amount),
            "currency": self.currency,
            "order_status": self.order_status,
            "payment_status": self.payment_status,
            "shipping_status": self.shipping_status,
            "items": json.dumps([item.to_dict() for item in self.items]),
            "dropship_orders": json.dumps(
                [do.to_dict() for do in self.dropship_orders]
            ),
            "order_time": self.order_time.isoformat() if self.order_time else None,
            "payment_time": (
                self.payment_time.isoformat() if self.payment_time else None
            ),
            "shipping_time": (
                self.shipping_time.isoformat() if self.shipping_time else None
            ),
            "delivery_time": (
                self.delivery_time.isoformat() if self.delivery_time else None
            ),
            "notes": self.notes,
            "metadata": json.dumps(self.metadata),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Order":
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get("items"), str):
            items_data = json.loads(data["items"]) if data["items"] else []
            data["items"] = [OrderItem.from_dict(item_data) for item_data in items_data]

        if isinstance(data.get("dropship_orders"), str):
            dropship_data = (
                json.loads(data["dropship_orders"]) if data["dropship_orders"] else []
            )
            data["dropship_orders"] = [
                DropshipOrder.from_dict(do_data) for do_data in dropship_data
            ]

        if isinstance(data.get("metadata"), str):
            data["metadata"] = json.loads(data["metadata"]) if data["metadata"] else {}

        # 处理时间字段
        time_fields = [
            "order_time",
            "payment_time",
            "shipping_time",
            "delivery_time",
            "created_at",
            "updated_at",
        ]
        for field in time_fields:
            if isinstance(data.get(field), str):
                data[field] = datetime.fromisoformat(data[field])

        return cls(**data)

    @classmethod
    def from_db_row(cls, row, columns: List[str]) -> Optional["Order"]:
        """从数据库行创建对象"""
        if not row:
            return None

        data = dict(zip(columns, row))
        return cls.from_dict(data)

    def validate(self) -> bool:
        """验证数据有效性"""
        if not self.customer_name:
            raise ValueError("客户姓名不能为空")

        if not self.shipping_address:
            raise ValueError("收货地址不能为空")

        if self.total_amount < 0:
            raise ValueError("订单金额不能为负数")

        if self.shipping_fee < 0:
            raise ValueError("运费不能为负数")

        if self.discount_amount < 0:
            raise ValueError("折扣金额不能为负数")

        if len(self.items) == 0:
            raise ValueError("订单必须包含至少一个商品")

        return True

    def __str__(self) -> str:
        return f"{self.order_id} - {self.customer_name} ({self.order_status})"

    def __repr__(self) -> str:
        return f"Order(order_id='{self.order_id}', customer='{self.customer_name}', status='{self.order_status}')"
