#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批次管理器

负责批次的CRUD操作、商品关联、状态管理等业务逻辑。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json

from core.database import DatabaseManager
from core.models.batch import Batch, BatchProduct
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class BatchManager(LoggerMixin):
    """批次管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化批次管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger.info("批次管理器初始化完成")
    
    @handle_errors(default_return=False)
    def create_batch(self, batch: Batch) -> bool:
        """
        创建批次
        
        Args:
            batch: 批次对象
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 验证批次数据
            batch.validate()
            
            # 检查批次编码是否重复
            if self.get_batch_by_code(batch.code):
                raise ValueError(f"批次编码 {batch.code} 已存在")
            
            # 插入批次基础信息
            sql = """
            INSERT INTO batches (
                batch_id, code, name, description, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            data = batch.to_dict()
            params = (
                data['batch_id'], data['code'], data['name'], data['description'],
                data['status'], data['created_at'], data['updated_at']
            )
            
            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"批次创建失败: {batch.name}")
                return False
            
            # 插入批次商品关联
            if batch.products:
                for batch_product in batch.products:
                    if not self._add_batch_product_to_db(batch.batch_id, batch_product):
                        self.logger.error(f"添加批次商品失败: {batch_product.product_id}")
                        return False
            
            self.logger.info(f"批次创建成功: {batch.batch_id} - {batch.name}")
            return True
                
        except Exception as e:
            self.logger.error(f"创建批次时出错: {e}")
            return False
    
    def _add_batch_product_to_db(self, batch_id: str, batch_product: BatchProduct) -> bool:
        """添加批次商品到数据库"""
        try:
            sql = """
            INSERT INTO batch_products (
                id, batch_id, product_id, quantity, unit_price, added_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            """
            
            import uuid
            bp_id = f"BP{uuid.uuid4().hex[:8].upper()}"
            
            params = (
                bp_id, batch_id, batch_product.product_id, batch_product.quantity,
                batch_product.unit_price, batch_product.added_at.isoformat()
            )
            
            cursor = self.db_manager.execute(sql, params)
            return cursor is not None
            
        except Exception as e:
            self.logger.error(f"添加批次商品到数据库失败: {e}")
            return False
    
    @handle_errors(default_return=None)
    def get_batch(self, batch_id: str) -> Optional[Batch]:
        """
        获取批次
        
        Args:
            batch_id: 批次ID
            
        Returns:
            Batch: 批次对象
        """
        # 获取批次基础信息
        sql = "SELECT * FROM batches WHERE batch_id = ?"
        row = self.db_manager.fetch_one(sql, (batch_id,))
        
        if not row:
            return None
        
        # 构建批次对象
        columns = [description[0] for description in self.db_manager.cursor.description]
        batch_data = dict(zip(columns, row))
        
        # 获取批次商品
        batch_products = self._get_batch_products(batch_id)
        batch_data['products'] = batch_products
        
        return Batch.from_dict(batch_data)
    
    def _get_batch_products(self, batch_id: str) -> List[BatchProduct]:
        """获取批次商品列表"""
        sql = """
        SELECT product_id, quantity, unit_price, added_at
        FROM batch_products 
        WHERE batch_id = ?
        ORDER BY added_at
        """
        
        rows = self.db_manager.fetch_all(sql, (batch_id,))
        batch_products = []
        
        if rows:
            for row in rows:
                batch_product = BatchProduct(
                    product_id=row[0],
                    quantity=row[1],
                    unit_price=row[2],
                    added_at=datetime.fromisoformat(row[3]) if row[3] else None
                )
                batch_products.append(batch_product)
        
        return batch_products
    
    @handle_errors(default_return=None)
    def get_batch_by_code(self, code: str) -> Optional[Batch]:
        """
        根据批次编码获取批次
        
        Args:
            code: 批次编码
            
        Returns:
            Batch: 批次对象
        """
        sql = "SELECT * FROM batches WHERE code = ?"
        row = self.db_manager.fetch_one(sql, (code,))
        
        if not row:
            return None
        
        columns = [description[0] for description in self.db_manager.cursor.description]
        batch_data = dict(zip(columns, row))
        
        # 获取批次商品
        batch_products = self._get_batch_products(batch_data['batch_id'])
        batch_data['products'] = batch_products
        
        return Batch.from_dict(batch_data)
    
    @handle_errors(default_return=[])
    def get_batches(self, status: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Batch]:
        """
        获取批次列表
        
        Args:
            status: 批次状态
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Batch]: 批次列表
        """
        sql = "SELECT * FROM batches WHERE 1=1"
        params = []
        
        if status:
            sql += " AND status = ?"
            params.append(status)
        
        sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = self.db_manager.fetch_all(sql, tuple(params))
        batches = []
        
        if rows:
            columns = [description[0] for description in self.db_manager.cursor.description]
            for row in rows:
                batch_data = dict(zip(columns, row))
                
                # 获取批次商品
                batch_products = self._get_batch_products(batch_data['batch_id'])
                batch_data['products'] = batch_products
                
                batch = Batch.from_dict(batch_data)
                batches.append(batch)
        
        return batches
    
    @handle_errors(default_return=[])
    def search_batches(self, keyword: str, limit: int = 100) -> List[Batch]:
        """
        搜索批次
        
        Args:
            keyword: 搜索关键词
            limit: 限制数量
            
        Returns:
            List[Batch]: 批次列表
        """
        sql = """
        SELECT * FROM batches 
        WHERE name LIKE ? OR code LIKE ? OR description LIKE ?
        ORDER BY created_at DESC LIMIT ?
        """
        
        search_term = f"%{keyword}%"
        params = (search_term, search_term, search_term, limit)
        
        rows = self.db_manager.fetch_all(sql, params)
        batches = []
        
        if rows:
            columns = [description[0] for description in self.db_manager.cursor.description]
            for row in rows:
                batch_data = dict(zip(columns, row))
                
                # 获取批次商品
                batch_products = self._get_batch_products(batch_data['batch_id'])
                batch_data['products'] = batch_products
                
                batch = Batch.from_dict(batch_data)
                batches.append(batch)
        
        return batches
    
    @handle_errors(default_return=False)
    def update_batch(self, batch: Batch) -> bool:
        """
        更新批次
        
        Args:
            batch: 批次对象
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证批次数据
            batch.validate()
            batch.updated_at = datetime.now()
            
            # 更新批次基础信息
            sql = """
            UPDATE batches SET
                name = ?, description = ?, status = ?, updated_at = ?
            WHERE batch_id = ?
            """
            
            data = batch.to_dict()
            params = (
                data['name'], data['description'], data['status'], 
                data['updated_at'], data['batch_id']
            )
            
            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"批次更新失败: {batch.name}")
                return False
            
            # 更新批次商品关联（先删除再重新插入）
            self._delete_batch_products(batch.batch_id)
            
            if batch.products:
                for batch_product in batch.products:
                    if not self._add_batch_product_to_db(batch.batch_id, batch_product):
                        self.logger.error(f"更新批次商品失败: {batch_product.product_id}")
                        return False
            
            self.logger.info(f"批次更新成功: {batch.batch_id} - {batch.name}")
            return True
                
        except Exception as e:
            self.logger.error(f"更新批次时出错: {e}")
            return False
    
    def _delete_batch_products(self, batch_id: str) -> bool:
        """删除批次商品关联"""
        try:
            sql = "DELETE FROM batch_products WHERE batch_id = ?"
            cursor = self.db_manager.execute(sql, (batch_id,))
            return cursor is not None
        except Exception as e:
            self.logger.error(f"删除批次商品关联失败: {e}")
            return False
    
    @handle_errors(default_return=False)
    def delete_batch(self, batch_id: str) -> bool:
        """
        删除批次
        
        Args:
            batch_id: 批次ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查批次是否存在
            batch = self.get_batch(batch_id)
            if not batch:
                self.logger.warning(f"要删除的批次不存在: {batch_id}")
                return False
            
            # 删除批次商品关联
            self._delete_batch_products(batch_id)
            
            # 删除批次
            sql = "DELETE FROM batches WHERE batch_id = ?"
            cursor = self.db_manager.execute(sql, (batch_id,))
            
            if cursor:
                self.logger.info(f"批次删除成功: {batch_id} - {batch.name}")
                return True
            else:
                self.logger.error(f"批次删除失败: {batch_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除批次时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def add_product_to_batch(self, batch_id: str, product_id: str, 
                           quantity: int, unit_price: float = 0.0) -> bool:
        """
        添加商品到批次
        
        Args:
            batch_id: 批次ID
            product_id: 商品ID
            quantity: 数量
            unit_price: 单价
            
        Returns:
            bool: 添加是否成功
        """
        try:
            batch = self.get_batch(batch_id)
            if not batch:
                self.logger.error(f"批次不存在: {batch_id}")
                return False
            
            # 添加商品到批次
            batch.add_product(product_id, quantity, unit_price)
            
            # 保存到数据库
            return self.update_batch(batch)
            
        except Exception as e:
            self.logger.error(f"添加商品到批次时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def remove_product_from_batch(self, batch_id: str, product_id: str) -> bool:
        """
        从批次中移除商品
        
        Args:
            batch_id: 批次ID
            product_id: 商品ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            batch = self.get_batch(batch_id)
            if not batch:
                self.logger.error(f"批次不存在: {batch_id}")
                return False
            
            # 从批次中移除商品
            if batch.remove_product(product_id):
                # 保存到数据库
                return self.update_batch(batch)
            else:
                self.logger.warning(f"商品不在批次中: {product_id}")
                return False
            
        except Exception as e:
            self.logger.error(f"从批次中移除商品时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def complete_batch(self, batch_id: str, reason: str = "") -> bool:
        """
        完成批次
        
        Args:
            batch_id: 批次ID
            reason: 完成原因
            
        Returns:
            bool: 操作是否成功
        """
        try:
            batch = self.get_batch(batch_id)
            if not batch:
                self.logger.error(f"批次不存在: {batch_id}")
                return False
            
            batch.complete_batch(reason)
            return self.update_batch(batch)
            
        except Exception as e:
            self.logger.error(f"完成批次时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def cancel_batch(self, batch_id: str, reason: str = "") -> bool:
        """
        取消批次
        
        Args:
            batch_id: 批次ID
            reason: 取消原因
            
        Returns:
            bool: 操作是否成功
        """
        try:
            batch = self.get_batch(batch_id)
            if not batch:
                self.logger.error(f"批次不存在: {batch_id}")
                return False
            
            batch.cancel_batch(reason)
            return self.update_batch(batch)
            
        except Exception as e:
            self.logger.error(f"取消批次时出错: {e}")
            return False
    
    @handle_errors(default_return=0)
    def get_batch_count(self, status: Optional[str] = None) -> int:
        """
        获取批次数量
        
        Args:
            status: 批次状态
            
        Returns:
            int: 批次数量
        """
        sql = "SELECT COUNT(*) FROM batches WHERE 1=1"
        params = []
        
        if status:
            sql += " AND status = ?"
            params.append(status)
        
        result = self.db_manager.fetch_one(sql, tuple(params))
        return result[0] if result else 0
    
    @handle_errors(default_return={})
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取批次统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            'total_batches': 0,
            'active_batches': 0,
            'completed_batches': 0,
            'cancelled_batches': 0,
            'total_value': 0.0
        }
        
        # 总批次数
        stats['total_batches'] = self.get_batch_count()
        
        # 按状态统计
        stats['active_batches'] = self.get_batch_count("活跃")
        stats['completed_batches'] = self.get_batch_count("已完成")
        stats['cancelled_batches'] = self.get_batch_count("已取消")
        
        # 总价值（活跃批次）
        sql = """
        SELECT SUM(bp.quantity * bp.unit_price)
        FROM batch_products bp
        JOIN batches b ON bp.batch_id = b.batch_id
        WHERE b.status = '活跃'
        """
        result = self.db_manager.fetch_one(sql)
        if result and result[0]:
            stats['total_value'] = float(result[0])
        
        return stats
