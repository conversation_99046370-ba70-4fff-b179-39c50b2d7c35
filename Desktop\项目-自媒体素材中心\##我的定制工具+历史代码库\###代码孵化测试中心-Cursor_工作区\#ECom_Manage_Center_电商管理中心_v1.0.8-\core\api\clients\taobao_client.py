#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝API客户端

实现淘宝开放平台API的对接功能。
"""

import hashlib
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode

from ..base_client import BaseAPIClient
from utils.error_handler import handle_errors


class TaobaoAPIClient(BaseAPIClient):
    """淘宝API客户端"""
    
    def __init__(self, platform_type: str, api_config: Dict[str, Any]):
        """
        初始化淘宝API客户端
        
        Args:
            platform_type: 平台类型
            api_config: API配置，包含app_key, app_secret, session等
        """
        super().__init__(platform_type, api_config)
        
        self.app_key = api_config.get('app_key')
        self.app_secret = api_config.get('app_secret')
        self.session_key = api_config.get('session_key')  # 用户授权token
        self.api_url = api_config.get('api_url', 'https://eco.taobao.com/router/rest')
        
        if not all([self.app_key, self.app_secret]):
            raise ValueError("淘宝API配置缺少必要参数: app_key, app_secret")
    
    def authenticate(self) -> bool:
        """
        进行API认证验证
        
        Returns:
            bool: 认证是否成功
        """
        try:
            # 调用一个简单的API来验证认证
            params = {
                'method': 'taobao.user.seller.get',
                'fields': 'nick'
            }
            
            response = self._call_api(params)
            
            if response and 'user_seller_get_response' in response:
                self.logger.info("淘宝API认证成功")
                return True
            else:
                self.logger.error("淘宝API认证失败")
                return False
                
        except Exception as e:
            self.logger.error(f"淘宝API认证异常: {e}")
            return False
    
    @handle_errors(default_return=[])
    def get_orders(self, start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None,
                   status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取订单列表
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status: 订单状态
            
        Returns:
            List[Dict[str, Any]]: 订单列表
        """
        params = {
            'method': 'taobao.trades.sold.get',
            'fields': 'tid,seller_nick,buyer_nick,title,type,created,modified,status,payment,discount_fee,adjust_fee,post_fee,total_fee,pay_time,end_time,modified,consign_time,buyer_obtain_point_fee,point_fee,real_point_fee,received_payment,commission_fee,pic_path,num_iid,num,price,cod_fee,cod_status,shipping_type,receiver_name,receiver_state,receiver_city,receiver_district,receiver_address,receiver_zip,receiver_mobile,receiver_phone',
            'page_no': 1,
            'page_size': 100
        }
        
        # 设置时间范围
        if start_time:
            params['start_created'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
        if end_time:
            params['end_created'] = end_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 设置订单状态
        if status:
            params['status'] = status
        
        response = self._call_api(params)
        
        if response and 'trades_sold_get_response' in response:
            trades_data = response['trades_sold_get_response']
            if 'trades' in trades_data and 'trade' in trades_data['trades']:
                return trades_data['trades']['trade']
        
        return []
    
    @handle_errors(default_return=None)
    def get_order_details(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        获取订单详情
        
        Args:
            order_id: 订单ID
            
        Returns:
            Optional[Dict[str, Any]]: 订单详情
        """
        params = {
            'method': 'taobao.trade.fullinfo.get',
            'tid': order_id,
            'fields': 'seller_nick,buyer_nick,title,type,created,sid,tid,seller_nick,buyer_nick,title,type,created,iid,num,price,cod_fee,payment,discount_fee,adjust_fee,post_fee,total_fee,pay_time,end_time,modified,consign_time,buyer_obtain_point_fee,point_fee,real_point_fee,received_payment,commission_fee,pic_path,num_iid,num,price,cod_fee,cod_status,shipping_type,orders.title,orders.pic_path,orders.price,orders.num,orders.iid,orders.sku_id,orders.sku_properties_name,orders.item_meal_name,orders.buyer_rate,orders.seller_rate,orders.outer_iid,orders.outer_sku_id,orders.refund_status,orders.snapshot_url,orders.timeout_action_time,orders.buyer_rate,orders.seller_rate'
        }
        
        response = self._call_api(params)
        
        if response and 'trade_fullinfo_get_response' in response:
            return response['trade_fullinfo_get_response'].get('trade')
        
        return None
    
    @handle_errors(default_return=False)
    def update_inventory(self, product_id: str, quantity: int) -> bool:
        """
        更新商品库存
        
        Args:
            product_id: 平台商品ID
            quantity: 库存数量
            
        Returns:
            bool: 更新是否成功
        """
        params = {
            'method': 'taobao.item.quantity.update',
            'num_iid': product_id,
            'quantity': quantity
        }
        
        response = self._call_api(params)
        
        if response and 'item_quantity_update_response' in response:
            return response['item_quantity_update_response'].get('item') is not None
        
        return False
    
    @handle_errors(default_return=None)
    def get_product_info(self, product_id: str) -> Optional[Dict[str, Any]]:
        """
        获取商品信息
        
        Args:
            product_id: 平台商品ID
            
        Returns:
            Optional[Dict[str, Any]]: 商品信息
        """
        params = {
            'method': 'taobao.item.get',
            'num_iid': product_id,
            'fields': 'num_iid,title,nick,type,cid,seller_cids,props,input_pids,input_str,desc,pic_url,num,valid_thru,list_time,delist_time,stuff_status,location,price,post_fee,express_fee,ems_fee,has_discount,freight_payer,has_invoice,has_warranty,has_showcase,modified,increment,approve_status,postage_id,product_id,auction_point,property_alias,item_img,prop_img,sku,video,outer_id'
        }
        
        response = self._call_api(params)
        
        if response and 'item_get_response' in response:
            return response['item_get_response'].get('item')
        
        return None
    
    def _call_api(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        调用淘宝API
        
        Args:
            params: API参数
            
        Returns:
            Optional[Dict[str, Any]]: API响应
        """
        # 添加公共参数
        common_params = {
            'app_key': self.app_key,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'format': 'json',
            'v': '2.0',
            'sign_method': 'md5'
        }
        
        if self.session_key:
            common_params['session'] = self.session_key
        
        # 合并参数
        all_params = {**common_params, **params}
        
        # 生成签名
        all_params['sign'] = self._generate_sign(all_params)
        
        # 发起请求
        return self.make_request('POST', self.api_url, data=all_params)
    
    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """
        生成API签名
        
        Args:
            params: 请求参数
            
        Returns:
            str: 签名字符串
        """
        # 排序参数
        sorted_params = sorted(params.items())
        
        # 拼接字符串
        sign_string = self.app_secret
        for key, value in sorted_params:
            if key != 'sign':
                sign_string += f"{key}{value}"
        sign_string += self.app_secret
        
        # MD5加密
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
