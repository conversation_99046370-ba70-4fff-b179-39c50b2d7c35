#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代发管理模块主界面

代发管理功能的完整实现。
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QSplitter,
    QGroupBox,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QLineEdit,
    QComboBox,
    QLabel,
    QMessageBox,
    QHeaderView,
    QDateEdit,
    QToolBar,
    QFrame,
    QTextEdit,
    QProgressBar,
    QGridLayout,
    QScrollArea,
    QSpinBox,
    QDoubleSpinBox,
    QCheckBox,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt6.QtGui import QFont, QPalette, QColor

from utils.logger import LoggerMixin


class DropshipWidget(QWidget, LoggerMixin):
    """代发管理主界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager

        # 当前选中的供应商
        self.current_supplier = None

        # 示例数据
        self.suppliers_data = []
        self.orders_data = []

        # 初始化界面
        self.init_ui()

        # 加载示例数据
        self.load_sample_data()

        self.logger.info("代发管理界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题区域
        title_frame = self.create_title_section()
        layout.addWidget(title_frame)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：统计卡片
        stats_frame = self.create_stats_section()
        main_splitter.addWidget(stats_frame)

        # 下半部分：订单管理
        order_frame = self.create_order_section()
        main_splitter.addWidget(order_frame)

        # 设置分割器比例
        main_splitter.setSizes([200, 500])
        layout.addWidget(main_splitter)

    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet(
            """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF9800, stop:1 #F57C00);
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """
        )

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 10, 20, 10)

        # 标题
        title = QLabel("🚚 代发管理系统")
        title.setStyleSheet(
            """
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
        """
        )
        layout.addWidget(title)

        layout.addStretch()

        # 操作按钮
        btn_layout = QHBoxLayout()

        add_btn = QPushButton("新建订单")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """
        )
        add_btn.clicked.connect(self.add_order)
        btn_layout.addWidget(add_btn)

        edit_btn = QPushButton("编辑订单")
        edit_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """
        )
        edit_btn.clicked.connect(self.edit_order)
        btn_layout.addWidget(edit_btn)

        delete_btn = QPushButton("删除订单")
        delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(244, 67, 54, 0.8);
                color: white;
                border: 1px solid rgba(244, 67, 54, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(244, 67, 54, 1.0);
            }
        """
        )
        delete_btn.clicked.connect(self.delete_order)
        btn_layout.addWidget(delete_btn)

        layout.addLayout(btn_layout)

        return frame

    def create_stats_section(self):
        """创建统计区域"""
        frame = QGroupBox("📊 订单统计")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #FF9800;
            }
        """
        )

        layout = QHBoxLayout(frame)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)

        # 统计数据
        stats_data = [
            ("待处理订单", "12", "#FF9800"),
            ("运输中", "8", "#2196F3"),
            ("已完成", "156", "#4CAF50"),
            ("总利润", "¥12,340", "#4CAF50"),
        ]

        for title, value, color in stats_data:
            stat_widget = self.create_simple_stat(title, value, color)
            layout.addWidget(stat_widget)

        return frame

    def create_simple_stat(self, title, value, color):
        """创建简单的统计显示"""
        widget = QFrame()
        widget.setStyleSheet(
            f"""
            QFrame {{
                background-color: #1e1e1e;
                border: 1px solid {color};
                border-radius: 6px;
                padding: 8px;
            }}
        """
        )

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(
            f"font-size: 12px; color: {color}; font-weight: bold; background: transparent;"
        )
        layout.addWidget(title_label)

        # 数值
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet(
            f"font-size: 18px; font-weight: bold; color: white; background: transparent;"
        )
        layout.addWidget(value_label)

        return widget

    def create_order_section(self):
        """创建订单管理区域"""
        frame = QGroupBox("📋 代发订单管理")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #FF9800;
            }
        """
        )

        layout = QVBoxLayout(frame)

        # 搜索栏
        search_layout = QHBoxLayout()

        search_label = QLabel("搜索:")
        search_label.setStyleSheet("color: white; font-weight: bold;")
        search_layout.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入订单号、商品名称或供应商...")
        self.search_edit.setStyleSheet(
            """
            QLineEdit {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                padding: 6px;
            }
        """
        )
        search_layout.addWidget(self.search_edit)

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """
        )
        search_btn.clicked.connect(self.search_orders)
        search_layout.addWidget(search_btn)

        layout.addLayout(search_layout)

        # 订单表格
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels(
            ["订单号", "商品名称", "供应商", "数量", "成本", "售价", "利润", "状态"]
        )

        # 设置表格样式
        self.orders_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                gridline-color: #555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QTableWidget::item:selected {
                background-color: #FF9800;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """
        )

        # 设置表格属性
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.orders_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        header = self.orders_table.horizontalHeader()
        header.setStretchLastSection(True)

        # 连接选择事件
        self.orders_table.itemSelectionChanged.connect(self.on_order_selected)

        layout.addWidget(self.orders_table)

        return frame

    def load_sample_data(self):
        """加载示例数据"""
        # 示例订单数据
        sample_orders = [
            [
                "ORD001",
                "iPhone 14 手机壳",
                "阿里巴巴供应商A",
                "50",
                "¥15.00",
                "¥25.00",
                "¥10.00",
                "待处理",
            ],
            [
                "ORD002",
                "蓝牙耳机",
                "1688供应商B",
                "30",
                "¥45.00",
                "¥89.00",
                "¥44.00",
                "运输中",
            ],
            [
                "ORD003",
                "数据线",
                "淘宝供应商C",
                "100",
                "¥8.00",
                "¥18.00",
                "¥10.00",
                "已完成",
            ],
            [
                "ORD004",
                "充电宝",
                "阿里巴巴供应商D",
                "25",
                "¥35.00",
                "¥68.00",
                "¥33.00",
                "待处理",
            ],
            [
                "ORD005",
                "手机支架",
                "1688供应商E",
                "80",
                "¥12.00",
                "¥22.00",
                "¥10.00",
                "运输中",
            ],
        ]

        self.orders_table.setRowCount(len(sample_orders))

        for row, order_data in enumerate(sample_orders):
            for col, data in enumerate(order_data):
                item = QTableWidgetItem(str(data))

                # 根据状态设置颜色
                if col == 7:  # 状态列
                    if data == "待处理":
                        item.setBackground(QColor("#FF9800"))
                    elif data == "运输中":
                        item.setBackground(QColor("#2196F3"))
                    elif data == "已完成":
                        item.setBackground(QColor("#4CAF50"))

                self.orders_table.setItem(row, col, item)

    def on_order_selected(self):
        """订单选择事件"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            order_id = self.orders_table.item(current_row, 0).text()
            self.logger.info(f"选中订单: {order_id}")

    def add_order(self):
        """添加新订单"""
        from PyQt6.QtWidgets import (
            QDialog,
            QFormLayout,
            QDialogButtonBox,
            QSpinBox,
            QDoubleSpinBox,
        )

        dialog = QDialog(self)
        dialog.setWindowTitle("新建代发订单")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QFormLayout(dialog)

        # 订单信息输入
        order_id_edit = QLineEdit(f"ORD{self.orders_table.rowCount() + 1:03d}")
        layout.addRow("订单号:", order_id_edit)

        product_edit = QLineEdit()
        layout.addRow("商品名称:", product_edit)

        supplier_edit = QLineEdit()
        layout.addRow("供应商:", supplier_edit)

        quantity_spin = QSpinBox()
        quantity_spin.setMinimum(1)
        quantity_spin.setMaximum(9999)
        quantity_spin.setValue(1)
        layout.addRow("数量:", quantity_spin)

        cost_spin = QDoubleSpinBox()
        cost_spin.setMinimum(0.01)
        cost_spin.setMaximum(99999.99)
        cost_spin.setDecimals(2)
        cost_spin.setPrefix("¥")
        layout.addRow("成本:", cost_spin)

        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0.01)
        price_spin.setMaximum(99999.99)
        price_spin.setDecimals(2)
        price_spin.setPrefix("¥")
        layout.addRow("售价:", price_spin)

        status_combo = QComboBox()
        status_combo.addItems(["待处理", "运输中", "已完成"])
        layout.addRow("状态:", status_combo)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加到表格
            row_count = self.orders_table.rowCount()
            self.orders_table.insertRow(row_count)

            # 计算利润
            cost = cost_spin.value()
            price = price_spin.value()
            profit = price - cost

            # 填充数据
            items = [
                order_id_edit.text(),
                product_edit.text(),
                supplier_edit.text(),
                str(quantity_spin.value()),
                f"¥{cost:.2f}",
                f"¥{price:.2f}",
                f"¥{profit:.2f}",
                status_combo.currentText(),
            ]

            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                # 根据状态设置颜色
                if col == 7:  # 状态列
                    if item_text == "待处理":
                        item.setBackground(QColor("#FF9800"))
                    elif item_text == "运输中":
                        item.setBackground(QColor("#2196F3"))
                    elif item_text == "已完成":
                        item.setBackground(QColor("#4CAF50"))

                self.orders_table.setItem(row_count, col, item)

            QMessageBox.information(self, "成功", "订单添加成功！")

    def edit_order(self):
        """编辑订单"""
        current_row = self.orders_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的订单")
            return

        from PyQt6.QtWidgets import (
            QDialog,
            QFormLayout,
            QDialogButtonBox,
            QSpinBox,
            QDoubleSpinBox,
        )

        dialog = QDialog(self)
        dialog.setWindowTitle("编辑代发订单")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QFormLayout(dialog)

        # 获取当前数据
        order_id = self.orders_table.item(current_row, 0).text()
        product = self.orders_table.item(current_row, 1).text()
        supplier = self.orders_table.item(current_row, 2).text()
        quantity = int(self.orders_table.item(current_row, 3).text())
        cost = float(self.orders_table.item(current_row, 4).text().replace("¥", ""))
        price = float(self.orders_table.item(current_row, 5).text().replace("¥", ""))
        status = self.orders_table.item(current_row, 7).text()

        # 创建编辑控件
        order_id_edit = QLineEdit(order_id)
        order_id_edit.setReadOnly(True)
        layout.addRow("订单号:", order_id_edit)

        product_edit = QLineEdit(product)
        layout.addRow("商品名称:", product_edit)

        supplier_edit = QLineEdit(supplier)
        layout.addRow("供应商:", supplier_edit)

        quantity_spin = QSpinBox()
        quantity_spin.setMinimum(1)
        quantity_spin.setMaximum(9999)
        quantity_spin.setValue(quantity)
        layout.addRow("数量:", quantity_spin)

        cost_spin = QDoubleSpinBox()
        cost_spin.setMinimum(0.01)
        cost_spin.setMaximum(99999.99)
        cost_spin.setDecimals(2)
        cost_spin.setPrefix("¥")
        cost_spin.setValue(cost)
        layout.addRow("成本:", cost_spin)

        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0.01)
        price_spin.setMaximum(99999.99)
        price_spin.setDecimals(2)
        price_spin.setPrefix("¥")
        price_spin.setValue(price)
        layout.addRow("售价:", price_spin)

        status_combo = QComboBox()
        status_combo.addItems(["待处理", "运输中", "已完成"])
        status_combo.setCurrentText(status)
        layout.addRow("状态:", status_combo)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 更新表格数据
            new_cost = cost_spin.value()
            new_price = price_spin.value()
            new_profit = new_price - new_cost

            items = [
                order_id_edit.text(),
                product_edit.text(),
                supplier_edit.text(),
                str(quantity_spin.value()),
                f"¥{new_cost:.2f}",
                f"¥{new_price:.2f}",
                f"¥{new_profit:.2f}",
                status_combo.currentText(),
            ]

            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                # 根据状态设置颜色
                if col == 7:  # 状态列
                    if item_text == "待处理":
                        item.setBackground(QColor("#FF9800"))
                    elif item_text == "运输中":
                        item.setBackground(QColor("#2196F3"))
                    elif item_text == "已完成":
                        item.setBackground(QColor("#4CAF50"))

                self.orders_table.setItem(current_row, col, item)

            QMessageBox.information(self, "成功", "订单更新成功！")

    def delete_order(self):
        """删除订单"""
        current_row = self.orders_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的订单")
            return

        order_id = self.orders_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除订单 {order_id} 吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.orders_table.removeRow(current_row)
            QMessageBox.information(self, "成功", "订单删除成功！")

    def search_orders(self):
        """搜索订单"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            # 显示所有行
            for row in range(self.orders_table.rowCount()):
                self.orders_table.setRowHidden(row, False)
            return

        # 搜索匹配的行
        for row in range(self.orders_table.rowCount()):
            match_found = False
            for col in range(self.orders_table.columnCount()):
                item = self.orders_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break

            self.orders_table.setRowHidden(row, not match_found)
