#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本

测试数据库连接、数据模型、管理器等基础功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database():
    """测试数据库功能"""
    print("=" * 50)
    print("测试数据库功能")
    print("=" * 50)
    
    try:
        from core.database import DatabaseManager
        
        # 创建数据库管理器
        db_manager = DatabaseManager("test_data/test.db")
        
        # 初始化数据库
        if db_manager.initialize():
            print("✅ 数据库初始化成功")
        else:
            print("❌ 数据库初始化失败")
            return False
        
        # 测试基本查询
        result = db_manager.fetch_one("SELECT COUNT(*) FROM platforms")
        if result:
            print(f"✅ 数据库查询成功，平台数量: {result[0]}")
        else:
            print("❌ 数据库查询失败")
            return False
        
        # 关闭数据库
        db_manager.close()
        print("✅ 数据库连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_models():
    """测试数据模型"""
    print("\n" + "=" * 50)
    print("测试数据模型")
    print("=" * 50)
    
    try:
        from core.models.product import Product
        from core.models.batch import Batch
        from core.models.supplier import Supplier
        from decimal import Decimal
        
        # 测试商品模型
        product = Product(
            name="测试商品",
            category="测试分类",
            quantity=100,
            purchase_price=Decimal('50.00'),
            selling_price=Decimal('80.00')
        )
        
        # 计算利润
        profit = product.calculate_profit()
        print(f"✅ 商品模型测试成功，利润: ¥{profit}")
        
        # 测试批次模型
        batch = Batch(
            code="TEST001",
            name="测试批次"
        )
        
        batch.add_product(product.product_id, 50, 50.0)
        print(f"✅ 批次模型测试成功，商品数量: {batch.total_products}")
        
        # 测试供应商模型
        supplier = Supplier(
            supplier_name="测试供应商",
            credit_rating=8
        )
        
        print(f"✅ 供应商模型测试成功，信用评级: {supplier.credit_rating}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False


def test_managers():
    """测试管理器"""
    print("\n" + "=" * 50)
    print("测试管理器")
    print("=" * 50)
    
    try:
        from core.database import DatabaseManager
        from core.managers.product_manager import ProductManager
        from core.models.product import Product
        from decimal import Decimal
        
        # 创建数据库管理器
        db_manager = DatabaseManager("test_data/test.db")
        db_manager.initialize()
        
        # 创建商品管理器
        product_manager = ProductManager(db_manager)
        
        # 创建测试商品
        test_product = Product(
            name="管理器测试商品",
            category="测试分类",
            quantity=50,
            purchase_price=Decimal('30.00'),
            selling_price=Decimal('50.00')
        )
        
        # 测试创建商品
        if product_manager.create_product(test_product):
            print("✅ 商品创建成功")
        else:
            print("❌ 商品创建失败")
            return False
        
        # 测试获取商品
        retrieved_product = product_manager.get_product(test_product.product_id)
        if retrieved_product:
            print(f"✅ 商品获取成功: {retrieved_product.name}")
        else:
            print("❌ 商品获取失败")
            return False
        
        # 测试搜索商品
        search_results = product_manager.search_products("管理器")
        if search_results:
            print(f"✅ 商品搜索成功，找到 {len(search_results)} 个商品")
        else:
            print("❌ 商品搜索失败")
        
        # 测试统计信息
        stats = product_manager.get_statistics()
        print(f"✅ 统计信息获取成功，总商品数: {stats.get('total_products', 0)}")
        
        # 清理测试数据
        product_manager.delete_product(test_product.product_id)
        print("✅ 测试数据清理完成")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 管理器测试失败: {e}")
        return False


def test_config():
    """测试配置管理"""
    print("\n" + "=" * 50)
    print("测试配置管理")
    print("=" * 50)
    
    try:
        from core.config import Config
        
        # 创建配置管理器
        config = Config("test_data/test_config.json")
        
        # 测试设置和获取配置
        config.set("test.key", "test_value")
        value = config.get("test.key")
        
        if value == "test_value":
            print("✅ 配置设置和获取成功")
        else:
            print("❌ 配置设置和获取失败")
            return False
        
        # 测试嵌套配置
        config.set("nested.deep.key", 123)
        nested_value = config.get("nested.deep.key")
        
        if nested_value == 123:
            print("✅ 嵌套配置测试成功")
        else:
            print("❌ 嵌套配置测试失败")
            return False
        
        # 测试默认配置
        default_theme = config.get("ui.theme", "light")
        print(f"✅ 默认配置获取成功，主题: {default_theme}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n" + "=" * 50)
    print("测试日志系统")
    print("=" * 50)
    
    try:
        from utils.logger import setup_logger
        
        # 创建日志记录器
        logger = setup_logger(
            name="TestLogger",
            log_level="INFO",
            log_file="test_data/test.log",
            console_output=True
        )
        
        # 测试不同级别的日志
        logger.debug("这是调试信息")
        logger.info("这是信息日志")
        logger.warning("这是警告日志")
        logger.error("这是错误日志")
        
        print("✅ 日志系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始基础功能测试")
    print("测试时间:", __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 创建测试目录
    test_dir = Path("test_data")
    test_dir.mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("配置管理", test_config),
        ("日志系统", test_logger),
        ("数据库功能", test_database),
        ("数据模型", test_models),
        ("管理器", test_managers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！基础架构运行正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
    
    sys.exit(exit_code)
