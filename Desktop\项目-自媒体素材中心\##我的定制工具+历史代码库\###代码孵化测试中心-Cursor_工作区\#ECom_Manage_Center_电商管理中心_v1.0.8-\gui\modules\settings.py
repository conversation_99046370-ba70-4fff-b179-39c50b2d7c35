#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置模块界面

系统设置和配置功能的主界面。
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from .settings.settings_widget import SettingsWidget as SettingsMainWidget
from .development_widget import DevelopmentWidget


class SettingsWidget(QWidget):
    """系统设置界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """
        )

        # 系统设置标签页 - 完整功能
        settings_widget = SettingsMainWidget(self.db_manager, self)
        tab_widget.addTab(settings_widget, "⚙️ 系统设置")

        # 用户管理标签页 - 开发中
        user_info = {
            "name": "用户管理功能",
            "icon": "👤",
            "description": "User Management & Permissions",
            "progress": 20,
            "eta": "2-3周",
            "features": [
                "👤 用户账户管理",
                "   • 用户注册和登录",
                "   • 用户信息管理",
                "   • 密码安全策略",
                "   • 账户状态管理",
                "",
                "🔐 权限控制",
                "   • 角色权限管理",
                "   • 功能访问控制",
                "   • 数据权限分离",
                "   • 操作审计",
                "",
                "📋 操作日志",
                "   • 用户操作记录",
                "   • 登录日志",
                "   • 权限变更记录",
                "   • 安全事件监控",
            ],
            "phases": [
                ("第一阶段", "基础用户管理", "⏳ 计划中", "#666"),
                ("第二阶段", "权限控制系统", "⏳ 计划中", "#666"),
                ("第三阶段", "安全审计功能", "⏳ 计划中", "#666"),
            ],
        }
        user_widget = DevelopmentWidget(user_info, self)
        tab_widget.addTab(user_widget, "👤 用户管理")

        # 系统监控标签页 - 开发中
        monitor_info = {
            "name": "系统监控功能",
            "icon": "📊",
            "description": "System Monitoring & Performance",
            "progress": 30,
            "eta": "1-2周",
            "features": [
                "📊 性能监控",
                "   • CPU和内存使用率",
                "   • 数据库性能监控",
                "   • API响应时间",
                "   • 系统负载分析",
                "",
                "🚨 告警系统",
                "   • 性能阈值告警",
                "   • 错误率监控",
                "   • 自动故障检测",
                "   • 邮件/短信通知",
                "",
                "📈 统计报表",
                "   • 系统使用统计",
                "   • 性能趋势分析",
                "   • 错误统计报告",
                "   • 用户行为分析",
            ],
            "phases": [
                ("第一阶段", "基础性能监控", "🔄 进行中", "#FF9800"),
                ("第二阶段", "告警系统", "⏳ 计划中", "#666"),
                ("第三阶段", "统计报表", "⏳ 计划中", "#666"),
            ],
        }
        monitor_widget = DevelopmentWidget(monitor_info, self)
        tab_widget.addTab(monitor_widget, "📊 系统监控")

        layout.addWidget(tab_widget)
