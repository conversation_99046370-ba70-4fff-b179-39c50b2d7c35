#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库测试脚本

专门测试数据库创建和初始化功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_creation():
    """测试数据库创建"""
    print("🗄️ 测试数据库创建...")
    
    try:
        from core.database import DatabaseManager
        
        # 删除现有数据库文件
        db_file = Path("data/test_db.db")
        if db_file.exists():
            db_file.unlink()
            print("✅ 删除现有数据库文件")
        
        # 创建数据库管理器
        db_manager = DatabaseManager(str(db_file))
        
        # 测试连接
        if not db_manager.connect():
            print("❌ 数据库连接失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 测试内置表创建
        if not db_manager._create_tables_inline():
            print("❌ 内置表创建失败")
            return False
        
        print("✅ 内置表创建成功")
        
        # 测试表是否存在
        tables_to_check = [
            'platforms', 'stores', 'products', 'batches', 
            'batch_products', 'suppliers', 'transactions', 'system_settings'
        ]
        
        for table in tables_to_check:
            sql = f"SELECT COUNT(*) FROM {table}"
            result = db_manager.fetch_one(sql)
            if result is not None:
                print(f"✅ 表 {table} 存在")
            else:
                print(f"❌ 表 {table} 不存在")
                return False
        
        # 测试插入初始数据
        if not db_manager.insert_initial_data():
            print("❌ 插入初始数据失败")
            return False
        
        print("✅ 插入初始数据成功")
        
        # 关闭数据库
        db_manager.close()
        print("✅ 数据库关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_manager():
    """测试商品管理器"""
    print("\n📦 测试商品管理器...")
    
    try:
        from core.database import DatabaseManager
        from core.managers.product_manager import ProductManager
        from core.models.product import Product
        from decimal import Decimal
        
        # 连接数据库
        db_manager = DatabaseManager("data/test_db.db")
        if not db_manager.connect():
            print("❌ 数据库连接失败")
            return False
        
        # 创建商品管理器
        product_manager = ProductManager(db_manager)
        
        # 创建测试商品
        test_product = Product(
            name="测试商品",
            category="测试分类",
            quantity=100,
            purchase_price=Decimal('50.00'),
            selling_price=Decimal('80.00')
        )
        
        # 测试创建商品
        if product_manager.create_product(test_product):
            print("✅ 商品创建成功")
        else:
            print("❌ 商品创建失败")
            return False
        
        # 测试获取商品
        retrieved_product = product_manager.get_product(test_product.product_id)
        if retrieved_product:
            print(f"✅ 商品获取成功: {retrieved_product.name}")
        else:
            print("❌ 商品获取失败")
            return False
        
        # 测试获取分类
        categories = product_manager.get_categories()
        print(f"✅ 获取分类成功: {categories}")
        
        # 测试库存更新
        if product_manager.update_stock(test_product.product_id, 20, "测试入库"):
            print("✅ 库存更新成功")
        else:
            print("❌ 库存更新失败")
            return False
        
        # 测试统计
        stats = product_manager.get_statistics()
        print(f"✅ 统计信息: {stats}")
        
        # 清理测试数据
        if product_manager.delete_product(test_product.product_id):
            print("✅ 测试数据清理成功")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 商品管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 数据库专项测试")
    print("=" * 40)
    
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("数据库创建", test_database_creation),
        ("商品管理器", test_product_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 40)
    print("测试结果汇总")
    print("=" * 40)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！数据库功能正常。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
    
    sys.exit(exit_code)
