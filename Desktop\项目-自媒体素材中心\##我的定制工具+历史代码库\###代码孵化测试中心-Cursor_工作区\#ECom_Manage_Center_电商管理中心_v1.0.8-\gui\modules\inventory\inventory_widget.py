#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
库存管理主界面

整合商品管理、批次管理等功能的主界面。
"""

import logging
from typing import Optional, List
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QPushButton,
    QLineEdit,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QMessageBox,
    QSplitter,
    QGroupBox,
    QLabel,
    QSpinBox,
    QDoubleSpinBox,
    QTextEdit,
    QFileDialog,
    QProgressBar,
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QPixmap, QIcon

from core.database import DatabaseManager
from core.managers.product_manager import ProductManager
from core.managers.batch_manager import BatchManager
from core.models.product import Product
from core.models.batch import Batch
from utils.logger import LoggerMixin


class InventoryWidget(QWidget, LoggerMixin):
    """库存管理主界面"""

    # 信号定义
    status_message = pyqtSignal(str)
    progress_update = pyqtSignal(int)

    def __init__(self, db_manager: DatabaseManager, parent=None):
        """
        初始化库存管理界面

        Args:
            db_manager: 数据库管理器
            parent: 父窗口
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.product_manager = ProductManager(db_manager)
        self.batch_manager = BatchManager(db_manager)

        # 数据
        self.products = []
        self.batches = []
        self.current_product = None
        self.current_batch = None

        # 初始化界面
        self.init_ui()
        self.load_data()

        self.logger.info("库存管理界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 商品管理页面
        self.product_tab = self.create_product_tab()
        self.tab_widget.addTab(self.product_tab, "📦 商品管理")

        # 批次管理页面
        self.batch_tab = self.create_batch_tab()
        self.tab_widget.addTab(self.batch_tab, "📋 批次管理")

        # 库存统计页面
        self.stats_tab = self.create_stats_tab()
        self.tab_widget.addTab(self.stats_tab, "📊 库存统计")

        layout.addWidget(self.tab_widget)

    def create_product_tab(self) -> QWidget:
        """创建商品管理页面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        # 搜索框
        self.product_search_edit = QLineEdit()
        self.product_search_edit.setPlaceholderText("搜索商品名称、编码、分类...")
        self.product_search_edit.textChanged.connect(self.search_products)
        toolbar_layout.addWidget(QLabel("搜索:"))
        toolbar_layout.addWidget(self.product_search_edit)

        # 分类筛选
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部分类", "")
        self.category_combo.currentTextChanged.connect(self.filter_products)
        toolbar_layout.addWidget(QLabel("分类:"))
        toolbar_layout.addWidget(self.category_combo)

        # 状态筛选
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部状态", "在库", "缺货", "停售", "预售"])
        self.status_combo.currentTextChanged.connect(self.filter_products)
        toolbar_layout.addWidget(QLabel("状态:"))
        toolbar_layout.addWidget(self.status_combo)

        toolbar_layout.addStretch()

        # 操作按钮
        self.add_product_btn = QPushButton("➕ 添加商品")
        self.add_product_btn.clicked.connect(self.add_product)
        toolbar_layout.addWidget(self.add_product_btn)

        self.edit_product_btn = QPushButton("✏️ 编辑商品")
        self.edit_product_btn.clicked.connect(self.edit_product)
        self.edit_product_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_product_btn)

        self.delete_product_btn = QPushButton("🗑️ 删除商品")
        self.delete_product_btn.clicked.connect(self.delete_product)
        self.delete_product_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_product_btn)

        # 库存操作按钮
        self.stock_in_btn = QPushButton("📥 入库")
        self.stock_in_btn.clicked.connect(self.stock_in)
        self.stock_in_btn.setEnabled(False)
        toolbar_layout.addWidget(self.stock_in_btn)

        self.stock_out_btn = QPushButton("📤 出库")
        self.stock_out_btn.clicked.connect(self.stock_out)
        self.stock_out_btn.setEnabled(False)
        toolbar_layout.addWidget(self.stock_out_btn)

        # 导入导出按钮
        self.import_btn = QPushButton("📂 导入")
        self.import_btn.clicked.connect(self.import_products)
        toolbar_layout.addWidget(self.import_btn)

        self.export_btn = QPushButton("💾 导出")
        self.export_btn.clicked.connect(self.export_products)
        toolbar_layout.addWidget(self.export_btn)

        layout.addLayout(toolbar_layout)

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 商品列表
        self.product_table = QTableWidget()
        self.setup_product_table()
        splitter.addWidget(self.product_table)

        # 商品详情
        self.product_detail_widget = self.create_product_detail_widget()
        splitter.addWidget(self.product_detail_widget)

        # 设置分割器比例
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)

        return widget

    def setup_product_table(self):
        """设置商品表格"""
        headers = [
            "商品ID",
            "商品名称",
            "编码",
            "分类",
            "当前库存",
            "安全库存",
            "单位",
            "采购价",
            "销售价",
            "利润率",
            "库存价值",
            "状态",
            "供应商",
            "最后入库",
            "创建时间",
        ]

        self.product_table.setColumnCount(len(headers))
        self.product_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.product_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.product_table.setAlternatingRowColors(True)
        self.product_table.setSortingEnabled(True)

        # 设置列宽
        header = self.product_table.horizontalHeader()
        header.setSectionResizeMode(
            1, QHeaderView.ResizeMode.Stretch
        )  # 商品名称列自适应

        # 连接选择信号
        self.product_table.itemSelectionChanged.connect(
            self.on_product_selection_changed
        )
        self.product_table.itemDoubleClicked.connect(self.edit_product)

    def create_product_detail_widget(self) -> QWidget:
        """创建商品详情控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题
        title_label = QLabel("商品详情")
        title_label.setStyleSheet(
            "font-size: 14px; font-weight: bold; margin-bottom: 10px;"
        )
        layout.addWidget(title_label)

        # 基础信息组
        basic_group = QGroupBox("基础信息")
        basic_layout = QVBoxLayout(basic_group)

        self.detail_name_label = QLabel("商品名称: -")
        self.detail_code_label = QLabel("商品编码: -")
        self.detail_category_label = QLabel("商品分类: -")
        self.detail_status_label = QLabel("商品状态: -")

        basic_layout.addWidget(self.detail_name_label)
        basic_layout.addWidget(self.detail_code_label)
        basic_layout.addWidget(self.detail_category_label)
        basic_layout.addWidget(self.detail_status_label)

        layout.addWidget(basic_group)

        # 库存信息组
        stock_group = QGroupBox("库存信息")
        stock_layout = QVBoxLayout(stock_group)

        self.detail_quantity_label = QLabel("当前库存: -")
        self.detail_unit_label = QLabel("计量单位: -")
        self.detail_location_label = QLabel("存放位置: -")

        stock_layout.addWidget(self.detail_quantity_label)
        stock_layout.addWidget(self.detail_unit_label)
        stock_layout.addWidget(self.detail_location_label)

        layout.addWidget(stock_group)

        # 价格信息组
        price_group = QGroupBox("价格信息")
        price_layout = QVBoxLayout(price_group)

        self.detail_purchase_price_label = QLabel("采购价: -")
        self.detail_selling_price_label = QLabel("销售价: -")
        self.detail_profit_label = QLabel("预估利润: -")
        self.detail_profit_margin_label = QLabel("利润率: -")

        price_layout.addWidget(self.detail_purchase_price_label)
        price_layout.addWidget(self.detail_selling_price_label)
        price_layout.addWidget(self.detail_profit_label)
        price_layout.addWidget(self.detail_profit_margin_label)

        layout.addWidget(price_group)

        # 供应商信息组
        supplier_group = QGroupBox("供应商信息")
        supplier_layout = QVBoxLayout(supplier_group)

        self.detail_supplier_label = QLabel("供应商: -")
        self.detail_supplier_link_label = QLabel("供应商链接: -")

        supplier_layout.addWidget(self.detail_supplier_label)
        supplier_layout.addWidget(self.detail_supplier_link_label)

        layout.addWidget(supplier_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.stock_in_btn = QPushButton("📥 入库")
        self.stock_in_btn.clicked.connect(self.stock_in)
        self.stock_in_btn.setEnabled(False)
        button_layout.addWidget(self.stock_in_btn)

        self.stock_out_btn = QPushButton("📤 出库")
        self.stock_out_btn.clicked.connect(self.stock_out)
        self.stock_out_btn.setEnabled(False)
        button_layout.addWidget(self.stock_out_btn)

        layout.addLayout(button_layout)

        layout.addStretch()

        return widget

    def create_batch_tab(self) -> QWidget:
        """创建批次管理页面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        # 搜索框
        self.batch_search_edit = QLineEdit()
        self.batch_search_edit.setPlaceholderText("搜索批次号、商品名称...")
        self.batch_search_edit.textChanged.connect(self.search_batches)
        toolbar_layout.addWidget(QLabel("搜索:"))
        toolbar_layout.addWidget(self.batch_search_edit)

        # 状态筛选
        self.batch_status_combo = QComboBox()
        self.batch_status_combo.addItems(["全部状态", "进行中", "已完成", "已取消"])
        self.batch_status_combo.currentTextChanged.connect(self.filter_batches)
        toolbar_layout.addWidget(QLabel("状态:"))
        toolbar_layout.addWidget(self.batch_status_combo)

        # 日期筛选
        self.batch_date_combo = QComboBox()
        self.batch_date_combo.addItems(["全部时间", "今天", "本周", "本月", "本年"])
        self.batch_date_combo.currentTextChanged.connect(self.filter_batches)
        toolbar_layout.addWidget(QLabel("时间:"))
        toolbar_layout.addWidget(self.batch_date_combo)

        toolbar_layout.addStretch()

        # 操作按钮
        self.create_batch_btn = QPushButton("➕ 创建批次")
        self.create_batch_btn.clicked.connect(self.create_batch)
        toolbar_layout.addWidget(self.create_batch_btn)

        self.edit_batch_btn = QPushButton("✏️ 编辑批次")
        self.edit_batch_btn.clicked.connect(self.edit_batch)
        self.edit_batch_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_batch_btn)

        self.close_batch_btn = QPushButton("✅ 完成批次")
        self.close_batch_btn.clicked.connect(self.close_batch)
        self.close_batch_btn.setEnabled(False)
        toolbar_layout.addWidget(self.close_batch_btn)

        self.delete_batch_btn = QPushButton("🗑️ 删除批次")
        self.delete_batch_btn.clicked.connect(self.delete_batch)
        self.delete_batch_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_batch_btn)

        layout.addLayout(toolbar_layout)

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 批次列表
        self.batch_table = QTableWidget()
        self.setup_batch_table()
        splitter.addWidget(self.batch_table)

        # 批次详情
        self.batch_detail_widget = self.create_batch_detail_widget()
        splitter.addWidget(self.batch_detail_widget)

        # 设置分割器比例
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)

        return widget

    def setup_batch_table(self):
        """设置批次表格"""
        headers = [
            "批次ID",
            "批次号",
            "商品数量",
            "总数量",
            "总价值",
            "状态",
            "创建人",
            "创建时间",
            "完成时间",
        ]

        self.batch_table.setColumnCount(len(headers))
        self.batch_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.batch_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.batch_table.setAlternatingRowColors(True)
        self.batch_table.setSortingEnabled(True)

        # 设置列宽
        header = self.batch_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 批次号列自适应

        # 连接选择信号
        self.batch_table.itemSelectionChanged.connect(self.on_batch_selection_changed)

    def create_batch_detail_widget(self) -> QWidget:
        """创建批次详情控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标题
        title_label = QLabel("📋 批次详情")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # 详情内容
        self.batch_detail_label = QLabel("请选择一个批次查看详情")
        self.batch_detail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.batch_detail_label.setStyleSheet("color: #888; margin: 20px;")
        layout.addWidget(self.batch_detail_label)

        layout.addStretch()

        return widget

    def create_stats_tab(self) -> QWidget:
        """创建库存统计页面"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        # 刷新按钮
        refresh_stats_btn = QPushButton("🔄 刷新统计")
        refresh_stats_btn.clicked.connect(self.refresh_stats)
        toolbar_layout.addWidget(refresh_stats_btn)

        # 库存预警按钮
        check_stock_btn = QPushButton("⚠️ 库存预警")
        check_stock_btn.clicked.connect(self.check_low_stock)
        toolbar_layout.addWidget(check_stock_btn)

        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)

        # 统计卡片区域
        stats_layout = QHBoxLayout()

        # 总体统计
        self.total_stats_group = self.create_stats_group(
            "📊 总体统计",
            [
                ("商品总数", "0"),
                ("库存总值", "¥0.00"),
                ("分类数量", "0"),
                ("供应商数量", "0"),
            ],
        )
        stats_layout.addWidget(self.total_stats_group)

        # 库存状态统计
        self.stock_stats_group = self.create_stats_group(
            "📦 库存状态",
            [("正常库存", "0"), ("低库存", "0"), ("缺货", "0"), ("停售", "0")],
        )
        stats_layout.addWidget(self.stock_stats_group)

        # 价值统计
        self.value_stats_group = self.create_stats_group(
            "💰 价值统计",
            [
                ("总采购价值", "¥0.00"),
                ("总销售价值", "¥0.00"),
                ("预期利润", "¥0.00"),
                ("平均利润率", "0%"),
            ],
        )
        stats_layout.addWidget(self.value_stats_group)

        layout.addLayout(stats_layout)

        # 详细统计表格
        self.stats_table = QTableWidget()
        self.setup_stats_table()
        layout.addWidget(self.stats_table)

        return widget

    def create_stats_group(self, title: str, stats: List[tuple]) -> QGroupBox:
        """创建统计组"""
        group = QGroupBox(title)
        layout = QVBoxLayout(group)

        for label, value in stats:
            stat_layout = QHBoxLayout()

            label_widget = QLabel(label + ":")
            label_widget.setStyleSheet("font-weight: bold;")
            stat_layout.addWidget(label_widget)

            value_widget = QLabel(value)
            value_widget.setStyleSheet("color: #4a90e2; font-weight: bold;")
            value_widget.setAlignment(Qt.AlignmentFlag.AlignRight)
            stat_layout.addWidget(value_widget)

            layout.addLayout(stat_layout)

        return group

    def setup_stats_table(self):
        """设置统计表格"""
        headers = ["分类", "商品数量", "总库存", "库存价值", "平均价格", "状态分布"]

        self.stats_table.setColumnCount(len(headers))
        self.stats_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.stats_table.setAlternatingRowColors(True)
        self.stats_table.setSortingEnabled(True)

        # 设置列宽
        header = self.stats_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

    def refresh_stats(self):
        """刷新统计数据"""
        try:
            self.status_message.emit("正在刷新统计数据...")

            # TODO: 实现统计数据计算
            # 这里应该从数据库获取实际统计数据

            # 更新统计卡片
            self.update_stats_cards()

            # 更新统计表格
            self.update_stats_table()

            self.status_message.emit("统计数据刷新完成")

        except Exception as e:
            self.logger.error(f"刷新统计数据失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新统计数据失败:\n{e}")

    def update_stats_cards(self):
        """更新统计卡片"""
        # TODO: 实现统计卡片更新逻辑
        pass

    def update_stats_table(self):
        """更新统计表格"""
        # TODO: 实现统计表格更新逻辑
        pass

    def load_data(self):
        """加载数据"""
        self.load_products()
        self.load_categories()

    def load_products(self):
        """加载商品数据"""
        try:
            self.status_message.emit("正在加载商品数据...")

            # 获取商品列表
            self.products = self.product_manager.get_products(limit=1000)

            # 更新表格
            self.update_product_table()

            self.status_message.emit(f"已加载 {len(self.products)} 个商品")

        except Exception as e:
            self.logger.error(f"加载商品数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载商品数据失败:\n{e}")

    def load_categories(self):
        """加载商品分类"""
        try:
            categories = self.product_manager.get_categories()

            # 更新分类下拉框
            self.category_combo.clear()
            self.category_combo.addItem("全部分类", "")

            for category in categories:
                self.category_combo.addItem(category, category)

        except Exception as e:
            self.logger.error(f"加载商品分类失败: {e}")

    def update_product_table(self):
        """更新商品表格"""
        self.product_table.setRowCount(len(self.products))

        for row, product in enumerate(self.products):
            # 商品ID
            self.product_table.setItem(
                row, 0, QTableWidgetItem(product.product_id or "")
            )

            # 商品名称
            self.product_table.setItem(row, 1, QTableWidgetItem(product.name))

            # 编码
            self.product_table.setItem(row, 2, QTableWidgetItem(product.code or ""))

            # 分类
            self.product_table.setItem(row, 3, QTableWidgetItem(product.category))

            # 库存
            quantity_item = QTableWidgetItem(str(product.quantity))
            quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.product_table.setItem(row, 4, quantity_item)

            # 单位
            self.product_table.setItem(row, 5, QTableWidgetItem(product.unit))

            # 采购价
            purchase_price_item = QTableWidgetItem(f"¥{product.purchase_price:.2f}")
            purchase_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)
            self.product_table.setItem(row, 6, purchase_price_item)

            # 销售价
            selling_price_item = QTableWidgetItem(f"¥{product.selling_price:.2f}")
            selling_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)
            self.product_table.setItem(row, 7, selling_price_item)

            # 利润
            profit = product.calculate_profit()
            profit_item = QTableWidgetItem(f"¥{profit:.2f}")
            profit_item.setTextAlignment(Qt.AlignmentFlag.AlignRight)
            if profit > 0:
                profit_item.setBackground(Qt.GlobalColor.green)
            elif profit < 0:
                profit_item.setBackground(Qt.GlobalColor.red)
            self.product_table.setItem(row, 8, profit_item)

            # 状态
            status_item = QTableWidgetItem(product.status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.product_table.setItem(row, 9, status_item)

            # 创建时间
            created_at = (
                product.created_at.strftime("%Y-%m-%d") if product.created_at else ""
            )
            self.product_table.setItem(row, 10, QTableWidgetItem(created_at))

    def on_product_selection_changed(self):
        """商品选择变化"""
        selected_rows = self.product_table.selectionModel().selectedRows()

        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.products):
                self.current_product = self.products[row]
                self.update_product_detail()

                # 启用按钮
                self.edit_product_btn.setEnabled(True)
                self.delete_product_btn.setEnabled(True)
                self.stock_in_btn.setEnabled(True)
                self.stock_out_btn.setEnabled(True)
            else:
                self.current_product = None
                self.clear_product_detail()
        else:
            self.current_product = None
            self.clear_product_detail()

            # 禁用按钮
            self.edit_product_btn.setEnabled(False)
            self.delete_product_btn.setEnabled(False)
            self.stock_in_btn.setEnabled(False)
            self.stock_out_btn.setEnabled(False)

    def update_product_detail(self):
        """更新商品详情"""
        if not self.current_product:
            return

        product = self.current_product

        # 基础信息
        self.detail_name_label.setText(f"商品名称: {product.name}")
        self.detail_code_label.setText(f"商品编码: {product.code or '-'}")
        self.detail_category_label.setText(f"商品分类: {product.category}")
        self.detail_status_label.setText(f"商品状态: {product.status}")

        # 库存信息
        self.detail_quantity_label.setText(
            f"当前库存: {product.quantity} {product.unit}"
        )
        self.detail_unit_label.setText(f"计量单位: {product.unit}")
        self.detail_location_label.setText(f"存放位置: {product.location or '-'}")

        # 价格信息
        self.detail_purchase_price_label.setText(
            f"采购价: ¥{product.purchase_price:.2f}"
        )
        self.detail_selling_price_label.setText(f"销售价: ¥{product.selling_price:.2f}")

        profit = product.calculate_profit()
        profit_margin = product.calculate_profit_margin()
        self.detail_profit_label.setText(f"预估利润: ¥{profit:.2f}")
        self.detail_profit_margin_label.setText(f"利润率: {profit_margin:.1f}%")

        # 供应商信息
        self.detail_supplier_label.setText(f"供应商: {product.supplier or '-'}")
        self.detail_supplier_link_label.setText(
            f"供应商链接: {product.supplier_link or '-'}"
        )

    def clear_product_detail(self):
        """清空商品详情"""
        labels = [
            self.detail_name_label,
            self.detail_code_label,
            self.detail_category_label,
            self.detail_status_label,
            self.detail_quantity_label,
            self.detail_unit_label,
            self.detail_location_label,
            self.detail_purchase_price_label,
            self.detail_selling_price_label,
            self.detail_profit_label,
            self.detail_profit_margin_label,
            self.detail_supplier_label,
            self.detail_supplier_link_label,
        ]

        for label in labels:
            text = label.text().split(":")[0] + ": -"
            label.setText(text)

    def search_products(self):
        """搜索商品"""
        keyword = self.product_search_edit.text().strip()

        if keyword:
            try:
                self.products = self.product_manager.search_products(
                    keyword, limit=1000
                )
                self.update_product_table()
                self.status_message.emit(f"搜索到 {len(self.products)} 个商品")
            except Exception as e:
                self.logger.error(f"搜索商品失败: {e}")
                QMessageBox.critical(self, "错误", f"搜索商品失败:\n{e}")
        else:
            self.load_products()

    def filter_products(self):
        """筛选商品"""
        category = self.category_combo.currentData()
        status = self.status_combo.currentText()

        if status == "全部状态":
            status = None

        try:
            self.products = self.product_manager.get_products(
                category=category if category else None, status=status, limit=1000
            )
            self.update_product_table()
            self.status_message.emit(f"筛选到 {len(self.products)} 个商品")
        except Exception as e:
            self.logger.error(f"筛选商品失败: {e}")
            QMessageBox.critical(self, "错误", f"筛选商品失败:\n{e}")

    # 商品操作方法
    def add_product(self):
        """添加商品"""
        try:
            from gui.modules.inventory.product_form import ProductForm

            # 获取分类列表
            categories = self.product_manager.get_categories()

            # 创建商品表单对话框
            dialog = ProductForm(categories=categories, parent=self)
            dialog.product_saved.connect(self.on_product_saved)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            self.logger.error(f"打开添加商品对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开添加商品对话框失败:\n{e}")

    def edit_product(self):
        """编辑商品"""
        if not self.current_product:
            return

        try:
            from gui.modules.inventory.product_form import ProductForm

            # 获取分类列表
            categories = self.product_manager.get_categories()

            # 创建商品表单对话框
            dialog = ProductForm(
                product=self.current_product, categories=categories, parent=self
            )
            dialog.product_saved.connect(self.on_product_saved)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            self.logger.error(f"打开编辑商品对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开编辑商品对话框失败:\n{e}")

    def on_product_saved(self, product):
        """商品保存回调"""
        try:
            # 判断是新建还是编辑
            if product.product_id and self.product_manager.get_product(
                product.product_id
            ):
                # 更新商品
                if self.product_manager.update_product(product):
                    QMessageBox.information(self, "成功", "商品更新成功")
                    self.load_products()
                    self.load_categories()
                else:
                    QMessageBox.warning(self, "失败", "商品更新失败")
            else:
                # 创建新商品
                if self.product_manager.create_product(product):
                    QMessageBox.information(self, "成功", "商品创建成功")
                    self.load_products()
                    self.load_categories()
                else:
                    QMessageBox.warning(self, "失败", "商品创建失败")

        except Exception as e:
            self.logger.error(f"保存商品时出错: {e}")
            QMessageBox.critical(self, "错误", f"保存商品时出错:\n{e}")

    def delete_product(self):
        """删除商品"""
        if not self.current_product:
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除商品 '{self.current_product.name}' 吗？\n此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                if self.product_manager.delete_product(self.current_product.product_id):
                    QMessageBox.information(self, "成功", "商品删除成功")
                    self.load_products()
                else:
                    QMessageBox.warning(self, "失败", "商品删除失败")
            except Exception as e:
                self.logger.error(f"删除商品失败: {e}")
                QMessageBox.critical(self, "错误", f"删除商品失败:\n{e}")

    def stock_in(self):
        """入库"""
        if not self.current_product:
            return

        try:
            from gui.modules.inventory.stock_dialog import StockDialog

            # 创建入库对话框
            dialog = StockDialog(self.current_product, "in", self)
            dialog.stock_updated.connect(self.on_stock_updated)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            self.logger.error(f"打开入库对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开入库对话框失败:\n{e}")

    def stock_out(self):
        """出库"""
        if not self.current_product:
            return

        try:
            from gui.modules.inventory.stock_dialog import StockDialog

            # 创建出库对话框
            dialog = StockDialog(self.current_product, "out", self)
            dialog.stock_updated.connect(self.on_stock_updated)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            self.logger.error(f"打开出库对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开出库对话框失败:\n{e}")

    def on_stock_updated(self, product_id: str, quantity_change: int, reason: str):
        """库存更新回调"""
        try:
            # 更新库存
            if self.product_manager.update_stock(product_id, quantity_change, reason):
                QMessageBox.information(self, "成功", "库存更新成功")

                # 刷新数据
                self.load_products()

                # 更新当前选中商品的详情
                if (
                    self.current_product
                    and self.current_product.product_id == product_id
                ):
                    updated_product = self.product_manager.get_product(product_id)
                    if updated_product:
                        self.current_product = updated_product
                        self.update_product_detail()
            else:
                QMessageBox.warning(self, "失败", "库存更新失败")

        except Exception as e:
            self.logger.error(f"库存更新时出错: {e}")
            QMessageBox.critical(self, "错误", f"库存更新时出错:\n{e}")

    def import_products(self):
        """导入商品数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "导入商品数据",
                "",
                "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*.*)",
            )

            if file_path:
                # TODO: 实现导入功能
                QMessageBox.information(
                    self, "提示", f"导入功能正在开发中\n选择的文件: {file_path}"
                )

        except Exception as e:
            self.logger.error(f"导入商品数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导入商品数据失败:\n{e}")

    def export_products(self):
        """导出商品数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出商品数据",
                "商品数据.xlsx",
                "Excel文件 (*.xlsx);;CSV文件 (*.csv);;所有文件 (*.*)",
            )

            if file_path:
                # TODO: 实现导出功能
                QMessageBox.information(
                    self, "提示", f"导出功能正在开发中\n保存路径: {file_path}"
                )

        except Exception as e:
            self.logger.error(f"导出商品数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出商品数据失败:\n{e}")

    def check_low_stock(self):
        """检查低库存商品"""
        try:
            low_stock_products = []
            for product in self.products:
                if hasattr(product, "current_stock") and hasattr(
                    product, "safety_stock"
                ):
                    if product.current_stock <= product.safety_stock:
                        low_stock_products.append(product)

            if low_stock_products:
                message = "以下商品库存不足:\n\n"
                for product in low_stock_products[:10]:  # 最多显示10个
                    message += f"• {product.name}: 当前库存 {product.current_stock}, 安全库存 {product.safety_stock}\n"

                if len(low_stock_products) > 10:
                    message += (
                        f"\n... 还有 {len(low_stock_products) - 10} 个商品库存不足"
                    )

                QMessageBox.warning(self, "库存预警", message)
            else:
                QMessageBox.information(self, "库存检查", "所有商品库存充足")

        except Exception as e:
            self.logger.error(f"检查低库存失败: {e}")
            QMessageBox.critical(self, "错误", f"检查低库存失败:\n{e}")

    # 批次管理相关方法
    def search_batches(self):
        """搜索批次"""
        try:
            search_text = self.batch_search_edit.text().strip()
            # TODO: 实现批次搜索逻辑
            self.status_message.emit(f"搜索批次: {search_text}")
        except Exception as e:
            self.logger.error(f"搜索批次失败: {e}")

    def filter_batches(self):
        """筛选批次"""
        try:
            status = self.batch_status_combo.currentText()
            date_range = self.batch_date_combo.currentText()
            # TODO: 实现批次筛选逻辑
            self.status_message.emit(f"筛选批次: {status}, {date_range}")
        except Exception as e:
            self.logger.error(f"筛选批次失败: {e}")

    def create_batch(self):
        """创建批次"""
        try:
            # TODO: 实现创建批次对话框
            QMessageBox.information(self, "提示", "创建批次功能正在开发中")
        except Exception as e:
            self.logger.error(f"创建批次失败: {e}")
            QMessageBox.critical(self, "错误", f"创建批次失败:\n{e}")

    def edit_batch(self):
        """编辑批次"""
        try:
            if not self.current_batch:
                return
            # TODO: 实现编辑批次对话框
            QMessageBox.information(self, "提示", "编辑批次功能正在开发中")
        except Exception as e:
            self.logger.error(f"编辑批次失败: {e}")
            QMessageBox.critical(self, "错误", f"编辑批次失败:\n{e}")

    def close_batch(self):
        """完成批次"""
        try:
            if not self.current_batch:
                return

            reply = QMessageBox.question(
                self,
                "确认",
                f"确定要完成批次 {self.current_batch.batch_number} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # TODO: 实现完成批次逻辑
                QMessageBox.information(self, "提示", "完成批次功能正在开发中")

        except Exception as e:
            self.logger.error(f"完成批次失败: {e}")
            QMessageBox.critical(self, "错误", f"完成批次失败:\n{e}")

    def delete_batch(self):
        """删除批次"""
        try:
            if not self.current_batch:
                return

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除批次 {self.current_batch.batch_number} 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # TODO: 实现删除批次逻辑
                QMessageBox.information(self, "提示", "删除批次功能正在开发中")

        except Exception as e:
            self.logger.error(f"删除批次失败: {e}")
            QMessageBox.critical(self, "错误", f"删除批次失败:\n{e}")

    def on_batch_selection_changed(self):
        """批次选择改变"""
        try:
            selected_items = self.batch_table.selectedItems()
            if selected_items:
                row = selected_items[0].row()
                batch_id = self.batch_table.item(row, 0).text()

                # TODO: 根据batch_id获取批次详情
                self.current_batch = None  # 临时设置

                # 更新按钮状态
                has_selection = self.current_batch is not None
                self.edit_batch_btn.setEnabled(has_selection)
                self.close_batch_btn.setEnabled(has_selection)
                self.delete_batch_btn.setEnabled(has_selection)

                # 更新详情显示
                self.update_batch_detail()
            else:
                self.current_batch = None
                self.edit_batch_btn.setEnabled(False)
                self.close_batch_btn.setEnabled(False)
                self.delete_batch_btn.setEnabled(False)
                self.batch_detail_label.setText("请选择一个批次查看详情")

        except Exception as e:
            self.logger.error(f"批次选择改变处理失败: {e}")

    def update_batch_detail(self):
        """更新批次详情显示"""
        try:
            if self.current_batch:
                # TODO: 显示批次详细信息
                detail_text = (
                    f"批次号: {getattr(self.current_batch, 'batch_number', 'N/A')}\n"
                )
                detail_text += f"状态: {getattr(self.current_batch, 'status', 'N/A')}\n"
                detail_text += "详细信息正在开发中..."
                self.batch_detail_label.setText(detail_text)
            else:
                self.batch_detail_label.setText("请选择一个批次查看详情")
        except Exception as e:
            self.logger.error(f"更新批次详情失败: {e}")

    def load_batches(self):
        """加载批次数据"""
        try:
            self.status_message.emit("正在加载批次数据...")

            # TODO: 从数据库加载批次数据
            self.batches = []  # 临时设置为空列表

            # 更新批次表格
            self.update_batch_table()

            self.status_message.emit(f"已加载 {len(self.batches)} 个批次")

        except Exception as e:
            self.logger.error(f"加载批次数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载批次数据失败:\n{e}")

    def update_batch_table(self):
        """更新批次表格"""
        try:
            self.batch_table.setRowCount(len(self.batches))

            for row, batch in enumerate(self.batches):
                # TODO: 填充批次表格数据
                self.batch_table.setItem(
                    row, 0, QTableWidgetItem(str(getattr(batch, "id", "")))
                )
                self.batch_table.setItem(
                    row, 1, QTableWidgetItem(getattr(batch, "batch_number", ""))
                )
                # ... 其他列的数据填充

        except Exception as e:
            self.logger.error(f"更新批次表格失败: {e}")
