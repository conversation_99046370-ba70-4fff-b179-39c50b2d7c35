#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析模块 - 备份版本

简化的数据分析界面，确保能正常显示。
"""

import logging
from datetime import datetime
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QFrame,
    QGridLayout,
    QPushButton,
    QTextEdit,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QGroupBox,
    QToolBar,
    QSizePolicy,
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from core.managers.product_manager import ProductManager
from core.managers.batch_manager import BatchManager
from core.managers.supplier_manager import SupplierManager
from utils.logger import LoggerMixin


class AnalyticsWidget(QWidget, LoggerMixin):
    """数据分析主界面 - 简化版"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_manager = ProductManager(db_manager)
        self.batch_manager = BatchManager(db_manager)
        self.supplier_manager = SupplierManager(db_manager)

        # 数据缓存
        self.analytics_data = {}

        # 初始化界面
        self.init_ui()

        # 延迟加载数据
        QTimer.singleShot(100, self.load_sample_data)

        self.logger.info("数据分析界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title = QLabel("📊 数据分析仪表板")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet(
            """
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            padding: 20px;
            background-color: #2b2b2b;
            border-radius: 10px;
            margin-bottom: 20px;
        """
        )
        layout.addWidget(title)

        # 工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)

        # 指标卡片
        cards_frame = self.create_cards()
        layout.addWidget(cards_frame)

        # 分析表格
        table_frame = self.create_table()
        layout.addWidget(table_frame)

        # 状态栏
        status_frame = self.create_status()
        layout.addWidget(status_frame)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setStyleSheet(
            """
            QToolBar {
                background-color: #2b2b2b;
                border: none;
                padding: 10px;
            }
            QToolBar QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin-right: 10px;
            }
            QToolBar QPushButton:hover {
                background-color: #45a049;
            }
        """
        )

        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.clicked.connect(self.load_sample_data)
        toolbar.addWidget(refresh_btn)

        # 销售趋势分析
        sales_trend_btn = QPushButton("📈 销售趋势")
        sales_trend_btn.clicked.connect(self.show_sales_trend)
        toolbar.addWidget(sales_trend_btn)

        # 库存周转分析
        inventory_turn_btn = QPushButton("🔄 库存周转")
        inventory_turn_btn.clicked.connect(self.show_inventory_turnover)
        toolbar.addWidget(inventory_turn_btn)

        # 利润分析
        profit_analysis_btn = QPushButton("💰 利润分析")
        profit_analysis_btn.clicked.connect(self.show_profit_analysis)
        toolbar.addWidget(profit_analysis_btn)

        # 供应商分析
        supplier_analysis_btn = QPushButton("🏪 供应商分析")
        supplier_analysis_btn.clicked.connect(self.show_supplier_analysis)
        toolbar.addWidget(supplier_analysis_btn)

        export_btn = QPushButton("📄 导出报告")
        export_btn.clicked.connect(self.export_report)
        toolbar.addWidget(export_btn)

        return toolbar

    def create_cards(self):
        """创建指标卡片"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 8px;
                padding: 15px;
            }
        """
        )

        layout = QGridLayout(frame)
        layout.setSpacing(12)
        # 设置列的拉伸因子，让卡片能够自适应宽度
        for col in range(4):
            layout.setColumnStretch(col, 1)
        # 设置行的拉伸因子，让卡片能够自适应高度
        for row in range(2):
            layout.setRowStretch(row, 1)

        # 创建8个指标卡片
        self.cards = {}

        cards_data = [
            ("products", "📦", "商品总数", "0", "#4CAF50"),
            ("inventory_value", "💰", "库存价值", "¥0", "#2196F3"),
            ("low_stock", "⚠️", "低库存预警", "0", "#FF9800"),
            ("suppliers", "🏪", "供应商数量", "0", "#9C27B0"),
            ("profit_margin", "📈", "平均利润率", "0%", "#4CAF50"),
            ("batches", "📋", "活跃批次", "0", "#2196F3"),
            ("total_cost", "💸", "总投资成本", "¥0", "#FF5722"),
            ("profit", "💎", "预期利润", "¥0", "#4CAF50"),
        ]

        for i, (key, icon, title, value, color) in enumerate(cards_data):
            card = self.create_card(icon, title, value, color)
            self.cards[key] = card
            row = i // 4
            col = i % 4
            layout.addWidget(card, row, col)

        return frame

    def create_card(self, icon: str, title: str, value: str, color: str):
        """创建单个指标卡片 - 自适应大小的美观样式"""
        card = QFrame()
        # 移除固定大小，让卡片能够自适应
        card.setMinimumSize(180, 100)  # 设置最小尺寸
        card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        card.setStyleSheet(
            f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1e1e1e);
                border: 2px solid {color};
                border-radius: 12px;
                padding: 0px;
                margin: 2px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a3a3a, stop:1 #2e2e2e);
                border: 2px solid {color};
                transform: scale(1.02);
            }}
        """
        )

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)

        # 顶部：图标和标题在同一行
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(
            f"""
            font-size: 22px;
            color: {color};
            background: transparent;
            min-width: 24px;
            max-width: 24px;
        """
        )
        header_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(
            f"""
            font-size: 13px;
            color: {color};
            background: transparent;
            font-weight: bold;
        """
        )
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 中间：数值 - 左对齐，更大字体，自适应大小
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        value_label.setStyleSheet(
            f"""
            font-size: 28px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin: 12px 0px;
            padding-left: 2px;
        """
        )
        layout.addWidget(value_label)

        # 添加弹性空间，让内容向上对齐
        layout.addStretch()

        # 底部：状态信息（可选）
        status_label = QLabel("实时数据")
        status_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        status_label.setStyleSheet(
            f"""
            font-size: 10px;
            color: #aaaaaa;
            background: transparent;
            padding: 3px;
            border-top: 1px solid #444;
            margin-top: 6px;
            padding-top: 6px;
        """
        )
        layout.addWidget(status_label)

        # 存储值标签以便更新
        card.value_label = value_label

        return card

    def create_table(self):
        """创建分析表格"""
        group = QGroupBox("📊 分类分析")
        group.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: white;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        )

        layout = QVBoxLayout(group)

        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(
            ["分类", "商品数量", "库存价值", "平均利润率", "状态"]
        )

        self.table.setStyleSheet(
            """
            QTableWidget {
                background-color: #1e1e1e;
                border: 1px solid #555;
                color: white;
                gridline-color: #555;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """
        )

        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        for i in range(1, 5):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(self.table)

        return group

    def create_status(self):
        """创建状态栏"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: #2b2b2b;
                border-top: 1px solid #555;
                padding: 10px;
            }
        """
        )

        layout = QHBoxLayout(frame)

        self.status_label = QLabel("数据分析就绪")
        self.status_label.setStyleSheet("color: white; font-size: 14px;")
        layout.addWidget(self.status_label)

        layout.addStretch()

        self.update_time_label = QLabel("")
        self.update_time_label.setStyleSheet("color: #888; font-size: 12px;")
        layout.addWidget(self.update_time_label)

        return frame

    def load_sample_data(self):
        """加载示例数据"""
        try:
            self.status_label.setText("正在加载数据...")

            # 示例数据
            sample_data = {
                "total_products": 25,
                "total_inventory_value": 15680.50,
                "total_cost": 12340.00,
                "total_expected_profit": 3340.50,
                "low_stock_count": 3,
                "total_suppliers": 8,
                "active_batches": 12,
                "avg_profit_margin": 27.1,
                "categories": {
                    "电子产品": {
                        "count": 8,
                        "inventory_value": 6800.00,
                        "total_cost": 5200.00,
                        "total_profit": 1600.00,
                    },
                    "服装配饰": {
                        "count": 10,
                        "inventory_value": 5200.50,
                        "total_cost": 4100.00,
                        "total_profit": 1100.50,
                    },
                    "家居用品": {
                        "count": 5,
                        "inventory_value": 2680.00,
                        "total_cost": 2040.00,
                        "total_profit": 640.00,
                    },
                    "未分类": {
                        "count": 2,
                        "inventory_value": 1000.00,
                        "total_cost": 1000.00,
                        "total_profit": 0.00,
                    },
                },
            }

            # 更新卡片
            self.cards["products"].value_label.setText(
                str(sample_data["total_products"])
            )
            self.cards["inventory_value"].value_label.setText(
                f"¥{sample_data['total_inventory_value']:,.2f}"
            )
            self.cards["low_stock"].value_label.setText(
                str(sample_data["low_stock_count"])
            )
            self.cards["suppliers"].value_label.setText(
                str(sample_data["total_suppliers"])
            )
            self.cards["profit_margin"].value_label.setText(
                f"{sample_data['avg_profit_margin']:.1f}%"
            )
            self.cards["batches"].value_label.setText(
                str(sample_data["active_batches"])
            )
            self.cards["total_cost"].value_label.setText(
                f"¥{sample_data['total_cost']:,.2f}"
            )
            self.cards["profit"].value_label.setText(
                f"¥{sample_data['total_expected_profit']:,.2f}"
            )

            # 更新表格
            categories = sample_data["categories"]
            self.table.setRowCount(len(categories))

            for row, (category, data) in enumerate(categories.items()):
                self.table.setItem(row, 0, QTableWidgetItem(category))
                self.table.setItem(row, 1, QTableWidgetItem(str(data["count"])))
                self.table.setItem(
                    row, 2, QTableWidgetItem(f"¥{data['inventory_value']:,.2f}")
                )

                margin = (
                    (data["total_profit"] / data["total_cost"] * 100)
                    if data["total_cost"] > 0
                    else 0
                )
                self.table.setItem(row, 3, QTableWidgetItem(f"{margin:.1f}%"))

                status = (
                    "优秀"
                    if margin > 20
                    else "良好" if margin > 10 else "一般" if margin > 0 else "亏损"
                )
                status_item = QTableWidgetItem(status)
                if margin > 20:
                    status_item.setForeground(Qt.GlobalColor.green)
                elif margin > 10:
                    status_item.setForeground(Qt.GlobalColor.blue)
                elif margin > 0:
                    status_item.setForeground(Qt.GlobalColor.yellow)
                else:
                    status_item.setForeground(Qt.GlobalColor.red)
                self.table.setItem(row, 4, status_item)

            self.status_label.setText("数据加载完成")
            self.update_time_label.setText(
                f"最后更新: {datetime.now().strftime('%H:%M:%S')}"
            )

            self.logger.info("示例数据加载完成")

        except Exception as e:
            self.status_label.setText("数据加载失败")
            self.logger.error(f"加载数据失败: {e}")

    def show_sales_trend(self):
        """显示销售趋势分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget, QScrollArea

            dialog = QDialog(self)
            dialog.setWindowTitle("销售趋势分析")
            dialog.setModal(True)
            dialog.resize(900, 700)

            layout = QVBoxLayout(dialog)

            tab_widget = QTabWidget()

            # 趋势图表
            trend_widget = QWidget()
            trend_layout = QVBoxLayout(trend_widget)

            # 趋势统计
            trend_stats = QGroupBox("📈 销售趋势统计")
            stats_layout = QGridLayout(trend_stats)

            # 今日销售
            stats_layout.addWidget(QLabel("今日销售:"), 0, 0)
            today_sales = QLabel("¥15,680.50")
            today_sales.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(today_sales, 0, 1)

            # 本周销售
            stats_layout.addWidget(QLabel("本周销售:"), 0, 2)
            week_sales = QLabel("¥89,234.20")
            week_sales.setStyleSheet(
                "font-weight: bold; color: #2196F3; font-size: 14px;"
            )
            stats_layout.addWidget(week_sales, 0, 3)

            # 本月销售
            stats_layout.addWidget(QLabel("本月销售:"), 1, 0)
            month_sales = QLabel("¥312,456.80")
            month_sales.setStyleSheet(
                "font-weight: bold; color: #FF9800; font-size: 14px;"
            )
            stats_layout.addWidget(month_sales, 1, 1)

            # 增长率
            stats_layout.addWidget(QLabel("月增长率:"), 1, 2)
            growth_rate = QLabel("+23.5%")
            growth_rate.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(growth_rate, 1, 3)

            trend_layout.addWidget(trend_stats)

            # 趋势分析文本
            trend_analysis = QTextEdit()
            trend_analysis.setMaximumHeight(200)
            trend_analysis.setPlainText(
                "销售趋势分析报告：\n\n"
                "1. 整体趋势：本月销售呈现稳步上升趋势，较上月增长23.5%\n"
                "2. 周期性特征：周末销售额明显高于工作日，建议加强周末营销\n"
                "3. 商品类别：电子产品销售增长最快，服装配饰保持稳定\n"
                "4. 季节性影响：临近节假日，礼品类商品销售显著增长\n"
                "5. 预测建议：预计下月销售将继续增长，建议提前备货热销商品"
            )
            trend_analysis.setReadOnly(True)
            trend_layout.addWidget(trend_analysis)

            tab_widget.addTab(trend_widget, "趋势分析")

            # 商品排行
            ranking_widget = QWidget()
            ranking_layout = QVBoxLayout(ranking_widget)

            ranking_table = QTableWidget()
            ranking_table.setColumnCount(5)
            ranking_table.setHorizontalHeaderLabels(
                ["排名", "商品名称", "销售额", "销量", "增长率"]
            )
            ranking_table.setRowCount(10)

            # 示例数据
            sample_products = [
                ("1", "iPhone 15 Pro", "¥45,600", "12台", "+15%"),
                ("2", "MacBook Air", "¥38,900", "8台", "+8%"),
                ("3", "AirPods Pro", "¥12,800", "32个", "+25%"),
                ("4", "iPad Air", "¥18,600", "15台", "+12%"),
                ("5", "Apple Watch", "¥15,200", "20个", "+18%"),
            ]

            for row, (rank, name, sales, quantity, growth) in enumerate(
                sample_products
            ):
                ranking_table.setItem(row, 0, QTableWidgetItem(rank))
                ranking_table.setItem(row, 1, QTableWidgetItem(name))
                ranking_table.setItem(row, 2, QTableWidgetItem(sales))
                ranking_table.setItem(row, 3, QTableWidgetItem(quantity))
                ranking_table.setItem(row, 4, QTableWidgetItem(growth))

            ranking_table.setAlternatingRowColors(True)
            ranking_layout.addWidget(ranking_table)

            tab_widget.addTab(ranking_widget, "商品排行")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示销售趋势分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示销售趋势分析失败:\n{e}")

    def show_inventory_turnover(self):
        """显示库存周转分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget

            dialog = QDialog(self)
            dialog.setWindowTitle("库存周转分析")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            tab_widget = QTabWidget()

            # 周转率统计
            turnover_widget = QWidget()
            turnover_layout = QVBoxLayout(turnover_widget)

            # 总体指标
            overall_stats = QGroupBox("📊 总体周转指标")
            stats_layout = QGridLayout(overall_stats)

            stats_layout.addWidget(QLabel("平均周转率:"), 0, 0)
            avg_turnover = QLabel("4.2次/年")
            avg_turnover.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(avg_turnover, 0, 1)

            stats_layout.addWidget(QLabel("平均周转天数:"), 0, 2)
            avg_days = QLabel("87天")
            avg_days.setStyleSheet(
                "font-weight: bold; color: #2196F3; font-size: 14px;"
            )
            stats_layout.addWidget(avg_days, 0, 3)

            stats_layout.addWidget(QLabel("快速周转商品:"), 1, 0)
            fast_products = QLabel("15个")
            fast_products.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(fast_products, 1, 1)

            stats_layout.addWidget(QLabel("慢速周转商品:"), 1, 2)
            slow_products = QLabel("8个")
            slow_products.setStyleSheet(
                "font-weight: bold; color: #f44336; font-size: 14px;"
            )
            stats_layout.addWidget(slow_products, 1, 3)

            turnover_layout.addWidget(overall_stats)

            # 周转分析表格
            turnover_table = QTableWidget()
            turnover_table.setColumnCount(6)
            turnover_table.setHorizontalHeaderLabels(
                ["商品名称", "当前库存", "月销量", "周转率", "周转天数", "状态"]
            )
            turnover_table.setRowCount(8)

            # 示例数据
            sample_turnover = [
                ("iPhone 15", "25", "12", "5.8", "63天", "快速"),
                ("MacBook Pro", "8", "3", "4.5", "81天", "正常"),
                ("iPad Mini", "15", "2", "1.6", "228天", "慢速"),
                ("AirPods", "50", "25", "6.0", "61天", "快速"),
                ("Apple Watch", "20", "8", "4.8", "76天", "正常"),
            ]

            for row, (name, stock, sales, rate, days, status) in enumerate(
                sample_turnover
            ):
                turnover_table.setItem(row, 0, QTableWidgetItem(name))
                turnover_table.setItem(row, 1, QTableWidgetItem(stock))
                turnover_table.setItem(row, 2, QTableWidgetItem(sales))
                turnover_table.setItem(row, 3, QTableWidgetItem(rate))
                turnover_table.setItem(row, 4, QTableWidgetItem(days))
                turnover_table.setItem(row, 5, QTableWidgetItem(status))

            turnover_table.setAlternatingRowColors(True)
            turnover_layout.addWidget(turnover_table)

            tab_widget.addTab(turnover_widget, "周转分析")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示库存周转分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示库存周转分析失败:\n{e}")

    def show_profit_analysis(self):
        """显示利润分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget

            dialog = QDialog(self)
            dialog.setWindowTitle("利润分析")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            tab_widget = QTabWidget()

            # 利润统计
            profit_widget = QWidget()
            profit_layout = QVBoxLayout(profit_widget)

            # 利润指标
            profit_stats = QGroupBox("💰 利润指标")
            stats_layout = QGridLayout(profit_stats)

            stats_layout.addWidget(QLabel("总利润:"), 0, 0)
            total_profit = QLabel("¥112,340.00")
            total_profit.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 16px;"
            )
            stats_layout.addWidget(total_profit, 0, 1)

            stats_layout.addWidget(QLabel("平均利润率:"), 0, 2)
            avg_margin = QLabel("30.8%")
            avg_margin.setStyleSheet(
                "font-weight: bold; color: #2196F3; font-size: 16px;"
            )
            stats_layout.addWidget(avg_margin, 0, 3)

            stats_layout.addWidget(QLabel("毛利润:"), 1, 0)
            gross_profit = QLabel("¥156,800.00")
            gross_profit.setStyleSheet(
                "font-weight: bold; color: #FF9800; font-size: 16px;"
            )
            stats_layout.addWidget(gross_profit, 1, 1)

            stats_layout.addWidget(QLabel("净利润率:"), 1, 2)
            net_margin = QLabel("27.1%")
            net_margin.setStyleSheet(
                "font-weight: bold; color: #9C27B0; font-size: 16px;"
            )
            stats_layout.addWidget(net_margin, 1, 3)

            profit_layout.addWidget(profit_stats)

            # 利润分析文本
            profit_analysis = QTextEdit()
            profit_analysis.setMaximumHeight(150)
            profit_analysis.setPlainText(
                "利润分析报告：\n\n"
                "1. 整体表现：本月利润较上月增长18.5%，表现良好\n"
                "2. 高利润商品：电子产品类利润率最高，达到35%\n"
                "3. 成本控制：采购成本控制良好，较计划节省5.2%\n"
                "4. 优化建议：建议增加高利润商品的采购比例"
            )
            profit_analysis.setReadOnly(True)
            profit_layout.addWidget(profit_analysis)

            tab_widget.addTab(profit_widget, "利润分析")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示利润分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示利润分析失败:\n{e}")

    def show_supplier_analysis(self):
        """显示供应商分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget

            dialog = QDialog(self)
            dialog.setWindowTitle("供应商分析")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            tab_widget = QTabWidget()

            # 供应商统计
            supplier_widget = QWidget()
            supplier_layout = QVBoxLayout(supplier_widget)

            # 供应商指标
            supplier_stats = QGroupBox("🏪 供应商指标")
            stats_layout = QGridLayout(supplier_stats)

            stats_layout.addWidget(QLabel("合作供应商:"), 0, 0)
            total_suppliers = QLabel("8家")
            total_suppliers.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(total_suppliers, 0, 1)

            stats_layout.addWidget(QLabel("优质供应商:"), 0, 2)
            good_suppliers = QLabel("5家")
            good_suppliers.setStyleSheet(
                "font-weight: bold; color: #2196F3; font-size: 14px;"
            )
            stats_layout.addWidget(good_suppliers, 0, 3)

            stats_layout.addWidget(QLabel("平均交货期:"), 1, 0)
            avg_delivery = QLabel("3.2天")
            avg_delivery.setStyleSheet(
                "font-weight: bold; color: #FF9800; font-size: 14px;"
            )
            stats_layout.addWidget(avg_delivery, 1, 1)

            stats_layout.addWidget(QLabel("准时交货率:"), 1, 2)
            on_time_rate = QLabel("94.5%")
            on_time_rate.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(on_time_rate, 1, 3)

            supplier_layout.addWidget(supplier_stats)

            # 供应商排行表格
            supplier_table = QTableWidget()
            supplier_table.setColumnCount(6)
            supplier_table.setHorizontalHeaderLabels(
                ["供应商", "合作时长", "订单数", "准时率", "质量评分", "综合评级"]
            )
            supplier_table.setRowCount(8)

            # 示例数据
            sample_suppliers = [
                ("科技供应商A", "2年", "156", "98%", "4.8", "⭐⭐⭐⭐⭐"),
                ("电子产品B", "1.5年", "89", "95%", "4.6", "⭐⭐⭐⭐"),
                ("配件供应商C", "3年", "234", "92%", "4.4", "⭐⭐⭐⭐"),
                ("数码产品D", "1年", "67", "88%", "4.2", "⭐⭐⭐"),
            ]

            for row, (name, duration, orders, rate, score, rating) in enumerate(
                sample_suppliers
            ):
                supplier_table.setItem(row, 0, QTableWidgetItem(name))
                supplier_table.setItem(row, 1, QTableWidgetItem(duration))
                supplier_table.setItem(row, 2, QTableWidgetItem(orders))
                supplier_table.setItem(row, 3, QTableWidgetItem(rate))
                supplier_table.setItem(row, 4, QTableWidgetItem(score))
                supplier_table.setItem(row, 5, QTableWidgetItem(rating))

            supplier_table.setAlternatingRowColors(True)
            supplier_layout.addWidget(supplier_table)

            tab_widget.addTab(supplier_widget, "供应商分析")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示供应商分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示供应商分析失败:\n{e}")

    def export_report(self):
        """导出报告"""
        try:
            from PyQt6.QtWidgets import QFileDialog, QMessageBox
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出数据分析报告",
                f"数据分析报告_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Excel文件 (*.xlsx);;PDF文件 (*.pdf);;所有文件 (*.*)",
            )

            if file_path:
                # TODO: 实现报告导出功能
                QMessageBox.information(
                    self, "提示", f"报告导出功能正在开发中\n保存路径: {file_path}"
                )

        except Exception as e:
            self.logger.error(f"导出报告失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"导出报告失败:\n{e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_sample_data()
