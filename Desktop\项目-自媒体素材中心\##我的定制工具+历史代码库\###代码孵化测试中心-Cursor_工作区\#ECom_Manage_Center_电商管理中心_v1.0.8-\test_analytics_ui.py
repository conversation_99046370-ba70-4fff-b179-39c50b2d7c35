#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析界面测试

专门测试数据分析界面的显示效果。
"""

import sys
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt6.QtCore import Qt, QTimer

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AnalyticsTestWindow(QMainWindow):
    """数据分析测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据分析界面测试 - 1600x1300")
        self.setGeometry(100, 100, 1600, 1300)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🧪 数据分析界面测试")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin: 20px;
            background-color: #2b2b2b;
            padding: 20px;
            border-radius: 10px;
        """)
        layout.addWidget(title)
        
        # 测试按钮
        test_btn = QPushButton("🚀 加载数据分析界面")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        test_btn.clicked.connect(self.load_analytics)
        layout.addWidget(test_btn)
        
        # 状态标签
        self.status_label = QLabel("点击按钮加载数据分析界面...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("""
            font-size: 14px;
            color: white;
            background-color: #1e1e1e;
            padding: 10px;
            border-radius: 5px;
            margin: 10px;
        """)
        layout.addWidget(self.status_label)
        
        # 数据分析界面容器
        self.analytics_container = QWidget()
        self.analytics_container.setStyleSheet("""
            QWidget {
                background-color: #1e1e1e;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(self.analytics_container)
        
        # 设置暗黑主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
        """)
        
    def load_analytics(self):
        """加载数据分析界面"""
        try:
            self.status_label.setText("🔄 正在加载数据分析界面...")
            
            # 导入数据分析模块
            from core.database import DatabaseManager
            from gui.modules.analytics.analytics_widget import AnalyticsWidget
            
            # 创建数据库管理器
            db_manager = DatabaseManager("data/test_analytics.db")
            if not db_manager.initialize():
                self.status_label.setText("❌ 数据库初始化失败")
                return
            
            # 创建数据分析界面
            analytics_widget = AnalyticsWidget(db_manager)
            
            # 添加到容器
            container_layout = QVBoxLayout(self.analytics_container)
            container_layout.addWidget(analytics_widget)
            
            self.status_label.setText("✅ 数据分析界面加载成功！")
            
            # 延迟刷新数据
            QTimer.singleShot(500, analytics_widget.refresh_data)
            
            logging.info("数据分析界面加载完成")
            
        except Exception as e:
            error_msg = f"❌ 加载失败: {e}"
            self.status_label.setText(error_msg)
            logging.error(error_msg)
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("🧪 启动数据分析界面测试")
    print("=" * 50)
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = AnalyticsTestWindow()
    window.show()
    
    print("✅ 测试窗口已启动")
    print("📋 测试说明:")
    print("1. 窗口大小: 1600x1300")
    print("2. 点击按钮加载数据分析界面")
    print("3. 检查界面是否正确显示数据")
    print("4. 验证所有组件是否可见")
    
    # 运行应用
    try:
        exit_code = app.exec()
        print(f"\n应用退出，退出码: {exit_code}")
        return exit_code
    except Exception as e:
        print(f"\n应用运行异常: {e}")
        return 1

if __name__ == "__main__":
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    sys.exit(main())
