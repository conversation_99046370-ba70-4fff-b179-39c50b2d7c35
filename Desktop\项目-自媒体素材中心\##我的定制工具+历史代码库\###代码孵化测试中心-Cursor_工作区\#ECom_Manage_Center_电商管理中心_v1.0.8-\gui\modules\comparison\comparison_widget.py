#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应链对比模块主界面

供应链对比分析功能的完整实现。
"""

import logging
from typing import List, Optional
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QSplitter,
    QGroupBox,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QLineEdit,
    QComboBox,
    QLabel,
    QMessageBox,
    QHeaderView,
    QMenu,
    QToolBar,
    QFrame,
    QTextEdit,
    QProgressBar,
    QGridLayout,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QColor

from utils.logger import LoggerMixin


class ComparisonWidget(QWidget, LoggerMixin):
    """供应链对比主界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager

        # 当前选中的对比组
        self.current_group = None

        # 初始化界面
        self.init_ui()

        # 加载示例数据
        self.load_sample_data()

        self.logger.info("供应链对比界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题区域
        title_frame = self.create_title_section()
        layout.addWidget(title_frame)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：搜索和筛选
        search_frame = self.create_search_section()
        main_splitter.addWidget(search_frame)

        # 下半部分：对比表格
        comparison_frame = self.create_comparison_section()
        main_splitter.addWidget(comparison_frame)

        # 设置分割器比例
        main_splitter.setSizes([120, 600])
        layout.addWidget(main_splitter)

    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet(
            """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """
        )

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 10, 20, 10)

        # 标题
        title = QLabel("📊 供应链对比分析")
        title.setStyleSheet(
            """
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
        """
        )
        layout.addWidget(title)

        layout.addStretch()

        # 操作按钮
        btn_layout = QHBoxLayout()

        add_btn = QPushButton("新建对比")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """
        )
        add_btn.clicked.connect(self.add_comparison)
        btn_layout.addWidget(add_btn)

        edit_btn = QPushButton("编辑对比")
        edit_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """
        )
        edit_btn.clicked.connect(self.edit_comparison)
        btn_layout.addWidget(edit_btn)

        delete_btn = QPushButton("删除对比")
        delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(244, 67, 54, 0.8);
                color: white;
                border: 1px solid rgba(244, 67, 54, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(244, 67, 54, 1.0);
            }
        """
        )
        delete_btn.clicked.connect(self.delete_comparison)
        btn_layout.addWidget(delete_btn)

        layout.addLayout(btn_layout)

        return frame

    def create_search_section(self):
        """创建搜索区域"""
        frame = QGroupBox("🔍 搜索筛选")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
        """
        )

        layout = QHBoxLayout(frame)

        # 商品搜索
        search_label = QLabel("商品:")
        search_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(search_label)

        self.product_search = QLineEdit()
        self.product_search.setPlaceholderText("输入商品名称...")
        self.product_search.setStyleSheet(
            """
            QLineEdit {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                padding: 6px;
            }
        """
        )
        layout.addWidget(self.product_search)

        # 供应商筛选
        supplier_label = QLabel("供应商:")
        supplier_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(supplier_label)

        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems(
            ["全部", "阿里巴巴", "1688", "淘宝", "京东", "拼多多"]
        )
        self.supplier_combo.setStyleSheet(
            """
            QComboBox {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                padding: 6px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """
        )
        layout.addWidget(self.supplier_combo)

        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """
        )
        search_btn.clicked.connect(self.search_products)
        layout.addWidget(search_btn)

        return frame

    def create_comparison_section(self):
        """创建对比表格区域"""
        frame = QGroupBox("📋 商品对比结果")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2196F3;
            }
        """
        )

        layout = QVBoxLayout(frame)

        # 对比表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setColumnCount(9)
        self.comparison_table.setHorizontalHeaderLabels(
            [
                "商品名称",
                "供应商",
                "价格",
                "最小起订量",
                "发货地",
                "评分",
                "销量",
                "运费",
                "综合评价",
            ]
        )

        # 设置表格样式
        self.comparison_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                gridline-color: #555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QTableWidget::item:selected {
                background-color: #2196F3;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """
        )

        # 设置表格属性
        self.comparison_table.setAlternatingRowColors(True)
        self.comparison_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.comparison_table.setSelectionMode(
            QTableWidget.SelectionMode.SingleSelection
        )
        header = self.comparison_table.horizontalHeader()
        header.setStretchLastSection(True)

        # 连接选择事件
        self.comparison_table.itemSelectionChanged.connect(self.on_comparison_selected)

        layout.addWidget(self.comparison_table)

        return frame

    def load_sample_data(self):
        """加载示例数据"""
        # 示例对比数据
        sample_data = [
            [
                "iPhone 14 手机壳",
                "阿里巴巴供应商A",
                "¥15.50",
                "50件",
                "广东深圳",
                "4.8⭐",
                "月销1000+",
                "¥8.00",
                "优秀",
            ],
            [
                "iPhone 14 手机壳",
                "1688供应商B",
                "¥12.80",
                "100件",
                "广东东莞",
                "4.5⭐",
                "月销500+",
                "¥10.00",
                "良好",
            ],
            [
                "iPhone 14 手机壳",
                "淘宝供应商C",
                "¥18.00",
                "20件",
                "浙江义乌",
                "4.9⭐",
                "月销2000+",
                "¥6.00",
                "优秀",
            ],
            [
                "蓝牙耳机",
                "京东供应商D",
                "¥45.00",
                "10件",
                "北京",
                "4.7⭐",
                "月销800+",
                "免运费",
                "优秀",
            ],
            [
                "蓝牙耳机",
                "拼多多供应商E",
                "¥38.50",
                "30件",
                "广东深圳",
                "4.3⭐",
                "月销300+",
                "¥12.00",
                "一般",
            ],
        ]

        self.comparison_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))

                # 根据列设置特殊样式
                if col == 2:  # 价格列
                    if "¥12" in value or "¥38" in value:
                        item.setBackground(QColor("#4CAF50"))  # 绿色表示低价
                elif col == 5:  # 评分列
                    if "4.8" in value or "4.9" in value:
                        item.setBackground(QColor("#FF9800"))  # 橙色表示高评分
                elif col == 8:  # 综合评价列
                    if value == "优秀":
                        item.setBackground(QColor("#4CAF50"))
                    elif value == "良好":
                        item.setBackground(QColor("#FF9800"))
                    elif value == "一般":
                        item.setBackground(QColor("#F44336"))

                self.comparison_table.setItem(row, col, item)

    def on_comparison_selected(self):
        """对比项选择事件"""
        current_row = self.comparison_table.currentRow()
        if current_row >= 0:
            product_name = self.comparison_table.item(current_row, 0).text()
            self.logger.info(f"选中对比项: {product_name}")

    def add_comparison(self):
        """添加新对比"""
        from PyQt6.QtWidgets import (
            QDialog,
            QFormLayout,
            QDialogButtonBox,
            QSpinBox,
            QDoubleSpinBox,
        )

        dialog = QDialog(self)
        dialog.setWindowTitle("新建供应链对比")
        dialog.setModal(True)
        dialog.resize(450, 350)

        layout = QFormLayout(dialog)

        # 商品信息输入
        product_edit = QLineEdit()
        layout.addRow("商品名称:", product_edit)

        supplier_edit = QLineEdit()
        layout.addRow("供应商:", supplier_edit)

        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0.01)
        price_spin.setMaximum(99999.99)
        price_spin.setDecimals(2)
        price_spin.setPrefix("¥")
        layout.addRow("价格:", price_spin)

        moq_spin = QSpinBox()
        moq_spin.setMinimum(1)
        moq_spin.setMaximum(99999)
        moq_spin.setSuffix("件")
        layout.addRow("最小起订量:", moq_spin)

        location_edit = QLineEdit()
        layout.addRow("发货地:", location_edit)

        rating_spin = QDoubleSpinBox()
        rating_spin.setMinimum(1.0)
        rating_spin.setMaximum(5.0)
        rating_spin.setDecimals(1)
        rating_spin.setSuffix("⭐")
        rating_spin.setValue(4.0)
        layout.addRow("评分:", rating_spin)

        sales_edit = QLineEdit()
        sales_edit.setPlaceholderText("如: 月销1000+")
        layout.addRow("销量:", sales_edit)

        shipping_spin = QDoubleSpinBox()
        shipping_spin.setMinimum(0.00)
        shipping_spin.setMaximum(999.99)
        shipping_spin.setDecimals(2)
        shipping_spin.setPrefix("¥")
        layout.addRow("运费:", shipping_spin)

        evaluation_combo = QComboBox()
        evaluation_combo.addItems(["优秀", "良好", "一般", "较差"])
        layout.addRow("综合评价:", evaluation_combo)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 添加到表格
            row_count = self.comparison_table.rowCount()
            self.comparison_table.insertRow(row_count)

            # 填充数据
            items = [
                product_edit.text(),
                supplier_edit.text(),
                f"¥{price_spin.value():.2f}",
                f"{moq_spin.value()}件",
                location_edit.text(),
                f"{rating_spin.value():.1f}⭐",
                sales_edit.text(),
                (
                    f"¥{shipping_spin.value():.2f}"
                    if shipping_spin.value() > 0
                    else "免运费"
                ),
                evaluation_combo.currentText(),
            ]

            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)

                # 根据列设置特殊样式
                if col == 2:  # 价格列
                    if price_spin.value() < 20:
                        item.setBackground(QColor("#4CAF50"))  # 绿色表示低价
                elif col == 5:  # 评分列
                    if rating_spin.value() >= 4.5:
                        item.setBackground(QColor("#FF9800"))  # 橙色表示高评分
                elif col == 8:  # 综合评价列
                    if item_text == "优秀":
                        item.setBackground(QColor("#4CAF50"))
                    elif item_text == "良好":
                        item.setBackground(QColor("#FF9800"))
                    elif item_text == "一般":
                        item.setBackground(QColor("#F44336"))

                self.comparison_table.setItem(row_count, col, item)

            QMessageBox.information(self, "成功", "对比项添加成功！")

    def edit_comparison(self):
        """编辑对比"""
        current_row = self.comparison_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要编辑的对比项")
            return

        from PyQt6.QtWidgets import (
            QDialog,
            QFormLayout,
            QDialogButtonBox,
            QSpinBox,
            QDoubleSpinBox,
        )

        dialog = QDialog(self)
        dialog.setWindowTitle("编辑供应链对比")
        dialog.setModal(True)
        dialog.resize(450, 350)

        layout = QFormLayout(dialog)

        # 获取当前数据
        product = self.comparison_table.item(current_row, 0).text()
        supplier = self.comparison_table.item(current_row, 1).text()
        price = float(
            self.comparison_table.item(current_row, 2).text().replace("¥", "")
        )
        moq = int(self.comparison_table.item(current_row, 3).text().replace("件", ""))
        location = self.comparison_table.item(current_row, 4).text()
        rating = float(
            self.comparison_table.item(current_row, 5).text().replace("⭐", "")
        )
        sales = self.comparison_table.item(current_row, 6).text()
        shipping_text = self.comparison_table.item(current_row, 7).text()
        shipping = (
            0.0 if shipping_text == "免运费" else float(shipping_text.replace("¥", ""))
        )
        evaluation = self.comparison_table.item(current_row, 8).text()

        # 创建编辑控件
        product_edit = QLineEdit(product)
        layout.addRow("商品名称:", product_edit)

        supplier_edit = QLineEdit(supplier)
        layout.addRow("供应商:", supplier_edit)

        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0.01)
        price_spin.setMaximum(99999.99)
        price_spin.setDecimals(2)
        price_spin.setPrefix("¥")
        price_spin.setValue(price)
        layout.addRow("价格:", price_spin)

        moq_spin = QSpinBox()
        moq_spin.setMinimum(1)
        moq_spin.setMaximum(99999)
        moq_spin.setSuffix("件")
        moq_spin.setValue(moq)
        layout.addRow("最小起订量:", moq_spin)

        location_edit = QLineEdit(location)
        layout.addRow("发货地:", location_edit)

        rating_spin = QDoubleSpinBox()
        rating_spin.setMinimum(1.0)
        rating_spin.setMaximum(5.0)
        rating_spin.setDecimals(1)
        rating_spin.setSuffix("⭐")
        rating_spin.setValue(rating)
        layout.addRow("评分:", rating_spin)

        sales_edit = QLineEdit(sales)
        layout.addRow("销量:", sales_edit)

        shipping_spin = QDoubleSpinBox()
        shipping_spin.setMinimum(0.00)
        shipping_spin.setMaximum(999.99)
        shipping_spin.setDecimals(2)
        shipping_spin.setPrefix("¥")
        shipping_spin.setValue(shipping)
        layout.addRow("运费:", shipping_spin)

        evaluation_combo = QComboBox()
        evaluation_combo.addItems(["优秀", "良好", "一般", "较差"])
        evaluation_combo.setCurrentText(evaluation)
        layout.addRow("综合评价:", evaluation_combo)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addRow(button_box)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 更新表格数据
            items = [
                product_edit.text(),
                supplier_edit.text(),
                f"¥{price_spin.value():.2f}",
                f"{moq_spin.value()}件",
                location_edit.text(),
                f"{rating_spin.value():.1f}⭐",
                sales_edit.text(),
                (
                    f"¥{shipping_spin.value():.2f}"
                    if shipping_spin.value() > 0
                    else "免运费"
                ),
                evaluation_combo.currentText(),
            ]

            for col, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)

                # 根据列设置特殊样式
                if col == 2:  # 价格列
                    if price_spin.value() < 20:
                        item.setBackground(QColor("#4CAF50"))  # 绿色表示低价
                elif col == 5:  # 评分列
                    if rating_spin.value() >= 4.5:
                        item.setBackground(QColor("#FF9800"))  # 橙色表示高评分
                elif col == 8:  # 综合评价列
                    if item_text == "优秀":
                        item.setBackground(QColor("#4CAF50"))
                    elif item_text == "良好":
                        item.setBackground(QColor("#FF9800"))
                    elif item_text == "一般":
                        item.setBackground(QColor("#F44336"))

                self.comparison_table.setItem(current_row, col, item)

            QMessageBox.information(self, "成功", "对比项更新成功！")

    def delete_comparison(self):
        """删除对比"""
        current_row = self.comparison_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的对比项")
            return

        product_name = self.comparison_table.item(current_row, 0).text()
        supplier = self.comparison_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除对比项吗？\n商品: {product_name}\n供应商: {supplier}\n\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.comparison_table.removeRow(current_row)
            QMessageBox.information(self, "成功", "对比项删除成功！")

    def search_products(self):
        """搜索商品"""
        product_name = self.product_search.text().strip().lower()
        supplier = self.supplier_combo.currentText()

        # 如果搜索框为空且选择"全部"，显示所有行
        if not product_name and supplier == "全部":
            for row in range(self.comparison_table.rowCount()):
                self.comparison_table.setRowHidden(row, False)
            return

        # 搜索匹配的行
        for row in range(self.comparison_table.rowCount()):
            match_found = False

            # 检查商品名称
            product_item = self.comparison_table.item(row, 0)
            product_match = not product_name or (
                product_item and product_name in product_item.text().lower()
            )

            # 检查供应商
            supplier_item = self.comparison_table.item(row, 1)
            supplier_match = supplier == "全部" or (
                supplier_item and supplier in supplier_item.text()
            )

            match_found = product_match and supplier_match
            self.comparison_table.setRowHidden(row, not match_found)
