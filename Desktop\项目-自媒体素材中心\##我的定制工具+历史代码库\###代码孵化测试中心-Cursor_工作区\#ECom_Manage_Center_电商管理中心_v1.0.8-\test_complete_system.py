#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台库存管理系统完整测试

测试整个多平台库存管理系统的完整功能，包括所有模块的集成测试。
"""

import os
import sys
import uuid
from pathlib import Path
from decimal import Decimal

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.database import DatabaseManager
from core.managers.product_manager import ProductManager
from core.managers.order_manager import OrderManager
from core.managers.mapping_manager import MappingManager
from core.api.platform_factory import PlatformAPIFactory
from core.services.order_sync_service import OrderSyncService
from core.models.product import Product


def test_complete_multiplatform_system():
    """测试完整的多平台库存管理系统"""
    print("\n" + "=" * 80)
    print("🚀 多平台库存管理系统完整测试")
    print("=" * 80)
    
    # 初始化数据库
    db_path = "data/test_complete_system.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    db_manager = DatabaseManager(db_path)
    
    try:
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False
        
        print("✅ 数据库初始化成功")
        
        # 初始化所有管理器
        product_manager = ProductManager(db_manager)
        order_manager = OrderManager(db_manager)
        mapping_manager = MappingManager(db_manager)
        api_factory = PlatformAPIFactory()
        sync_service = OrderSyncService(db_manager)
        
        print("✅ 所有管理器初始化成功")
        
        # 步骤1: 创建商品并启用多平台
        print("\n📦 步骤1: 创建商品并启用多平台")
        
        test_products = [
            Product(
                product_id=f"MP_PHONE_{uuid.uuid4().hex[:6]}",
                name="多平台智能手机",
                category="电子产品",
                selling_price=2999.99,
                quantity=100,
                min_stock_threshold=10,
                max_stock_threshold=500
            ),
            Product(
                product_id=f"MP_CASE_{uuid.uuid4().hex[:6]}",
                name="多平台手机壳",
                category="配件",
                selling_price=29.99,
                quantity=500,
                inventory_type="dropship"
            )
        ]
        
        for product in test_products:
            if product_manager.create_product(product):
                print(f"   ✅ 创建商品: {product.name}")
                
                # 启用多平台功能
                if product_manager.enable_multi_platform(product.product_id):
                    print(f"   ✅ 启用多平台: {product.name}")
                else:
                    print(f"   ❌ 启用多平台失败: {product.name}")
                    return False
            else:
                print(f"   ❌ 创建商品失败: {product.name}")
                return False
        
        # 步骤2: 创建平台映射
        print("\n🔗 步骤2: 创建平台映射")
        
        platforms = ['taobao', 'xiaohongshu', 'douyin']
        mapping_count = 0
        
        for product in test_products:
            for platform in platforms:
                platform_product_id = f"{platform.upper()}_{product.product_id}"
                
                if mapping_manager.create_mapping(
                    sku_id=product.product_id,
                    platform_type=platform,
                    platform_product_id=platform_product_id,
                    notes=f"{platform}平台映射"
                ):
                    mapping_count += 1
                    print(f"   ✅ 创建映射: {product.name} -> {platform}")
                else:
                    print(f"   ❌ 创建映射失败: {product.name} -> {platform}")
                    return False
        
        print(f"   📊 总计创建 {mapping_count} 个平台映射")
        
        # 步骤3: 模拟平台订单处理
        print("\n📋 步骤3: 模拟平台订单处理")
        
        # 模拟淘宝订单
        taobao_order_data = {
            'tid': f'TB_ORDER_{uuid.uuid4().hex[:8]}',
            'buyer_nick': 'test_buyer_001',
            'total_fee': 2999.99,
            'receiver_address': '北京市朝阳区测试地址123号',
            'orders': {
                'order': {
                    'num_iid': f"TAOBAO_{test_products[0].product_id}",
                    'title': test_products[0].name,
                    'price': 2999.99,
                    'num': 1
                }
            }
        }
        
        matched_products = [
            {
                'sku_id': test_products[0].product_id,
                'quantity': 1,
                'price': 2999.99
            }
        ]
        
        if order_manager.create_platform_order(taobao_order_data, 'taobao', matched_products):
            print("   ✅ 创建淘宝平台订单成功")
        else:
            print("   ❌ 创建淘宝平台订单失败")
            return False
        
        # 步骤4: 测试智能匹配
        print("\n🧠 步骤4: 测试智能匹配")
        
        # 模拟未知商品的订单
        unknown_order = {
            'product_name': '智能手机',
            'price': 2999.99
        }
        
        matches = mapping_manager.smart_match_products(unknown_order, 0.5)
        if matches:
            print(f"   ✅ 智能匹配成功: 找到 {len(matches)} 个匹配项")
            for match in matches:
                print(f"      - {match['product_name']} (置信度: {match['confidence']:.2f})")
        else:
            print("   ⚠️ 智能匹配未找到结果")
        
        # 步骤5: 测试代发订单处理
        print("\n🚚 步骤5: 测试代发订单处理")
        
        # 创建代发商品订单
        dropship_order_data = {
            'tid': f'XHS_ORDER_{uuid.uuid4().hex[:8]}',
            'buyer_nick': 'test_buyer_002',
            'total_fee': 29.99,
            'receiver_address': '上海市浦东新区测试地址456号'
        }
        
        dropship_matched = [
            {
                'sku_id': test_products[1].product_id,
                'quantity': 2,
                'price': 29.99
            }
        ]
        
        if order_manager.create_platform_order(dropship_order_data, 'xiaohongshu', dropship_matched):
            print("   ✅ 创建小红书代发订单成功")
            
            # 获取创建的订单
            platform_orders = order_manager.get_platform_orders('xiaohongshu')
            if platform_orders:
                order_id = platform_orders[0].order_id
                if order_manager.process_dropship_order(order_id):
                    print("   ✅ 代发订单处理成功")
                else:
                    print("   ❌ 代发订单处理失败")
                    return False
        else:
            print("   ❌ 创建小红书代发订单失败")
            return False
        
        # 步骤6: 测试库存管理
        print("\n📊 步骤6: 测试库存管理")
        
        # 获取多平台商品
        multi_platform_products = product_manager.get_multi_platform_products()
        print(f"   📦 多平台商品数量: {len(multi_platform_products)}")
        
        # 获取低库存商品
        low_stock_products = product_manager.get_low_stock_products()
        print(f"   ⚠️ 低库存商品数量: {len(low_stock_products)}")
        
        # 测试库存阈值设置
        for product in test_products:
            if product_manager.set_stock_thresholds(product.product_id, 5, 1000):
                print(f"   ✅ 设置库存阈值: {product.name}")
            else:
                print(f"   ❌ 设置库存阈值失败: {product.name}")
                return False
        
        # 步骤7: 测试订单状态管理
        print("\n🔄 步骤7: 测试订单状态管理")
        
        # 获取所有平台订单
        all_platform_orders = order_manager.get_platform_orders()
        print(f"   📋 平台订单总数: {len(all_platform_orders)}")
        
        # 更新订单状态
        for order in all_platform_orders:
            tracking_info = {
                'tracking_number': f'SF{uuid.uuid4().hex[:10]}',
                'carrier': '顺丰快递',
                'status': 'shipped'
            }
            
            if order_manager.update_platform_order_status(
                order.platform_order_id,
                order.source_platform,
                'shipped',
                tracking_info
            ):
                print(f"   ✅ 更新订单状态: {order.platform_order_id}")
            else:
                print(f"   ❌ 更新订单状态失败: {order.platform_order_id}")
                return False
        
        # 步骤8: 系统统计和报告
        print("\n📈 步骤8: 系统统计和报告")
        
        # 商品统计
        product_stats = product_manager.get_statistics()
        print(f"   📦 商品统计:")
        print(f"      - 总商品数: {product_stats['total_products']}")
        print(f"      - 总价值: ¥{product_stats['total_value']:.2f}")
        print(f"      - 低库存商品: {product_stats['low_stock_products']}")
        
        # 订单统计
        order_stats = order_manager.get_statistics()
        print(f"   📋 订单统计:")
        print(f"      - 总订单数: {order_stats['total_orders']}")
        print(f"      - 总金额: ¥{order_stats['total_amount']:.2f}")
        print(f"      - 代发订单数: {order_stats['dropship_orders']}")
        
        # 映射统计
        unmatched_orders = order_manager.get_unmatched_orders()
        print(f"   🔗 映射统计:")
        print(f"      - 未匹配订单: {len(unmatched_orders)}")
        
        db_manager.close()
        print("\n✅ 多平台库存管理系统完整测试成功！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 系统测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 多平台库存管理系统 v2.1.0 完整测试")
    print("=" * 100)
    
    if test_complete_multiplatform_system():
        print("\n" + "=" * 100)
        print("🎉 恭喜！多平台库存管理系统升级完成！")
        print("=" * 100)
        print("\n🚀 系统功能总览:")
        print("1. ✅ 数据库扩展 - 支持多平台映射和订单管理")
        print("2. ✅ API集成模块 - 支持淘宝、小红书、抖音、1688等平台")
        print("3. ✅ 智能商品匹配 - 基于名称和价格的智能匹配算法")
        print("4. ✅ 订单自动同步 - 定时拉取各平台订单并自动处理")
        print("5. ✅ 代发订单管理 - 自动识别和处理代发商品")
        print("6. ✅ 库存实时同步 - 多平台库存状态实时同步")
        print("7. ✅ 异常处理机制 - 完善的错误处理和人工干预")
        print("8. ✅ 统计分析功能 - 全面的业务数据统计和分析")
        
        print("\n📋 下一步建议:")
        print("1. 开发用户界面 (Phase 4)")
        print("2. 完善API对接 (获取真实API凭证)")
        print("3. 性能优化和测试")
        print("4. 部署和上线")
        
    else:
        print("\n❌ 系统测试失败，请检查相关功能。")
    
    input("\n按任意键退出...")


if __name__ == "__main__":
    main()
