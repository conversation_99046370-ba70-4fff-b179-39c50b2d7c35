#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单同步服务

负责从各个平台自动拉取订单，进行商品匹配，并创建本地订单记录。
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import threading

from core.database import DatabaseManager
from core.managers.mapping_manager import MappingManager
from core.managers.order_manager import OrderManager
from core.api.platform_factory import PlatformAPIFactory
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class OrderSyncService(LoggerMixin):
    """订单同步服务"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化订单同步服务

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.mapping_manager = MappingManager(db_manager)
        self.order_manager = OrderManager(db_manager)
        self.api_factory = PlatformAPIFactory()

        self._running = False
        self._sync_thread = None

        # 从系统设置获取配置
        self.sync_interval = int(
            self.db_manager.get_setting("order_sync_interval_minutes", "15")
        )
        auto_matching_setting = self.db_manager.get_setting(
            "auto_matching_enabled", "true"
        )
        self.auto_matching_enabled = str(auto_matching_setting).lower() == "true"
        self.confidence_threshold = float(
            self.db_manager.get_setting("matching_confidence_threshold", "0.8")
        )

        self.logger.info("订单同步服务初始化完成")

    def start_sync(self):
        """启动订单同步服务"""
        if self._running:
            self.logger.warning("订单同步服务已在运行")
            return

        self._running = True
        self._sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
        self._sync_thread.start()

        self.logger.info("订单同步服务已启动")

    def stop_sync(self):
        """停止订单同步服务"""
        self._running = False
        if self._sync_thread:
            self._sync_thread.join(timeout=10)

        self.logger.info("订单同步服务已停止")

    def _sync_loop(self):
        """同步循环"""
        while self._running:
            try:
                self.sync_all_platforms()

                # 等待下次同步
                for _ in range(self.sync_interval * 60):  # 转换为秒
                    if not self._running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"订单同步循环异常: {e}")
                time.sleep(60)  # 异常时等待1分钟再重试

    @handle_errors(default_return=False)
    def sync_all_platforms(self) -> bool:
        """同步所有平台的订单"""
        self.logger.info("开始同步所有平台订单")

        # 获取启用的平台配置
        platforms = self._get_enabled_platforms()

        total_synced = 0
        for platform_config in platforms:
            try:
                synced_count = self.sync_platform_orders(
                    platform_config["platform_type"], platform_config["api_config"]
                )
                total_synced += synced_count

            except Exception as e:
                self.logger.error(
                    f"同步平台 {platform_config['platform_type']} 订单失败: {e}"
                )

        self.logger.info(f"订单同步完成，共同步 {total_synced} 个订单")
        return True

    @handle_errors(default_return=0)
    def sync_platform_orders(
        self, platform_type: str, api_config: Dict[str, Any]
    ) -> int:
        """
        同步指定平台的订单

        Args:
            platform_type: 平台类型
            api_config: API配置

        Returns:
            int: 同步的订单数量
        """
        self.logger.info(f"开始同步 {platform_type} 平台订单")

        # 创建API客户端
        api_client = self.api_factory.get_client(platform_type, api_config)
        if not api_client:
            self.logger.error(f"无法创建 {platform_type} API客户端")
            return 0

        # 获取最近的订单（最近24小时）
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)

        try:
            orders = api_client.get_orders(start_time=start_time, end_time=end_time)

            synced_count = 0
            for order_data in orders:
                if self._process_order(platform_type, order_data):
                    synced_count += 1

            self.logger.info(
                f"{platform_type} 平台订单同步完成，共处理 {synced_count} 个订单"
            )
            return synced_count

        except Exception as e:
            self.logger.error(f"同步 {platform_type} 订单异常: {e}")
            return 0
        finally:
            api_client.close()

    @handle_errors(default_return=False)
    def _process_order(self, platform_type: str, order_data: Dict[str, Any]) -> bool:
        """
        处理单个订单

        Args:
            platform_type: 平台类型
            order_data: 订单数据

        Returns:
            bool: 处理是否成功
        """
        platform_order_id = order_data.get("tid") or order_data.get("order_id")
        if not platform_order_id:
            self.logger.warning("订单缺少ID信息")
            return False

        # 检查订单是否已存在
        if self._order_exists(platform_type, platform_order_id):
            return True  # 已存在，跳过

        # 处理订单中的商品
        order_items = self._extract_order_items(order_data)

        for item in order_items:
            platform_product_id = item.get("num_iid") or item.get("product_id")
            product_name = item.get("title") or item.get("name", "")

            if not platform_product_id:
                continue

            # 尝试匹配商品
            matched_sku = self._match_product(
                platform_type, platform_product_id, product_name, item.get("price", 0)
            )

            # 记录匹配日志
            if matched_sku:
                self.mapping_manager.log_order_matching(
                    platform_order_id=platform_order_id,
                    platform_type=platform_type,
                    platform_product_id=platform_product_id,
                    product_name=product_name,
                    matching_status="auto_matched",
                    matched_sku_id=matched_sku["sku_id"],
                    confidence_score=matched_sku.get("confidence", 1.0),
                    matching_method="auto_mapping",
                )
            else:
                self.mapping_manager.log_order_matching(
                    platform_order_id=platform_order_id,
                    platform_type=platform_type,
                    platform_product_id=platform_product_id,
                    product_name=product_name,
                    matching_status="pending",
                    error_reason="no_mapping_found",
                )

        return True

    def _match_product(
        self,
        platform_type: str,
        platform_product_id: str,
        product_name: str,
        price: float,
    ) -> Optional[Dict[str, Any]]:
        """
        匹配商品

        Args:
            platform_type: 平台类型
            platform_product_id: 平台商品ID
            product_name: 商品名称
            price: 商品价格

        Returns:
            Optional[Dict[str, Any]]: 匹配结果
        """
        # 首先尝试精确匹配
        mapping = self.mapping_manager.get_mapping_by_platform_id(
            platform_type, platform_product_id
        )

        if mapping:
            return {
                "sku_id": mapping["sku_id"],
                "confidence": 1.0,
                "method": "exact_mapping",
            }

        # 如果启用了自动匹配，尝试智能匹配
        if self.auto_matching_enabled:
            matches = self.mapping_manager.smart_match_products(
                {"product_name": product_name, "price": price},
                confidence_threshold=self.confidence_threshold,
            )

            if matches:
                best_match = matches[0]

                # 自动创建映射关系
                self.mapping_manager.create_mapping(
                    sku_id=best_match["product_id"],
                    platform_type=platform_type,
                    platform_product_id=platform_product_id,
                    notes=f"自动匹配创建，置信度: {best_match['confidence']:.2f}",
                )

                return {
                    "sku_id": best_match["product_id"],
                    "confidence": best_match["confidence"],
                    "method": "smart_matching",
                }

        return None

    def _extract_order_items(self, order_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从订单数据中提取商品项"""
        # 不同平台的订单结构可能不同，这里做统一处理
        items = []

        # 淘宝订单结构
        if "orders" in order_data and "order" in order_data["orders"]:
            items = order_data["orders"]["order"]
            if not isinstance(items, list):
                items = [items]

        # 其他平台可以在这里添加处理逻辑

        return items

    def _order_exists(self, platform_type: str, platform_order_id: str) -> bool:
        """检查订单是否已存在"""
        sql = """
        SELECT COUNT(*) FROM order_matching_logs 
        WHERE platform_type = ? AND platform_order_id = ?
        """

        result = self.db_manager.fetch_one(sql, (platform_type, platform_order_id))
        return result and result[0] > 0

    def _get_enabled_platforms(self) -> List[Dict[str, Any]]:
        """获取启用的平台配置"""
        # 这里应该从数据库或配置文件获取平台配置
        # 示例配置
        return [
            {
                "platform_type": "taobao",
                "api_config": {
                    "app_key": "your_app_key",
                    "app_secret": "your_app_secret",
                    "session_key": "your_session_key",
                },
            }
            # 其他平台配置...
        ]
