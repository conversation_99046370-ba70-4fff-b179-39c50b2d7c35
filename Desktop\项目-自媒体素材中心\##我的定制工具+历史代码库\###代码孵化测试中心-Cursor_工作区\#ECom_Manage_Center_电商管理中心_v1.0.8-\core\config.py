#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供统一的配置管理功能，支持JSON和YAML格式的配置文件。
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union
import logging


class Config:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为 config/config.json
        """
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        if config_file is None:
            config_file = self.config_dir / "config.json"
        else:
            config_file = Path(config_file)
            
        self.config_file = config_file
        self._config_data = {}
        self._default_config = self._get_default_config()
        
        self.load()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": "电商管理系统",
                "version": "2.0.0",
                "first_run": True,
                "auto_save": True,
                "auto_backup": True
            },
            "database": {
                "path": "data/ecommerce.db",
                "backup_path": "backups/",
                "auto_backup_interval": 24,  # 小时
                "max_backups": 10
            },
            "ui": {
                "theme": "dark",
                "language": "zh_CN",
                "window_size": [1200, 800],
                "window_position": [100, 100],
                "remember_window_state": True,
                "table_page_size": 50,
                "auto_refresh": True,
                "refresh_interval": 30  # 秒
            },
            "api": {
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1,
                "rate_limit": 1000,  # 每小时
                "user_agent": "EcommerceMgmt/2.0.0"
            },
            "sync": {
                "enabled": True,
                "interval": 30,  # 分钟
                "batch_size": 100,
                "max_concurrent": 5
            },
            "logging": {
                "level": "INFO",
                "file": "logs/app.log",
                "max_size": "10MB",
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "security": {
                "encrypt_credentials": True,
                "session_timeout": 3600,  # 秒
                "max_login_attempts": 5
            },
            "storage": {
                "image_path": "images/",
                "export_path": "exports/",
                "temp_path": "temp/",
                "max_image_size": "5MB",
                "allowed_image_formats": ["jpg", "jpeg", "png", "gif", "bmp"]
            },
            "platforms": {
                "1688": {
                    "enabled": False,
                    "app_key": "",
                    "app_secret": "",
                    "redirect_uri": "http://localhost:8080/callback"
                },
                "taobao": {
                    "enabled": False,
                    "app_key": "",
                    "app_secret": "",
                    "redirect_uri": "http://localhost:8080/callback"
                },
                "pdd": {
                    "enabled": False,
                    "client_id": "",
                    "client_secret": "",
                    "redirect_uri": "http://localhost:8080/callback"
                }
            },
            "notifications": {
                "enabled": True,
                "sound": True,
                "desktop": True,
                "email": False,
                "email_settings": {
                    "smtp_server": "",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "use_tls": True
                }
            }
        }
    
    def load(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
                
                # 合并默认配置（确保新增的配置项存在）
                self._config_data = self._merge_config(
                    self._default_config, 
                    self._config_data
                )
            else:
                # 使用默认配置
                self._config_data = self._default_config.copy()
                self.save()  # 保存默认配置到文件
            
            return True
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            self._config_data = self._default_config.copy()
            return False
    
    def save(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'database.path'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config_data
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        try:
            keys = key.split('.')
            config = self._config_data
            
            # 导航到最后一级的父级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            
            # 自动保存
            if self.get('app.auto_save', True):
                self.save()
                
        except Exception as e:
            logging.error(f"设置配置值失败: {e}")
    
    def delete(self, key: str) -> bool:
        """
        删除配置项
        
        Args:
            key: 配置键
            
        Returns:
            bool: 删除是否成功
        """
        try:
            keys = key.split('.')
            config = self._config_data
            
            # 导航到最后一级的父级
            for k in keys[:-1]:
                if k not in config:
                    return False
                config = config[k]
            
            # 删除键
            if keys[-1] in config:
                del config[keys[-1]]
                
                # 自动保存
                if self.get('app.auto_save', True):
                    self.save()
                
                return True
            
            return False
            
        except Exception as e:
            logging.error(f"删除配置项失败: {e}")
            return False
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self._config_data = self._default_config.copy()
        self.save()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config_data.copy()
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """
        合并配置字典
        
        Args:
            default: 默认配置
            user: 用户配置
            
        Returns:
            合并后的配置
        """
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def validate(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必要的配置项
            required_keys = [
                'app.name',
                'app.version',
                'database.path',
                'ui.theme'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    logging.error(f"缺少必要的配置项: {key}")
                    return False
            
            # 检查路径配置
            paths_to_check = [
                'database.backup_path',
                'storage.image_path',
                'storage.export_path',
                'storage.temp_path'
            ]
            
            for path_key in paths_to_check:
                path_value = self.get(path_key)
                if path_value:
                    path_obj = Path(path_value)
                    if not path_obj.is_absolute():
                        # 相对路径，基于项目根目录
                        full_path = self.project_root / path_value
                        full_path.mkdir(parents=True, exist_ok=True)
            
            return True
            
        except Exception as e:
            logging.error(f"配置验证失败: {e}")
            return False
