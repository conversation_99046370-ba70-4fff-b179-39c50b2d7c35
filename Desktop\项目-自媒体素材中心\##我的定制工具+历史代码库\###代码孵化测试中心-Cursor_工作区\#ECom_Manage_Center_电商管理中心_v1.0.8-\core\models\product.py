#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品数据模型

整合版商品模型，基于Inventory_Management_v1.7.3的Product类，
扩展支持多平台、供应商关联等功能。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid
import json


@dataclass
class Product:
    """
    商品数据模型

    整合了库存管理、代发管理、供应链对比等功能的统一商品模型
    """

    # 基础标识信息
    product_id: Optional[str] = None
    name: str = ""
    code: Optional[str] = None  # 商品编码
    category: str = ""
    description: str = ""

    # 库存信息
    quantity: int = 0
    unit: str = "个"
    status: str = "在库"  # 在库、缺货、停售、预售
    location: Optional[str] = None

    # 价格信息
    purchase_price: Decimal = field(default_factory=lambda: Decimal("0"))
    shipping_cost: Decimal = field(default_factory=lambda: Decimal("0"))
    other_cost: Decimal = field(default_factory=lambda: Decimal("0"))
    total_cost: Decimal = field(default_factory=lambda: Decimal("0"))
    selling_price: Decimal = field(default_factory=lambda: Decimal("0"))
    discount_rate: Decimal = field(default_factory=lambda: Decimal("100"))  # 折扣率(%)
    total_profit: Decimal = field(default_factory=lambda: Decimal("0"))

    # 供应商信息
    supplier: Optional[str] = None
    supplier_id: Optional[str] = None
    supplier_link: Optional[str] = None
    purchase_link: Optional[str] = None

    # 销售信息
    selling_link: Optional[str] = None
    purchaser: Optional[str] = None

    # 图片和媒体
    image_path: Optional[str] = None
    images: List[str] = field(default_factory=list)  # 多张图片

    # 扩展属性
    brand: Optional[str] = None
    model: Optional[str] = None
    color: Optional[str] = None
    size: Optional[str] = None
    weight: Optional[Decimal] = None
    tags: List[str] = field(default_factory=list)

    # 平台相关
    platform_products: Dict[str, Dict[str, Any]] = field(
        default_factory=dict
    )  # 平台商品信息

    # 多平台扩展字段
    is_multi_platform: bool = False  # 是否为多平台商品
    platform_count: int = 0  # 关联平台数量
    sync_status: str = "none"  # 同步状态: none, syncing, synced, error
    last_sync_time: Optional[datetime] = None  # 最后同步时间
    sync_error_message: Optional[str] = None  # 同步错误信息
    auto_sync_enabled: bool = True  # 是否启用自动同步
    inventory_type: str = "physical"  # 库存类型: physical, dropship, mixed
    min_stock_threshold: int = 0  # 最低库存阈值
    max_stock_threshold: int = 1000  # 最高库存阈值
    stock_warning_enabled: bool = True  # 是否启用库存预警
    platform_tags: List[str] = field(default_factory=list)  # 平台标签

    # 备注和元数据
    remarks: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.product_id is None:
            self.product_id = self.generate_product_id()

        if self.created_at is None:
            self.created_at = datetime.now()

        self.updated_at = datetime.now()

        # 确保价格字段为Decimal类型
        self._ensure_decimal_fields()

    def generate_product_id(self) -> str:
        """生成商品ID"""
        return f"P{uuid.uuid4().hex[:8].upper()}"

    def _ensure_decimal_fields(self):
        """确保价格字段为Decimal类型"""
        decimal_fields = [
            "purchase_price",
            "shipping_cost",
            "other_cost",
            "total_cost",
            "selling_price",
            "discount_rate",
            "total_profit",
        ]

        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if not isinstance(value, Decimal):
                setattr(self, field_name, Decimal(str(value or 0)))

    def calculate_total_cost(self) -> Decimal:
        """计算总成本"""
        self.total_cost = self.purchase_price + self.shipping_cost + self.other_cost
        return self.total_cost

    def calculate_discounted_price(self) -> Decimal:
        """计算折后价"""
        return self.selling_price * (self.discount_rate / Decimal("100"))

    def calculate_profit(self) -> Decimal:
        """计算预估利润"""
        discounted_price = self.calculate_discounted_price()
        total_cost = self.calculate_total_cost()
        self.total_profit = discounted_price - total_cost
        return self.total_profit

    def calculate_profit_margin(self) -> Decimal:
        """计算利润率(%)"""
        discounted_price = self.calculate_discounted_price()
        if discounted_price > 0:
            return (self.calculate_profit() / discounted_price) * Decimal("100")
        return Decimal("0")

    def add_platform_product(
        self, platform_id: str, platform_product_id: str, platform_data: Dict[str, Any]
    ):
        """添加平台商品信息"""
        self.platform_products[platform_id] = {
            "platform_product_id": platform_product_id,
            "data": platform_data,
            "last_sync": datetime.now().isoformat(),
        }

    def get_platform_product(self, platform_id: str) -> Optional[Dict[str, Any]]:
        """获取平台商品信息"""
        return self.platform_products.get(platform_id)

    def add_image(self, image_path: str):
        """添加图片"""
        if image_path and image_path not in self.images:
            self.images.append(image_path)
            if self.image_path is None:
                self.image_path = image_path

    def remove_image(self, image_path: str):
        """移除图片"""
        if image_path in self.images:
            self.images.remove(image_path)
            if self.image_path == image_path:
                self.image_path = self.images[0] if self.images else None

    def add_tag(self, tag: str):
        """添加标签"""
        if tag and tag not in self.tags:
            self.tags.append(tag)

    def remove_tag(self, tag: str):
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)

    def update_stock(self, quantity_change: int, reason: str = ""):
        """更新库存"""
        old_quantity = self.quantity
        self.quantity += quantity_change

        if self.quantity < 0:
            self.quantity = 0

        # 更新状态
        if self.quantity == 0:
            self.status = "缺货"
        elif self.status == "缺货" and self.quantity > 0:
            self.status = "在库"

        self.updated_at = datetime.now()

        # 记录库存变化到元数据
        if "stock_changes" not in self.metadata:
            self.metadata["stock_changes"] = []

        self.metadata["stock_changes"].append(
            {
                "timestamp": datetime.now().isoformat(),
                "old_quantity": old_quantity,
                "new_quantity": self.quantity,
                "change": quantity_change,
                "reason": reason,
            }
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "product_id": self.product_id,
            "name": self.name,
            "code": self.code,
            "category": self.category,
            "description": self.description,
            "quantity": self.quantity,
            "unit": self.unit,
            "status": self.status,
            "location": self.location,
            "purchase_price": float(self.purchase_price),
            "shipping_cost": float(self.shipping_cost),
            "other_cost": float(self.other_cost),
            "total_cost": float(self.total_cost),
            "selling_price": float(self.selling_price),
            "discount_rate": float(self.discount_rate),
            "total_profit": float(self.total_profit),
            "supplier": self.supplier,
            "supplier_id": self.supplier_id,
            "supplier_link": self.supplier_link,
            "purchase_link": self.purchase_link,
            "selling_link": self.selling_link,
            "purchaser": self.purchaser,
            "image_path": self.image_path,
            "images": json.dumps(self.images),
            "brand": self.brand,
            "model": self.model,
            "color": self.color,
            "size": self.size,
            "weight": float(self.weight) if self.weight else None,
            "tags": json.dumps(self.tags),
            "platform_products": json.dumps(self.platform_products),
            # 多平台字段
            "is_multi_platform": self.is_multi_platform,
            "platform_count": self.platform_count,
            "sync_status": self.sync_status,
            "last_sync_time": (
                self.last_sync_time.isoformat() if self.last_sync_time else None
            ),
            "sync_error_message": self.sync_error_message,
            "auto_sync_enabled": self.auto_sync_enabled,
            "inventory_type": self.inventory_type,
            "min_stock_threshold": self.min_stock_threshold,
            "max_stock_threshold": self.max_stock_threshold,
            "stock_warning_enabled": self.stock_warning_enabled,
            "platform_tags": json.dumps(self.platform_tags),
            "remarks": self.remarks,
            "metadata": json.dumps(self.metadata),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Product":
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get("images"), str):
            data["images"] = json.loads(data["images"]) if data["images"] else []

        if isinstance(data.get("tags"), str):
            data["tags"] = json.loads(data["tags"]) if data["tags"] else []

        if isinstance(data.get("platform_products"), str):
            data["platform_products"] = (
                json.loads(data["platform_products"])
                if data["platform_products"]
                else {}
            )

        if isinstance(data.get("metadata"), str):
            data["metadata"] = json.loads(data["metadata"]) if data["metadata"] else {}

        # 处理多平台字段
        if isinstance(data.get("platform_tags"), str):
            data["platform_tags"] = (
                json.loads(data["platform_tags"]) if data["platform_tags"] else []
            )

        # 处理时间字段
        if isinstance(data.get("created_at"), str):
            data["created_at"] = datetime.fromisoformat(data["created_at"])

        if isinstance(data.get("updated_at"), str):
            data["updated_at"] = datetime.fromisoformat(data["updated_at"])

        if isinstance(data.get("last_sync_time"), str):
            data["last_sync_time"] = datetime.fromisoformat(data["last_sync_time"])

        # 处理Decimal字段
        decimal_fields = [
            "purchase_price",
            "shipping_cost",
            "other_cost",
            "total_cost",
            "selling_price",
            "discount_rate",
            "total_profit",
            "weight",
        ]

        for field in decimal_fields:
            if field in data and data[field] is not None:
                data[field] = Decimal(str(data[field]))

        return cls(**data)

    @classmethod
    def from_db_row(cls, row, columns: List[str]) -> Optional["Product"]:
        """从数据库行创建对象"""
        if not row:
            return None

        data = dict(zip(columns, row))
        return cls.from_dict(data)

    def validate(self) -> bool:
        """验证数据有效性"""
        if not self.name:
            raise ValueError("商品名称不能为空")

        if not self.category:
            raise ValueError("商品分类不能为空")

        if self.quantity < 0:
            raise ValueError("库存数量不能为负数")

        if self.purchase_price < 0:
            raise ValueError("采购价不能为负数")

        if self.shipping_cost < 0:
            raise ValueError("运费不能为负数")

        if self.other_cost < 0:
            raise ValueError("其他成本不能为负数")

        if self.selling_price < 0:
            raise ValueError("销售价不能为负数")

        if not 0 <= self.discount_rate <= 100:
            raise ValueError("折扣率必须在0-100之间")

        return True

    def __str__(self) -> str:
        return f"{self.product_id} - {self.name} ({self.category})"

    def __repr__(self) -> str:
        return f"Product(product_id='{self.product_id}', name='{self.name}', category='{self.category}')"
