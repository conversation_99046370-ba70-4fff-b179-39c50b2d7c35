#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台API工厂

负责创建和管理不同平台的API客户端实例。
"""

import logging
from typing import Dict, Any, Optional
from .base_client import BaseAPIClient
from .clients.taobao_client import TaobaoAPIClient
from .clients.xiaohongshu_client import XiaohongshuAPIClient
from .clients.douyin_client import DouyinAPIClient
from .clients.alibaba_1688_client import Alibaba1688APIClient

from utils.logger import LoggerMixin


class PlatformAPIFactory(LoggerMixin):
    """平台API工厂类"""
    
    # 支持的平台类型映射
    PLATFORM_CLIENTS = {
        'taobao': TaobaoAPIClient,
        'xiaohongshu': XiaohongshuAPIClient,
        'douyin': DouyinAPIClient,
        '1688': Alibaba1688APIClient,
    }
    
    def __init__(self):
        """初始化工厂"""
        self._clients_cache = {}  # 客户端缓存
        self.logger.info("平台API工厂初始化完成")
    
    def create_client(self, platform_type: str, api_config: Dict[str, Any]) -> Optional[BaseAPIClient]:
        """
        创建平台API客户端
        
        Args:
            platform_type: 平台类型 (taobao, xiaohongshu, douyin, 1688)
            api_config: API配置信息
            
        Returns:
            Optional[BaseAPIClient]: API客户端实例
        """
        if platform_type not in self.PLATFORM_CLIENTS:
            self.logger.error(f"不支持的平台类型: {platform_type}")
            return None
        
        # 检查缓存
        cache_key = f"{platform_type}_{hash(str(api_config))}"
        if cache_key in self._clients_cache:
            self.logger.debug(f"从缓存获取 {platform_type} API客户端")
            return self._clients_cache[cache_key]
        
        try:
            # 创建客户端实例
            client_class = self.PLATFORM_CLIENTS[platform_type]
            client = client_class(platform_type, api_config)
            
            # 尝试认证
            if client.authenticate():
                self._clients_cache[cache_key] = client
                self.logger.info(f"成功创建并认证 {platform_type} API客户端")
                return client
            else:
                self.logger.error(f"{platform_type} API客户端认证失败")
                return None
                
        except Exception as e:
            self.logger.error(f"创建 {platform_type} API客户端失败: {e}")
            return None
    
    def get_client(self, platform_type: str, api_config: Dict[str, Any]) -> Optional[BaseAPIClient]:
        """
        获取平台API客户端（优先从缓存获取）
        
        Args:
            platform_type: 平台类型
            api_config: API配置信息
            
        Returns:
            Optional[BaseAPIClient]: API客户端实例
        """
        return self.create_client(platform_type, api_config)
    
    def get_supported_platforms(self) -> list:
        """
        获取支持的平台列表
        
        Returns:
            list: 支持的平台类型列表
        """
        return list(self.PLATFORM_CLIENTS.keys())
    
    def is_platform_supported(self, platform_type: str) -> bool:
        """
        检查平台是否支持
        
        Args:
            platform_type: 平台类型
            
        Returns:
            bool: 是否支持
        """
        return platform_type in self.PLATFORM_CLIENTS
    
    def clear_cache(self):
        """清理客户端缓存"""
        for client in self._clients_cache.values():
            try:
                client.close()
            except Exception as e:
                self.logger.warning(f"关闭客户端连接失败: {e}")
        
        self._clients_cache.clear()
        self.logger.info("API客户端缓存已清理")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            'cached_clients': len(self._clients_cache),
            'platforms': [client.get_platform_type() for client in self._clients_cache.values()],
            'supported_platforms': self.get_supported_platforms()
        }
