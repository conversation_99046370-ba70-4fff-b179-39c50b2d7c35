#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台管理界面

提供多平台API配置和管理功能的用户界面。
"""

import json
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QTableWidget, 
    QTableWidgetItem, QPushButton, QLabel, QLineEdit, QTextEdit,
    QComboBox, QCheckBox, QGroupBox, QFormLayout, QMessageBox,
    QHeaderView, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from core.database import DatabaseManager
from core.api.platform_factory import PlatformAPIFactory
from utils.logger import LoggerMixin


class PlatformConfigWidget(QWidget, LoggerMixin):
    """平台配置组件"""
    
    config_changed = pyqtSignal(str, dict)  # 平台类型, 配置数据
    
    def __init__(self, platform_type: str, parent=None):
        super().__init__(parent)
        self.platform_type = platform_type
        self.config_data = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 平台标题
        title_label = QLabel(f"{self.platform_type.upper()} 平台配置")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 配置表单
        form_group = QGroupBox("API配置")
        form_layout = QFormLayout(form_group)
        
        # 根据平台类型创建不同的配置字段
        self.config_fields = {}
        
        if self.platform_type == 'taobao':
            self.config_fields = {
                'app_key': QLineEdit(),
                'app_secret': QLineEdit(),
                'session_key': QLineEdit(),
                'api_url': QLineEdit('https://eco.taobao.com/router/rest')
            }
        elif self.platform_type == 'xiaohongshu':
            self.config_fields = {
                'app_id': QLineEdit(),
                'app_secret': QLineEdit(),
                'access_token': QLineEdit(),
                'api_url': QLineEdit('https://ark.xiaohongshu.com')
            }
        elif self.platform_type == 'douyin':
            self.config_fields = {
                'app_key': QLineEdit(),
                'app_secret': QLineEdit(),
                'access_token': QLineEdit(),
                'api_url': QLineEdit('https://openapi-fxg.jinritemai.com')
            }
        elif self.platform_type == '1688':
            self.config_fields = {
                'app_key': QLineEdit(),
                'app_secret': QLineEdit(),
                'access_token': QLineEdit(),
                'api_url': QLineEdit('https://gw.open.1688.com/openapi')
            }
        
        # 添加字段到表单
        for field_name, field_widget in self.config_fields.items():
            if 'secret' in field_name or 'token' in field_name:
                field_widget.setEchoMode(QLineEdit.EchoMode.Password)
            
            form_layout.addRow(field_name.replace('_', ' ').title() + ':', field_widget)
            field_widget.textChanged.connect(self.on_config_changed)
        
        layout.addWidget(form_group)
        
        # 高级设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        self.enabled_checkbox = QCheckBox("启用此平台")
        self.enabled_checkbox.setChecked(True)
        self.enabled_checkbox.stateChanged.connect(self.on_config_changed)
        
        self.rate_limit_edit = QLineEdit("1000")
        self.rate_limit_edit.textChanged.connect(self.on_config_changed)
        
        self.sync_interval_edit = QLineEdit("15")
        self.sync_interval_edit.textChanged.connect(self.on_config_changed)
        
        advanced_layout.addRow("启用状态:", self.enabled_checkbox)
        advanced_layout.addRow("API调用限制(每小时):", self.rate_limit_edit)
        advanced_layout.addRow("同步间隔(分钟):", self.sync_interval_edit)
        
        layout.addWidget(advanced_group)
        
        # 测试连接按钮
        test_layout = QHBoxLayout()
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_connection)
        
        self.status_label = QLabel("未测试")
        self.status_label.setStyleSheet("color: gray;")
        
        test_layout.addWidget(self.test_button)
        test_layout.addWidget(self.status_label)
        test_layout.addStretch()
        
        layout.addLayout(test_layout)
        layout.addStretch()
    
    def on_config_changed(self):
        """配置变更处理"""
        self.config_data = self.get_config()
        self.config_changed.emit(self.platform_type, self.config_data)
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        config = {}
        
        # API配置
        for field_name, field_widget in self.config_fields.items():
            config[field_name] = field_widget.text()
        
        # 高级设置
        config['enabled'] = self.enabled_checkbox.isChecked()
        config['rate_limit'] = int(self.rate_limit_edit.text() or 1000)
        config['sync_interval'] = int(self.sync_interval_edit.text() or 15)
        
        return config
    
    def set_config(self, config: Dict[str, Any]):
        """设置配置"""
        self.config_data = config
        
        # 设置API配置
        for field_name, field_widget in self.config_fields.items():
            if field_name in config:
                field_widget.setText(str(config[field_name]))
        
        # 设置高级设置
        if 'enabled' in config:
            self.enabled_checkbox.setChecked(config['enabled'])
        if 'rate_limit' in config:
            self.rate_limit_edit.setText(str(config['rate_limit']))
        if 'sync_interval' in config:
            self.sync_interval_edit.setText(str(config['sync_interval']))
    
    def test_connection(self):
        """测试API连接"""
        self.test_button.setEnabled(False)
        self.status_label.setText("测试中...")
        self.status_label.setStyleSheet("color: orange;")
        
        try:
            # 创建API客户端并测试
            factory = PlatformAPIFactory()
            client = factory.create_client(self.platform_type, self.get_config())
            
            if client and client.authenticate():
                self.status_label.setText("✅ 连接成功")
                self.status_label.setStyleSheet("color: green;")
                QMessageBox.information(self, "测试成功", f"{self.platform_type} 平台连接测试成功！")
            else:
                self.status_label.setText("❌ 连接失败")
                self.status_label.setStyleSheet("color: red;")
                QMessageBox.warning(self, "测试失败", f"{self.platform_type} 平台连接测试失败，请检查配置。")
                
        except Exception as e:
            self.status_label.setText("❌ 连接异常")
            self.status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "测试异常", f"连接测试异常：{str(e)}")
            self.logger.error(f"平台连接测试异常: {e}")
        
        finally:
            self.test_button.setEnabled(True)


class PlatformManagerWidget(QWidget, LoggerMixin):
    """平台管理主界面"""
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.platform_configs = {}
        self.init_ui()
        self.load_configs()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("多平台管理中心")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：平台配置标签页
        self.config_tabs = QTabWidget()
        
        # 支持的平台
        platforms = ['taobao', 'xiaohongshu', 'douyin', '1688']
        self.platform_widgets = {}
        
        for platform in platforms:
            widget = PlatformConfigWidget(platform)
            widget.config_changed.connect(self.on_platform_config_changed)
            self.platform_widgets[platform] = widget
            self.config_tabs.addTab(widget, platform.upper())
        
        splitter.addWidget(self.config_tabs)
        
        # 右侧：状态和操作面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 平台状态
        status_group = QGroupBox("平台状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_table = QTableWidget(0, 4)
        self.status_table.setHorizontalHeaderLabels(['平台', '状态', '最后同步', '操作'])
        self.status_table.horizontalHeader().setStretchLastSection(True)
        status_layout.addWidget(self.status_table)
        
        right_layout.addWidget(status_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_configs)
        
        self.sync_button = QPushButton("立即同步")
        self.sync_button.clicked.connect(self.sync_all_platforms)
        
        self.refresh_button = QPushButton("刷新状态")
        self.refresh_button.clicked.connect(self.refresh_status)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.sync_button)
        button_layout.addWidget(self.refresh_button)
        button_layout.addStretch()
        
        right_layout.addLayout(button_layout)
        right_layout.addStretch()
        
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        
        # 初始化状态表
        self.refresh_status()
    
    def on_platform_config_changed(self, platform_type: str, config: Dict[str, Any]):
        """平台配置变更处理"""
        self.platform_configs[platform_type] = config
        self.logger.debug(f"平台配置已更新: {platform_type}")
    
    def load_configs(self):
        """加载平台配置"""
        try:
            # 从数据库加载配置
            for platform_type, widget in self.platform_widgets.items():
                # 这里可以从数据库或配置文件加载
                # 暂时使用默认配置
                default_config = {
                    'enabled': False,
                    'rate_limit': 1000,
                    'sync_interval': 15
                }
                widget.set_config(default_config)
                
        except Exception as e:
            self.logger.error(f"加载平台配置失败: {e}")
            QMessageBox.warning(self, "加载失败", f"加载平台配置失败：{str(e)}")
    
    def save_configs(self):
        """保存平台配置"""
        try:
            # 保存到数据库或配置文件
            for platform_type, config in self.platform_configs.items():
                # 这里实现保存逻辑
                self.logger.info(f"保存平台配置: {platform_type}")
            
            QMessageBox.information(self, "保存成功", "平台配置已保存！")
            
        except Exception as e:
            self.logger.error(f"保存平台配置失败: {e}")
            QMessageBox.critical(self, "保存失败", f"保存平台配置失败：{str(e)}")
    
    def sync_all_platforms(self):
        """同步所有平台"""
        try:
            # 实现同步逻辑
            QMessageBox.information(self, "同步完成", "所有平台同步完成！")
            self.refresh_status()
            
        except Exception as e:
            self.logger.error(f"平台同步失败: {e}")
            QMessageBox.critical(self, "同步失败", f"平台同步失败：{str(e)}")
    
    def refresh_status(self):
        """刷新平台状态"""
        try:
            platforms = ['taobao', 'xiaohongshu', 'douyin', '1688']
            self.status_table.setRowCount(len(platforms))
            
            for i, platform in enumerate(platforms):
                self.status_table.setItem(i, 0, QTableWidgetItem(platform.upper()))
                
                # 获取平台状态
                config = self.platform_configs.get(platform, {})
                enabled = config.get('enabled', False)
                status = "✅ 已启用" if enabled else "⚪ 未启用"
                
                self.status_table.setItem(i, 1, QTableWidgetItem(status))
                self.status_table.setItem(i, 2, QTableWidgetItem("未同步"))
                
                # 操作按钮
                test_button = QPushButton("测试")
                test_button.clicked.connect(lambda checked, p=platform: self.test_platform(p))
                self.status_table.setCellWidget(i, 3, test_button)
            
        except Exception as e:
            self.logger.error(f"刷新状态失败: {e}")
    
    def test_platform(self, platform_type: str):
        """测试指定平台"""
        widget = self.platform_widgets.get(platform_type)
        if widget:
            widget.test_connection()
