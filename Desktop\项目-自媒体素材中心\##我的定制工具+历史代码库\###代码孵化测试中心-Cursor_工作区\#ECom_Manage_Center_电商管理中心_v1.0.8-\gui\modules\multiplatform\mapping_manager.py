#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品映射管理界面

提供商品与平台ID映射管理的用户界面。
"""

import uuid
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QLineEdit, QComboBox, QTextEdit, QGroupBox,
    QFormLayout, QMessageBox, QHeaderView, QSplitter, QTabWidget,
    QCheckBox, QProgressBar, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QColor

from core.database import DatabaseManager
from core.managers.mapping_manager import MappingManager
from core.managers.product_manager import ProductManager
from utils.logger import LoggerMixin


class MappingDialog(QDialog):
    """映射创建/编辑对话框"""
    
    def __init__(self, mapping_manager: MappingManager, product_manager: ProductManager, 
                 mapping_data: Optional[Dict[str, Any]] = None, parent=None):
        super().__init__(parent)
        self.mapping_manager = mapping_manager
        self.product_manager = product_manager
        self.mapping_data = mapping_data
        self.is_edit_mode = mapping_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_mapping_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("编辑映射" if self.is_edit_mode else "创建映射")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 表单
        form_group = QGroupBox("映射信息")
        form_layout = QFormLayout(form_group)
        
        # 本地商品选择
        self.product_combo = QComboBox()
        self.load_products()
        form_layout.addRow("本地商品:", self.product_combo)
        
        # 平台类型
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(['taobao', 'xiaohongshu', 'douyin', '1688'])
        form_layout.addRow("平台类型:", self.platform_combo)
        
        # 平台商品ID
        self.platform_product_id_edit = QLineEdit()
        form_layout.addRow("平台商品ID:", self.platform_product_id_edit)
        
        # 平台SKU编码
        self.platform_sku_edit = QLineEdit()
        form_layout.addRow("平台SKU编码:", self.platform_sku_edit)
        
        # 自动同步
        self.auto_sync_checkbox = QCheckBox("启用自动同步")
        self.auto_sync_checkbox.setChecked(True)
        form_layout.addRow("同步设置:", self.auto_sync_checkbox)
        
        # 备注
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        form_layout.addRow("备注:", self.notes_edit)
        
        layout.addWidget(form_group)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
    
    def load_products(self):
        """加载商品列表"""
        try:
            products = self.product_manager.get_all_products()
            self.product_combo.clear()
            
            for product in products:
                self.product_combo.addItem(
                    f"{product.name} ({product.product_id})",
                    product.product_id
                )
                
        except Exception as e:
            QMessageBox.warning(self, "加载失败", f"加载商品列表失败：{str(e)}")
    
    def load_mapping_data(self):
        """加载映射数据"""
        if not self.mapping_data:
            return
        
        # 设置商品
        product_id = self.mapping_data.get('sku_id')
        for i in range(self.product_combo.count()):
            if self.product_combo.itemData(i) == product_id:
                self.product_combo.setCurrentIndex(i)
                break
        
        # 设置其他字段
        self.platform_combo.setCurrentText(self.mapping_data.get('platform_type', ''))
        self.platform_product_id_edit.setText(self.mapping_data.get('platform_product_id', ''))
        self.platform_sku_edit.setText(self.mapping_data.get('platform_sku_code', ''))
        self.auto_sync_checkbox.setChecked(self.mapping_data.get('auto_sync_enabled', True))
        self.notes_edit.setPlainText(self.mapping_data.get('notes', ''))
    
    def get_mapping_data(self) -> Dict[str, Any]:
        """获取映射数据"""
        return {
            'sku_id': self.product_combo.currentData(),
            'platform_type': self.platform_combo.currentText(),
            'platform_product_id': self.platform_product_id_edit.text(),
            'platform_sku_code': self.platform_sku_edit.text(),
            'auto_sync_enabled': self.auto_sync_checkbox.isChecked(),
            'notes': self.notes_edit.toPlainText()
        }
    
    def accept(self):
        """确认操作"""
        data = self.get_mapping_data()
        
        # 验证必填字段
        if not data['sku_id']:
            QMessageBox.warning(self, "验证失败", "请选择本地商品！")
            return
        
        if not data['platform_product_id']:
            QMessageBox.warning(self, "验证失败", "请输入平台商品ID！")
            return
        
        try:
            if self.is_edit_mode:
                # 更新映射
                mapping_id = self.mapping_data['mapping_id']
                # 这里需要实现更新映射的方法
                QMessageBox.information(self, "成功", "映射更新成功！")
            else:
                # 创建映射
                success = self.mapping_manager.create_mapping(
                    sku_id=data['sku_id'],
                    platform_type=data['platform_type'],
                    platform_product_id=data['platform_product_id'],
                    platform_sku_code=data['platform_sku_code'],
                    auto_sync_enabled=data['auto_sync_enabled'],
                    notes=data['notes']
                )
                
                if success:
                    QMessageBox.information(self, "成功", "映射创建成功！")
                else:
                    QMessageBox.warning(self, "失败", "映射创建失败！")
                    return
            
            super().accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败：{str(e)}")


class SmartMatchDialog(QDialog):
    """智能匹配对话框"""
    
    def __init__(self, mapping_manager: MappingManager, parent=None):
        super().__init__(parent)
        self.mapping_manager = mapping_manager
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("智能商品匹配")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 输入区域
        input_group = QGroupBox("商品信息")
        input_layout = QFormLayout(input_group)
        
        self.product_name_edit = QLineEdit()
        self.product_price_edit = QLineEdit()
        self.confidence_edit = QLineEdit("0.8")
        
        input_layout.addRow("商品名称:", self.product_name_edit)
        input_layout.addRow("商品价格:", self.product_price_edit)
        input_layout.addRow("置信度阈值:", self.confidence_edit)
        
        layout.addWidget(input_group)
        
        # 搜索按钮
        search_button = QPushButton("开始匹配")
        search_button.clicked.connect(self.start_matching)
        layout.addWidget(search_button)
        
        # 结果表格
        self.result_table = QTableWidget(0, 5)
        self.result_table.setHorizontalHeaderLabels([
            '商品名称', '商品ID', '置信度', '名称相似度', '价格相似度'
        ])
        self.result_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.result_table)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.select_button = QPushButton("选择匹配")
        self.select_button.clicked.connect(self.select_match)
        self.select_button.setEnabled(False)
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.select_button)
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 选中行变化
        self.result_table.itemSelectionChanged.connect(self.on_selection_changed)
    
    def start_matching(self):
        """开始智能匹配"""
        product_name = self.product_name_edit.text().strip()
        if not product_name:
            QMessageBox.warning(self, "输入错误", "请输入商品名称！")
            return
        
        try:
            price = float(self.product_price_edit.text() or 0)
            confidence = float(self.confidence_edit.text() or 0.8)
            
            # 执行智能匹配
            order_data = {
                'product_name': product_name,
                'price': price
            }
            
            matches = self.mapping_manager.smart_match_products(order_data, confidence)
            
            # 显示结果
            self.result_table.setRowCount(len(matches))
            
            for i, match in enumerate(matches):
                self.result_table.setItem(i, 0, QTableWidgetItem(match['product_name']))
                self.result_table.setItem(i, 1, QTableWidgetItem(match['product_id']))
                self.result_table.setItem(i, 2, QTableWidgetItem(f"{match['confidence']:.3f}"))
                self.result_table.setItem(i, 3, QTableWidgetItem(f"{match['name_similarity']:.3f}"))
                self.result_table.setItem(i, 4, QTableWidgetItem(f"{match['price_similarity']:.3f}"))
                
                # 根据置信度设置行颜色
                if match['confidence'] >= 0.9:
                    color = QColor(200, 255, 200)  # 绿色
                elif match['confidence'] >= 0.7:
                    color = QColor(255, 255, 200)  # 黄色
                else:
                    color = QColor(255, 200, 200)  # 红色
                
                for j in range(5):
                    item = self.result_table.item(i, j)
                    if item:
                        item.setBackground(color)
            
            if not matches:
                QMessageBox.information(self, "匹配结果", "未找到匹配的商品。")
            
        except ValueError:
            QMessageBox.warning(self, "输入错误", "价格和置信度必须是数字！")
        except Exception as e:
            QMessageBox.critical(self, "匹配失败", f"智能匹配失败：{str(e)}")
    
    def on_selection_changed(self):
        """选中行变化"""
        self.select_button.setEnabled(len(self.result_table.selectedItems()) > 0)
    
    def select_match(self):
        """选择匹配项"""
        current_row = self.result_table.currentRow()
        if current_row >= 0:
            product_id = self.result_table.item(current_row, 1).text()
            product_name = self.result_table.item(current_row, 0).text()
            confidence = self.result_table.item(current_row, 2).text()
            
            QMessageBox.information(
                self, "选择成功", 
                f"已选择商品：{product_name}\n商品ID：{product_id}\n置信度：{confidence}"
            )
            
            self.selected_product_id = product_id
            self.accept()


class MappingManagerWidget(QWidget, LoggerMixin):
    """商品映射管理主界面"""
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.mapping_manager = MappingManager(db_manager)
        self.product_manager = ProductManager(db_manager)
        self.init_ui()
        self.load_mappings()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("商品映射管理")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("新增映射")
        self.add_button.clicked.connect(self.add_mapping)
        
        self.edit_button = QPushButton("编辑映射")
        self.edit_button.clicked.connect(self.edit_mapping)
        self.edit_button.setEnabled(False)
        
        self.delete_button = QPushButton("删除映射")
        self.delete_button.clicked.connect(self.delete_mapping)
        self.delete_button.setEnabled(False)
        
        self.smart_match_button = QPushButton("智能匹配")
        self.smart_match_button.clicked.connect(self.smart_match)
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.load_mappings)
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.smart_match_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 过滤器
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("平台:"))
        self.platform_filter = QComboBox()
        self.platform_filter.addItems(['全部', 'taobao', 'xiaohongshu', 'douyin', '1688'])
        self.platform_filter.currentTextChanged.connect(self.apply_filter)
        
        filter_layout.addWidget(self.platform_filter)
        
        filter_layout.addWidget(QLabel("状态:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(['全部', 'active', 'inactive', 'pending'])
        self.status_filter.currentTextChanged.connect(self.apply_filter)
        
        filter_layout.addWidget(self.status_filter)
        
        filter_layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索商品名称或ID...")
        self.search_edit.textChanged.connect(self.apply_filter)
        
        filter_layout.addWidget(self.search_edit)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # 映射表格
        self.mapping_table = QTableWidget(0, 8)
        self.mapping_table.setHorizontalHeaderLabels([
            '本地商品', 'SKU ID', '平台', '平台商品ID', '状态', '同步时间', '错误次数', '备注'
        ])
        
        # 设置列宽
        header = self.mapping_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        
        self.mapping_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.mapping_table.itemDoubleClicked.connect(self.edit_mapping)
        
        layout.addWidget(self.mapping_table)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        self.count_label = QLabel("总计: 0")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.count_label)
        
        layout.addLayout(status_layout)
    
    def load_mappings(self):
        """加载映射数据"""
        try:
            self.status_label.setText("加载中...")
            
            # 获取所有映射
            sql = """
            SELECT m.*, p.name as product_name 
            FROM platform_product_mapping m
            LEFT JOIN products p ON m.sku_id = p.product_id
            ORDER BY m.created_at DESC
            """
            
            rows = self.db_manager.fetch_all(sql)
            
            self.mapping_table.setRowCount(len(rows))
            
            for i, row in enumerate(rows):
                columns = [description[0] for description in self.db_manager.cursor.description]
                mapping = dict(zip(columns, row))
                
                self.mapping_table.setItem(i, 0, QTableWidgetItem(mapping.get('product_name', '')))
                self.mapping_table.setItem(i, 1, QTableWidgetItem(mapping.get('sku_id', '')))
                self.mapping_table.setItem(i, 2, QTableWidgetItem(mapping.get('platform_type', '')))
                self.mapping_table.setItem(i, 3, QTableWidgetItem(mapping.get('platform_product_id', '')))
                self.mapping_table.setItem(i, 4, QTableWidgetItem(mapping.get('mapping_status', '')))
                self.mapping_table.setItem(i, 5, QTableWidgetItem(mapping.get('last_sync_time', '')))
                self.mapping_table.setItem(i, 6, QTableWidgetItem(str(mapping.get('sync_error_count', 0))))
                self.mapping_table.setItem(i, 7, QTableWidgetItem(mapping.get('notes', '')))
                
                # 存储完整数据
                self.mapping_table.item(i, 0).setData(Qt.ItemDataRole.UserRole, mapping)
            
            self.count_label.setText(f"总计: {len(rows)}")
            self.status_label.setText("就绪")
            
        except Exception as e:
            self.logger.error(f"加载映射数据失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载映射数据失败：{str(e)}")
            self.status_label.setText("加载失败")
    
    def apply_filter(self):
        """应用过滤器"""
        platform_filter = self.platform_filter.currentText()
        status_filter = self.status_filter.currentText()
        search_text = self.search_edit.text().lower()
        
        for i in range(self.mapping_table.rowCount()):
            show_row = True
            
            # 平台过滤
            if platform_filter != '全部':
                platform = self.mapping_table.item(i, 2).text()
                if platform != platform_filter:
                    show_row = False
            
            # 状态过滤
            if status_filter != '全部':
                status = self.mapping_table.item(i, 4).text()
                if status != status_filter:
                    show_row = False
            
            # 搜索过滤
            if search_text:
                product_name = self.mapping_table.item(i, 0).text().lower()
                sku_id = self.mapping_table.item(i, 1).text().lower()
                if search_text not in product_name and search_text not in sku_id:
                    show_row = False
            
            self.mapping_table.setRowHidden(i, not show_row)
    
    def on_selection_changed(self):
        """选择变化处理"""
        has_selection = len(self.mapping_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def add_mapping(self):
        """新增映射"""
        dialog = MappingDialog(self.mapping_manager, self.product_manager, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_mappings()
    
    def edit_mapping(self):
        """编辑映射"""
        current_row = self.mapping_table.currentRow()
        if current_row >= 0:
            mapping_data = self.mapping_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            dialog = MappingDialog(
                self.mapping_manager, self.product_manager, 
                mapping_data, parent=self
            )
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_mappings()
    
    def delete_mapping(self):
        """删除映射"""
        current_row = self.mapping_table.currentRow()
        if current_row >= 0:
            mapping_data = self.mapping_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            mapping_id = mapping_data.get('mapping_id')
            
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除映射 {mapping_data.get('platform_product_id')} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    if self.mapping_manager.delete_mapping(mapping_id):
                        QMessageBox.information(self, "成功", "映射删除成功！")
                        self.load_mappings()
                    else:
                        QMessageBox.warning(self, "失败", "映射删除失败！")
                        
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除失败：{str(e)}")
    
    def smart_match(self):
        """智能匹配"""
        dialog = SmartMatchDialog(self.mapping_manager, parent=self)
        dialog.exec()
