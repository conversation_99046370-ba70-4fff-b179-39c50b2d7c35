#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集成管理器

负责管理各模块间的数据关联和一致性，确保数据的完整性和同步性。
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from .product_manager import ProductManager
from .supplier_manager import SupplierManager
from .batch_manager import BatchManager
from .order_manager import OrderManager
from .comparison_manager import ComparisonManager
from utils.logger import LoggerMixin


@dataclass
class DataSyncEvent:
    """数据同步事件"""
    event_type: str  # 'create', 'update', 'delete'
    module: str      # 'product', 'supplier', 'batch', 'order', 'comparison'
    entity_id: str   # 实体ID
    data: Dict[str, Any]  # 相关数据
    timestamp: datetime
    processed: bool = False


class DataIntegrationManager(LoggerMixin):
    """数据集成管理器"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
        # 初始化各个管理器
        self.product_manager = ProductManager(db_manager)
        self.supplier_manager = SupplierManager(db_manager)
        self.batch_manager = BatchManager(db_manager)
        self.order_manager = OrderManager(db_manager)
        self.comparison_manager = ComparisonManager(db_manager)
        
        # 数据同步事件队列
        self.sync_events: List[DataSyncEvent] = []
        
        # 数据关联映射
        self.data_relations = {
            'product_supplier': {},  # 商品-供应商关联
            'product_batch': {},     # 商品-批次关联
            'supplier_order': {},    # 供应商-订单关联
            'product_comparison': {} # 商品-对比关联
        }
        
        self.logger.info("数据集成管理器初始化完成")
    
    def sync_product_data(self, product_id: str, action: str = 'update'):
        """同步商品数据到相关模块"""
        try:
            product = self.product_manager.get_product(product_id)
            if not product:
                self.logger.warning(f"商品 {product_id} 不存在")
                return
            
            # 同步到供应商模块
            if hasattr(product, 'supplier_id') and product.supplier_id:
                self._sync_product_to_supplier(product, action)
            
            # 同步到批次模块
            self._sync_product_to_batch(product, action)
            
            # 同步到对比模块
            self._sync_product_to_comparison(product, action)
            
            # 记录同步事件
            self._add_sync_event('product', product_id, action, product.__dict__)
            
            self.logger.info(f"商品 {product_id} 数据同步完成")
            
        except Exception as e:
            self.logger.error(f"同步商品数据失败: {e}")
    
    def sync_supplier_data(self, supplier_id: str, action: str = 'update'):
        """同步供应商数据到相关模块"""
        try:
            supplier = self.supplier_manager.get_supplier(supplier_id)
            if not supplier:
                self.logger.warning(f"供应商 {supplier_id} 不存在")
                return
            
            # 同步到商品模块
            self._sync_supplier_to_products(supplier, action)
            
            # 同步到订单模块
            self._sync_supplier_to_orders(supplier, action)
            
            # 同步到对比模块
            self._sync_supplier_to_comparison(supplier, action)
            
            # 记录同步事件
            self._add_sync_event('supplier', supplier_id, action, supplier.__dict__)
            
            self.logger.info(f"供应商 {supplier_id} 数据同步完成")
            
        except Exception as e:
            self.logger.error(f"同步供应商数据失败: {e}")
    
    def sync_batch_data(self, batch_id: str, action: str = 'update'):
        """同步批次数据到相关模块"""
        try:
            batch = self.batch_manager.get_batch(batch_id)
            if not batch:
                self.logger.warning(f"批次 {batch_id} 不存在")
                return
            
            # 同步到商品模块（更新库存）
            self._sync_batch_to_products(batch, action)
            
            # 记录同步事件
            self._add_sync_event('batch', batch_id, action, batch.__dict__)
            
            self.logger.info(f"批次 {batch_id} 数据同步完成")
            
        except Exception as e:
            self.logger.error(f"同步批次数据失败: {e}")
    
    def sync_order_data(self, order_id: str, action: str = 'update'):
        """同步订单数据到相关模块"""
        try:
            order = self.order_manager.get_order(order_id)
            if not order:
                self.logger.warning(f"订单 {order_id} 不存在")
                return
            
            # 同步到商品模块（更新库存）
            self._sync_order_to_products(order, action)
            
            # 同步到供应商模块
            if hasattr(order, 'supplier_id') and order.supplier_id:
                self._sync_order_to_supplier(order, action)
            
            # 记录同步事件
            self._add_sync_event('order', order_id, action, order.__dict__)
            
            self.logger.info(f"订单 {order_id} 数据同步完成")
            
        except Exception as e:
            self.logger.error(f"同步订单数据失败: {e}")
    
    def validate_data_consistency(self) -> Dict[str, List[str]]:
        """验证数据一致性"""
        inconsistencies = {
            'products': [],
            'suppliers': [],
            'batches': [],
            'orders': []
        }
        
        try:
            # 验证商品数据一致性
            products = self.product_manager.get_all_products()
            for product in products:
                if hasattr(product, 'supplier_id') and product.supplier_id:
                    supplier = self.supplier_manager.get_supplier(product.supplier_id)
                    if not supplier:
                        inconsistencies['products'].append(
                            f"商品 {product.id} 关联的供应商 {product.supplier_id} 不存在"
                        )
            
            # 验证供应商数据一致性
            suppliers = self.supplier_manager.get_all_suppliers()
            for supplier in suppliers:
                # 检查供应商的商品是否存在
                supplier_products = self.product_manager.get_products_by_supplier(supplier.id)
                if not supplier_products:
                    inconsistencies['suppliers'].append(
                        f"供应商 {supplier.id} 没有关联的商品"
                    )
            
            # 验证批次数据一致性
            batches = self.batch_manager.get_all_batches()
            for batch in batches:
                if hasattr(batch, 'products'):
                    for product_id in batch.products:
                        product = self.product_manager.get_product(product_id)
                        if not product:
                            inconsistencies['batches'].append(
                                f"批次 {batch.id} 关联的商品 {product_id} 不存在"
                            )
            
            # 验证订单数据一致性
            orders = self.order_manager.get_all_orders()
            for order in orders:
                if hasattr(order, 'supplier_id') and order.supplier_id:
                    supplier = self.supplier_manager.get_supplier(order.supplier_id)
                    if not supplier:
                        inconsistencies['orders'].append(
                            f"订单 {order.id} 关联的供应商 {order.supplier_id} 不存在"
                        )
            
            self.logger.info("数据一致性验证完成")
            
        except Exception as e:
            self.logger.error(f"数据一致性验证失败: {e}")
        
        return inconsistencies
    
    def get_integrated_dashboard_data(self) -> Dict[str, Any]:
        """获取集成仪表板数据"""
        try:
            dashboard_data = {
                'overview': {
                    'total_products': 0,
                    'total_suppliers': 0,
                    'active_batches': 0,
                    'pending_orders': 0,
                    'total_inventory_value': 0.0,
                    'low_stock_products': 0
                },
                'recent_activities': [],
                'alerts': [],
                'performance_metrics': {
                    'sales_trend': 'up',
                    'inventory_turnover': 4.2,
                    'supplier_performance': 94.5,
                    'profit_margin': 27.1
                }
            }
            
            # 获取基础统计数据
            products = self.product_manager.get_all_products()
            suppliers = self.supplier_manager.get_all_suppliers()
            batches = self.batch_manager.get_all_batches()
            orders = self.order_manager.get_all_orders()
            
            dashboard_data['overview']['total_products'] = len(products)
            dashboard_data['overview']['total_suppliers'] = len(suppliers)
            dashboard_data['overview']['active_batches'] = len([b for b in batches if getattr(b, 'status', '') == 'active'])
            dashboard_data['overview']['pending_orders'] = len([o for o in orders if getattr(o, 'status', '') == 'pending'])
            
            # 计算库存总价值
            total_value = 0.0
            low_stock_count = 0
            for product in products:
                if hasattr(product, 'current_stock') and hasattr(product, 'purchase_price'):
                    total_value += product.current_stock * product.purchase_price
                
                if hasattr(product, 'current_stock') and hasattr(product, 'safety_stock'):
                    if product.current_stock <= product.safety_stock:
                        low_stock_count += 1
            
            dashboard_data['overview']['total_inventory_value'] = total_value
            dashboard_data['overview']['low_stock_products'] = low_stock_count
            
            # 获取最近活动
            dashboard_data['recent_activities'] = self._get_recent_activities()
            
            # 获取警报信息
            dashboard_data['alerts'] = self._get_system_alerts()
            
            return dashboard_data
            
        except Exception as e:
            self.logger.error(f"获取集成仪表板数据失败: {e}")
            return {}
    
    def _sync_product_to_supplier(self, product, action: str):
        """同步商品数据到供应商模块"""
        # TODO: 实现商品到供应商的数据同步逻辑
        pass
    
    def _sync_product_to_batch(self, product, action: str):
        """同步商品数据到批次模块"""
        # TODO: 实现商品到批次的数据同步逻辑
        pass
    
    def _sync_product_to_comparison(self, product, action: str):
        """同步商品数据到对比模块"""
        # TODO: 实现商品到对比的数据同步逻辑
        pass
    
    def _sync_supplier_to_products(self, supplier, action: str):
        """同步供应商数据到商品模块"""
        # TODO: 实现供应商到商品的数据同步逻辑
        pass
    
    def _sync_supplier_to_orders(self, supplier, action: str):
        """同步供应商数据到订单模块"""
        # TODO: 实现供应商到订单的数据同步逻辑
        pass
    
    def _sync_supplier_to_comparison(self, supplier, action: str):
        """同步供应商数据到对比模块"""
        # TODO: 实现供应商到对比的数据同步逻辑
        pass
    
    def _sync_batch_to_products(self, batch, action: str):
        """同步批次数据到商品模块"""
        # TODO: 实现批次到商品的数据同步逻辑
        pass
    
    def _sync_order_to_products(self, order, action: str):
        """同步订单数据到商品模块"""
        # TODO: 实现订单到商品的数据同步逻辑
        pass
    
    def _sync_order_to_supplier(self, order, action: str):
        """同步订单数据到供应商模块"""
        # TODO: 实现订单到供应商的数据同步逻辑
        pass
    
    def _add_sync_event(self, module: str, entity_id: str, action: str, data: Dict[str, Any]):
        """添加同步事件"""
        event = DataSyncEvent(
            event_type=action,
            module=module,
            entity_id=entity_id,
            data=data,
            timestamp=datetime.now()
        )
        self.sync_events.append(event)
    
    def _get_recent_activities(self) -> List[Dict[str, Any]]:
        """获取最近活动"""
        activities = []
        
        # 从同步事件中获取最近的活动
        recent_events = sorted(self.sync_events, key=lambda x: x.timestamp, reverse=True)[:10]
        
        for event in recent_events:
            activities.append({
                'type': event.event_type,
                'module': event.module,
                'entity_id': event.entity_id,
                'timestamp': event.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'description': f"{event.module} {event.entity_id} {event.event_type}"
            })
        
        return activities
    
    def _get_system_alerts(self) -> List[Dict[str, Any]]:
        """获取系统警报"""
        alerts = []
        
        try:
            # 检查低库存警报
            products = self.product_manager.get_all_products()
            low_stock_products = []
            for product in products:
                if hasattr(product, 'current_stock') and hasattr(product, 'safety_stock'):
                    if product.current_stock <= product.safety_stock:
                        low_stock_products.append(product.name)
            
            if low_stock_products:
                alerts.append({
                    'type': 'warning',
                    'title': '库存预警',
                    'message': f"{len(low_stock_products)} 个商品库存不足",
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 检查数据一致性警报
            inconsistencies = self.validate_data_consistency()
            total_issues = sum(len(issues) for issues in inconsistencies.values())
            if total_issues > 0:
                alerts.append({
                    'type': 'error',
                    'title': '数据一致性问题',
                    'message': f"发现 {total_issues} 个数据一致性问题",
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
        except Exception as e:
            self.logger.error(f"获取系统警报失败: {e}")
        
        return alerts
