#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试商品模型问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def debug_product_creation():
    """调试商品创建问题"""
    print("🔍 调试商品创建...")

    try:
        from core.models.product import Product
        from decimal import Decimal

        # 创建简单商品
        product = Product(
            name="测试商品",
            category="测试分类",
            quantity=100,
            purchase_price=Decimal("50.00"),
            selling_price=Decimal("80.00"),
        )

        print(f"✅ 商品创建成功: {product.name}")
        print(f"   ID: {product.product_id}")
        print(f"   分类: {product.category}")
        print(f"   数量: {product.quantity}")

        # 测试to_dict
        data = product.to_dict()
        print(f"✅ to_dict成功，字段数: {len(data)}")

        # 测试from_dict
        new_product = Product.from_dict(data)
        print(f"✅ from_dict成功: {new_product.name}")

        return True

    except Exception as e:
        print(f"❌ 商品创建失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def debug_database_interaction():
    """调试数据库交互"""
    print("\n🗄️ 调试数据库交互...")

    try:
        from core.database import DatabaseManager
        from core.models.product import Product
        from decimal import Decimal

        # 连接数据库
        db_manager = DatabaseManager("data/test_db.db")
        if not db_manager.initialize():
            print("❌ 数据库初始化失败")
            return False

        print("✅ 数据库初始化成功")

        # 创建商品
        product = Product(
            name="调试商品",
            category="调试分类",
            quantity=50,
            purchase_price=Decimal("30.00"),
            selling_price=Decimal("50.00"),
        )

        print(f"✅ 商品对象创建: {product.name}")

        # 手动插入数据库
        data = product.to_dict()
        print(f"✅ 商品数据字典: {len(data)} 个字段")

        # 检查数据库表结构
        sql = "PRAGMA table_info(products)"
        columns_info = db_manager.fetch_all(sql)
        print(f"✅ 数据库表有 {len(columns_info)} 个字段")

        db_columns = [col[1] for col in columns_info]
        print(f"   数据库字段: {db_columns[:10]}...")  # 显示前10个字段

        # 检查字段匹配
        missing_fields = []
        for field in data.keys():
            if field not in db_columns:
                missing_fields.append(field)

        if missing_fields:
            print(f"❌ 缺失字段: {missing_fields}")
        else:
            print("✅ 所有字段匹配")

        # 尝试简单插入
        simple_sql = """
        INSERT INTO products (product_id, name, category, quantity, purchase_price, selling_price)
        VALUES (?, ?, ?, ?, ?, ?)
        """

        params = (
            product.product_id,
            product.name,
            product.category,
            product.quantity,
            float(product.purchase_price),
            float(product.selling_price),
        )

        cursor = db_manager.execute(simple_sql, params)
        if cursor:
            print("✅ 简单插入成功")

            # 尝试查询
            query_sql = "SELECT * FROM products WHERE product_id = ?"
            row = db_manager.fetch_one(query_sql, (product.product_id,))

            if row:
                print(f"✅ 查询成功，获得 {len(row)} 个字段")

                # 获取列名
                columns = [
                    description[0] for description in db_manager.cursor.description
                ]
                print(f"   列名: {columns[:10]}...")  # 显示前10个列名

                # 尝试创建Product对象
                try:
                    data_dict = dict(zip(columns, row))
                    retrieved_product = Product.from_dict(data_dict)
                    print(f"✅ 从数据库创建商品成功: {retrieved_product.name}")
                    return True
                except Exception as e:
                    print(f"❌ 从数据库创建商品失败: {e}")
                    print(
                        f"   数据字典: {dict(zip(columns[:5], row[:5]))}"
                    )  # 显示前5个字段
                    import traceback

                    traceback.print_exc()
                    return False
            else:
                print("❌ 查询失败")
                return False
        else:
            print("❌ 简单插入失败")
            return False

    except Exception as e:
        print(f"❌ 数据库交互失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🐛 商品模型调试")
    print("=" * 30)

    # 创建数据目录
    Path("data").mkdir(exist_ok=True)

    # 运行调试
    tests = [
        ("商品创建", debug_product_creation),
        ("数据库交互", debug_database_interaction),
    ]

    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 调试...")
        try:
            if test_func():
                print(f"✅ {test_name} 调试成功")
            else:
                print(f"❌ {test_name} 调试失败")
        except Exception as e:
            print(f"❌ {test_name} 调试异常: {e}")


if __name__ == "__main__":
    main()

    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
