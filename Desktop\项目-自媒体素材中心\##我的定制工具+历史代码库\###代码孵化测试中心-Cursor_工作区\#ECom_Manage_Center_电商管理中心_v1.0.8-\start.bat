@echo off
chcp 65001 >nul
title E-Commerce Management System v2.0.0

echo ====================================================
echo E-Commerce Management System v2.0.0
echo ====================================================
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo Python environment OK
echo.

echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo WARNING: Some dependencies may not be installed
    echo Trying to install PyQt6 separately...
    pip install PyQt6
)

echo.
echo Starting application...
python main.py

if errorlevel 1 (
    echo.
    echo Program exited with error code: %errorlevel%
    pause
)

echo.
echo Program finished
pause
