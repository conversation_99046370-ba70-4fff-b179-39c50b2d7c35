#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批次数据模型

基于Inventory_Management_v1.7.3的Batch类，扩展支持更多功能。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import json


@dataclass
class BatchProduct:
    """批次商品关联"""
    
    product_id: str
    quantity: int
    unit_price: float = 0.0
    total_value: float = 0.0
    added_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.added_at is None:
            self.added_at = datetime.now()
        
        self.total_value = self.quantity * self.unit_price
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'product_id': self.product_id,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_value': self.total_value,
            'added_at': self.added_at.isoformat() if self.added_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchProduct':
        if isinstance(data.get('added_at'), str):
            data['added_at'] = datetime.fromisoformat(data['added_at'])
        return cls(**data)


@dataclass
class Batch:
    """
    批次数据模型
    
    管理商品批次信息，支持批次创建、商品关联、状态跟踪等功能
    """
    
    # 基础信息
    batch_id: Optional[str] = None
    code: str = ""  # 批次编码
    name: str = ""  # 批次名称
    description: str = ""
    status: str = "活跃"  # 活跃、已完成、已取消
    
    # 商品关联
    products: List[BatchProduct] = field(default_factory=list)
    
    # 统计信息
    total_products: int = 0
    total_quantity: int = 0
    total_value: float = 0.0
    
    # 备注和元数据
    remarks: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.batch_id is None:
            self.batch_id = self.generate_batch_id()
        
        if self.created_at is None:
            self.created_at = datetime.now()
        
        self.updated_at = datetime.now()
        
        # 更新统计信息
        self.update_statistics()
    
    def generate_batch_id(self) -> str:
        """生成批次ID"""
        return f"B{uuid.uuid4().hex[:8].upper()}"
    
    def add_product(self, product_id: str, quantity: int, unit_price: float = 0.0) -> bool:
        """添加商品到批次"""
        # 检查商品是否已存在
        for batch_product in self.products:
            if batch_product.product_id == product_id:
                # 更新数量
                batch_product.quantity += quantity
                batch_product.total_value = batch_product.quantity * batch_product.unit_price
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        
        # 添加新商品
        batch_product = BatchProduct(
            product_id=product_id,
            quantity=quantity,
            unit_price=unit_price
        )
        self.products.append(batch_product)
        self.update_statistics()
        self.updated_at = datetime.now()
        return True
    
    def remove_product(self, product_id: str) -> bool:
        """从批次中移除商品"""
        for i, batch_product in enumerate(self.products):
            if batch_product.product_id == product_id:
                del self.products[i]
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        return False
    
    def update_product_quantity(self, product_id: str, new_quantity: int) -> bool:
        """更新商品数量"""
        for batch_product in self.products:
            if batch_product.product_id == product_id:
                batch_product.quantity = new_quantity
                batch_product.total_value = batch_product.quantity * batch_product.unit_price
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        return False
    
    def update_product_price(self, product_id: str, new_price: float) -> bool:
        """更新商品单价"""
        for batch_product in self.products:
            if batch_product.product_id == product_id:
                batch_product.unit_price = new_price
                batch_product.total_value = batch_product.quantity * batch_product.unit_price
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_product(self, product_id: str) -> Optional[BatchProduct]:
        """获取批次中的商品信息"""
        for batch_product in self.products:
            if batch_product.product_id == product_id:
                return batch_product
        return None
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_products = len(self.products)
        self.total_quantity = sum(bp.quantity for bp in self.products)
        self.total_value = sum(bp.total_value for bp in self.products)
    
    def set_status(self, status: str, reason: str = ""):
        """设置批次状态"""
        old_status = self.status
        self.status = status
        self.updated_at = datetime.now()
        
        # 记录状态变化
        if 'status_changes' not in self.metadata:
            self.metadata['status_changes'] = []
        
        self.metadata['status_changes'].append({
            'timestamp': datetime.now().isoformat(),
            'old_status': old_status,
            'new_status': status,
            'reason': reason
        })
    
    def complete_batch(self, reason: str = ""):
        """完成批次"""
        self.set_status("已完成", reason)
    
    def cancel_batch(self, reason: str = ""):
        """取消批次"""
        self.set_status("已取消", reason)
    
    def get_product_list(self) -> List[str]:
        """获取批次中的商品ID列表"""
        return [bp.product_id for bp in self.products]
    
    def is_empty(self) -> bool:
        """检查批次是否为空"""
        return len(self.products) == 0
    
    def is_active(self) -> bool:
        """检查批次是否活跃"""
        return self.status == "活跃"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'batch_id': self.batch_id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'status': self.status,
            'products': json.dumps([bp.to_dict() for bp in self.products]),
            'total_products': self.total_products,
            'total_quantity': self.total_quantity,
            'total_value': self.total_value,
            'remarks': self.remarks,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Batch':
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get('products'), str):
            products_data = json.loads(data['products']) if data['products'] else []
            data['products'] = [BatchProduct.from_dict(pd) for pd in products_data]
        
        if isinstance(data.get('metadata'), str):
            data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        # 处理时间字段
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    @classmethod
    def from_db_row(cls, row, columns: List[str]) -> Optional['Batch']:
        """从数据库行创建对象"""
        if not row:
            return None
        
        data = dict(zip(columns, row))
        return cls.from_dict(data)
    
    def validate(self) -> bool:
        """验证数据有效性"""
        if not self.code:
            raise ValueError("批次编码不能为空")
        
        if not self.name:
            raise ValueError("批次名称不能为空")
        
        if self.status not in ["活跃", "已完成", "已取消"]:
            raise ValueError("批次状态无效")
        
        # 验证商品数据
        for batch_product in self.products:
            if not batch_product.product_id:
                raise ValueError("批次中存在无效的商品ID")
            
            if batch_product.quantity < 0:
                raise ValueError("批次中商品数量不能为负数")
            
            if batch_product.unit_price < 0:
                raise ValueError("批次中商品单价不能为负数")
        
        return True
    
    def __str__(self) -> str:
        return f"{self.batch_id} - {self.name} ({self.status})"
    
    def __repr__(self) -> str:
        return f"Batch(batch_id='{self.batch_id}', name='{self.name}', status='{self.status}')"
