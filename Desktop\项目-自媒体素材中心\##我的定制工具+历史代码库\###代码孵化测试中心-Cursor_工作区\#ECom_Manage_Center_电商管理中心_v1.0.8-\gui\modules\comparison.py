#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应链对比模块界面

供应链对比分析功能的主界面。
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from .comparison.comparison_widget import ComparisonWidget as ComparisonMainWidget
from .development_widget import DevelopmentWidget


class ComparisonWidget(QWidget):
    """供应链对比界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #2b2b2b;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #555;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background-color: #404040;
            }
        """
        )

        # 对比管理标签页 - 完整功能
        comparison_widget = ComparisonMainWidget(self.db_manager, self)
        tab_widget.addTab(comparison_widget, "📊 对比管理")

        # 价格分析标签页 - 开发中
        analysis_info = {
            "name": "价格分析功能",
            "icon": "💰",
            "description": "Price Analysis & Trends",
            "progress": 30,
            "eta": "1周",
            "features": [
                "📈 价格趋势分析",
                "   • 历史价格走势",
                "   • 价格波动分析",
                "   • 季节性价格模式",
                "   • 价格预测模型",
                "",
                "💰 成本效益分析",
                "   • 采购成本对比",
                "   • 运费成本计算",
                "   • 总成本分析",
                "   • ROI计算",
                "",
                "🎯 最优选择推荐",
                "   • 综合评分算法",
                "   • 供应商推荐",
                "   • 采购建议",
                "   • 风险评估",
            ],
            "phases": [
                ("第一阶段", "基础价格分析", "🔄 进行中", "#FF9800"),
                ("第二阶段", "趋势预测模型", "⏳ 计划中", "#666"),
                ("第三阶段", "智能推荐算法", "⏳ 计划中", "#666"),
            ],
        }
        analysis_widget = DevelopmentWidget(analysis_info, self)
        tab_widget.addTab(analysis_widget, "💰 价格分析")

        # 数据可视化标签页 - 开发中
        visualization_info = {
            "name": "数据可视化",
            "icon": "📈",
            "description": "Data Visualization & Charts",
            "progress": 10,
            "eta": "2-3周",
            "features": [
                "📊 对比图表",
                "   • 价格对比柱状图",
                "   • 趋势折线图",
                "   • 供应商雷达图",
                "   • 成本饼图",
                "",
                "📈 交互式仪表板",
                "   • 实时数据展示",
                "   • 可配置图表",
                "   • 数据钻取",
                "   • 导出功能",
                "",
                "📋 报表生成",
                "   • 对比分析报告",
                "   • 决策支持报表",
                "   • 定制报表模板",
                "   • 自动报表推送",
            ],
            "phases": [
                ("第一阶段", "基础图表组件", "⏳ 计划中", "#666"),
                ("第二阶段", "交互式仪表板", "⏳ 计划中", "#666"),
                ("第三阶段", "报表生成系统", "⏳ 计划中", "#666"),
            ],
        }
        visualization_widget = DevelopmentWidget(visualization_info, self)
        tab_widget.addTab(visualization_widget, "📈 可视化")

        layout.addWidget(tab_widget)
