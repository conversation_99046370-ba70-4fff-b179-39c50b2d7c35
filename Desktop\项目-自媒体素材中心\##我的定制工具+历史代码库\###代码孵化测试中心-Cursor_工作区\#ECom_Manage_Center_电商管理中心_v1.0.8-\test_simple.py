#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本

只测试最基本的功能，确保系统可以运行。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_database():
    """测试基本数据库功能"""
    print("🗄️ 测试基本数据库功能...")
    
    try:
        from core.database import DatabaseManager
        
        # 删除现有数据库文件
        db_file = Path("data/simple_test.db")
        if db_file.exists():
            db_file.unlink()
            print("✅ 删除现有数据库文件")
        
        # 创建数据库管理器
        db_manager = DatabaseManager(str(db_file))
        
        # 只测试连接和表创建
        if not db_manager.connect():
            print("❌ 数据库连接失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 只创建表，不插入初始数据
        if not db_manager._create_tables_inline():
            print("❌ 表创建失败")
            return False
        
        print("✅ 表创建成功")
        
        # 测试基本查询
        sql = "SELECT name FROM sqlite_master WHERE type='table'"
        tables = db_manager.fetch_all(sql)
        table_names = [table[0] for table in tables]
        print(f"✅ 创建了 {len(table_names)} 个表: {table_names}")
        
        # 关闭数据库
        db_manager.close()
        print("✅ 数据库关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_product():
    """测试基本商品功能"""
    print("\n📦 测试基本商品功能...")
    
    try:
        from core.database import DatabaseManager
        from core.managers.product_manager import ProductManager
        from core.models.product import Product
        from decimal import Decimal
        
        # 连接数据库
        db_manager = DatabaseManager("data/simple_test.db")
        if not db_manager.connect():
            print("❌ 数据库连接失败")
            return False
        
        # 创建商品管理器
        product_manager = ProductManager(db_manager)
        
        # 创建简单商品
        product = Product(
            name="简单测试商品",
            category="测试分类",
            quantity=10,
            purchase_price=Decimal('20.00'),
            selling_price=Decimal('30.00')
        )
        
        print(f"✅ 商品对象创建: {product.name}")
        
        # 尝试保存商品
        if product_manager.create_product(product):
            print("✅ 商品保存成功")
            
            # 尝试获取商品
            retrieved = product_manager.get_product(product.product_id)
            if retrieved:
                print(f"✅ 商品获取成功: {retrieved.name}")
                
                # 测试库存更新
                if product_manager.update_stock(product.product_id, 5, "测试入库"):
                    print("✅ 库存更新成功")
                else:
                    print("❌ 库存更新失败")
                
                # 清理测试数据
                if product_manager.delete_product(product.product_id):
                    print("✅ 测试数据清理成功")
                
                db_manager.close()
                return True
            else:
                print("❌ 商品获取失败")
                return False
        else:
            print("❌ 商品保存失败")
            return False
        
    except Exception as e:
        print(f"❌ 商品测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_import():
    """测试GUI组件导入"""
    print("\n🎨 测试GUI组件导入...")
    
    try:
        # 测试PyQt6
        import PyQt6
        print("✅ PyQt6 导入成功")
        
        # 测试主要GUI组件
        from gui.main_window import MainWindow
        print("✅ 主窗口组件导入成功")
        
        from gui.modules.inventory.inventory_widget import InventoryWidget
        print("✅ 库存管理组件导入成功")
        
        from gui.modules.inventory.product_form import ProductForm
        print("✅ 商品表单组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 简化功能测试")
    print("=" * 40)
    
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)
    
    # 运行测试
    tests = [
        ("基本数据库", test_basic_database),
        ("基本商品", test_basic_product),
        ("GUI组件导入", test_gui_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始 {test_name} 测试...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 40)
    print("测试结果汇总")
    print("=" * 40)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统基本功能正常。")
        print("\n📋 下一步可以:")
        print("1. 运行 python main.py 启动完整应用")
        print("2. 运行 python quick_start.py 快速启动")
        print("3. 开始使用库存管理功能")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass
    
    sys.exit(exit_code)
