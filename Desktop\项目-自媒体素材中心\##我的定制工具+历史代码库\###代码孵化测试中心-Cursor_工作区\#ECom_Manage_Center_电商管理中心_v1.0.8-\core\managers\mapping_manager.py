#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台ID映射管理器

负责管理SKU与平台商品ID的映射关系，支持自动匹配、手动绑定、同步状态管理等功能。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json
import uuid
from difflib import SequenceMatcher

from core.database import DatabaseManager
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class MappingManager(LoggerMixin):
    """平台ID映射管理器"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化映射管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger.info("平台ID映射管理器初始化完成")

    @handle_errors(default_return=False)
    def create_mapping(
        self,
        sku_id: str,
        platform_type: str,
        platform_product_id: str,
        platform_store_id: Optional[str] = None,
        platform_sku_code: Optional[str] = None,
        auto_sync_enabled: bool = True,
        notes: Optional[str] = None,
    ) -> bool:
        """
        创建平台商品ID映射

        Args:
            sku_id: 本地SKU ID
            platform_type: 平台类型 (taobao, xiaohongshu, douyin, 1688)
            platform_product_id: 平台商品ID
            platform_store_id: 店铺ID
            platform_sku_code: 平台SKU编码
            auto_sync_enabled: 是否启用自动同步
            notes: 备注信息

        Returns:
            bool: 创建是否成功
        """
        mapping_id = str(uuid.uuid4())

        sql = """
        INSERT INTO platform_product_mapping 
        (mapping_id, sku_id, platform_type, platform_product_id, platform_store_id, 
         platform_sku_code, auto_sync_enabled, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """

        params = (
            mapping_id,
            sku_id,
            platform_type,
            platform_product_id,
            platform_store_id,
            platform_sku_code,
            auto_sync_enabled,
            notes,
        )

        if self.db_manager.execute(sql, params):
            self.logger.info(
                f"创建平台映射成功: {sku_id} -> {platform_type}:{platform_product_id}"
            )

            # 更新商品的多平台状态
            self._update_product_platform_status(sku_id)
            return True

        return False

    @handle_errors(default_return=None)
    def get_mapping_by_platform_id(
        self,
        platform_type: str,
        platform_product_id: str,
        platform_store_id: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        根据平台商品ID获取映射信息

        Args:
            platform_type: 平台类型
            platform_product_id: 平台商品ID
            platform_store_id: 店铺ID

        Returns:
            Optional[Dict[str, Any]]: 映射信息
        """
        sql = """
        SELECT * FROM platform_product_mapping 
        WHERE platform_type = ? AND platform_product_id = ?
        """
        params = [platform_type, platform_product_id]

        if platform_store_id:
            sql += " AND platform_store_id = ?"
            params.append(platform_store_id)

        sql += " AND mapping_status = 'active' LIMIT 1"

        row = self.db_manager.fetch_one(sql, tuple(params))
        if row:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return dict(zip(columns, row))

        return None

    @handle_errors(default_return=[])
    def get_mappings_by_sku(self, sku_id: str) -> List[Dict[str, Any]]:
        """
        根据SKU ID获取所有平台映射

        Args:
            sku_id: 本地SKU ID

        Returns:
            List[Dict[str, Any]]: 映射列表
        """
        sql = """
        SELECT * FROM platform_product_mapping 
        WHERE sku_id = ? AND mapping_status = 'active'
        ORDER BY created_at DESC
        """

        rows = self.db_manager.fetch_all(sql, (sku_id,))
        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [dict(zip(columns, row)) for row in rows]

        return []

    @handle_errors(default_return=False)
    def update_mapping_status(
        self, mapping_id: str, status: str, error_message: Optional[str] = None
    ) -> bool:
        """
        更新映射状态

        Args:
            mapping_id: 映射ID
            status: 新状态 (active, inactive, pending)
            error_message: 错误信息

        Returns:
            bool: 更新是否成功
        """
        sql = """
        UPDATE platform_product_mapping 
        SET mapping_status = ?, sync_error_count = CASE 
            WHEN ? = 'active' THEN 0 
            ELSE sync_error_count + 1 
        END, updated_at = CURRENT_TIMESTAMP
        WHERE mapping_id = ?
        """

        return self.db_manager.execute(sql, (status, status, mapping_id))

    @handle_errors(default_return=False)
    def update_sync_time(self, mapping_id: str, success: bool = True) -> bool:
        """
        更新同步时间

        Args:
            mapping_id: 映射ID
            success: 是否同步成功

        Returns:
            bool: 更新是否成功
        """
        if success:
            sql = """
            UPDATE platform_product_mapping 
            SET last_sync_time = CURRENT_TIMESTAMP, sync_error_count = 0
            WHERE mapping_id = ?
            """
        else:
            sql = """
            UPDATE platform_product_mapping 
            SET sync_error_count = sync_error_count + 1
            WHERE mapping_id = ?
            """

        return self.db_manager.execute(sql, (mapping_id,))

    @handle_errors(default_return=False)
    def delete_mapping(self, mapping_id: str) -> bool:
        """
        删除映射关系

        Args:
            mapping_id: 映射ID

        Returns:
            bool: 删除是否成功
        """
        # 先获取映射信息
        mapping = self.get_mapping_by_id(mapping_id)
        if not mapping:
            return False

        sql = "DELETE FROM platform_product_mapping WHERE mapping_id = ?"

        if self.db_manager.execute(sql, (mapping_id,)):
            self.logger.info(f"删除平台映射成功: {mapping_id}")

            # 更新商品的多平台状态
            self._update_product_platform_status(mapping["sku_id"])
            return True

        return False

    @handle_errors(default_return=None)
    def get_mapping_by_id(self, mapping_id: str) -> Optional[Dict[str, Any]]:
        """
        根据映射ID获取映射信息

        Args:
            mapping_id: 映射ID

        Returns:
            Optional[Dict[str, Any]]: 映射信息
        """
        sql = "SELECT * FROM platform_product_mapping WHERE mapping_id = ?"

        row = self.db_manager.fetch_one(sql, (mapping_id,))
        if row:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return dict(zip(columns, row))

        return None

    @handle_errors(default_return=False)
    def _update_product_platform_status(self, sku_id: str) -> bool:
        """
        更新商品的多平台状态

        Args:
            sku_id: 商品SKU ID

        Returns:
            bool: 更新是否成功
        """
        # 统计该商品的平台映射数量
        sql = """
        SELECT COUNT(*) as count FROM platform_product_mapping 
        WHERE sku_id = ? AND mapping_status = 'active'
        """

        result = self.db_manager.fetch_one(sql, (sku_id,))
        platform_count = result[0] if result else 0

        # 更新商品表
        update_sql = """
        UPDATE products 
        SET is_multi_platform = ?, platform_count = ?, updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        is_multi_platform = platform_count > 0

        return self.db_manager.execute(
            update_sql, (is_multi_platform, platform_count, sku_id)
        )

    @handle_errors(default_return=[])
    def search_unmapped_products(
        self, platform_type: str, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        搜索未映射的商品

        Args:
            platform_type: 平台类型
            limit: 限制数量

        Returns:
            List[Dict[str, Any]]: 未映射商品列表
        """
        sql = """
        SELECT p.* FROM products p
        WHERE p.product_id NOT IN (
            SELECT DISTINCT sku_id FROM platform_product_mapping 
            WHERE platform_type = ? AND mapping_status = 'active'
        )
        ORDER BY p.created_at DESC
        LIMIT ?
        """

        rows = self.db_manager.fetch_all(sql, (platform_type, limit))
        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [dict(zip(columns, row)) for row in rows]

        return []

    @handle_errors(default_return=0.0)
    def calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        计算商品名称相似度

        Args:
            name1: 商品名称1
            name2: 商品名称2

        Returns:
            float: 相似度 (0.0-1.0)
        """
        if not name1 or not name2:
            return 0.0

        # 简单的字符串相似度计算
        return SequenceMatcher(None, name1.lower(), name2.lower()).ratio()

    @handle_errors(default_return=[])
    def smart_match_products(
        self, platform_order_data: Dict[str, Any], confidence_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """
        智能匹配商品

        Args:
            platform_order_data: 平台订单数据，包含商品名称、价格等信息
            confidence_threshold: 置信度阈值

        Returns:
            List[Dict[str, Any]]: 匹配结果列表，按置信度排序
        """
        platform_product_name = platform_order_data.get("product_name", "")
        platform_price = platform_order_data.get("price", 0)

        if not platform_product_name:
            return []

        # 获取所有商品进行匹配
        sql = """
        SELECT product_id, name, selling_price, category, brand
        FROM products
        WHERE status = '在库'
        """

        rows = self.db_manager.fetch_all(sql)
        if not rows:
            return []

        columns = [description[0] for description in self.db_manager.cursor.description]
        products = [dict(zip(columns, row)) for row in rows]

        matches = []

        for product in products:
            # 计算名称相似度
            name_similarity = self.calculate_name_similarity(
                platform_product_name, product["name"]
            )

            # 计算价格相似度
            price_similarity = 0.0
            if platform_price > 0 and product["selling_price"] > 0:
                price_diff = abs(platform_price - product["selling_price"])
                max_price = max(platform_price, product["selling_price"])
                price_similarity = max(0, 1 - (price_diff / max_price))

            # 综合置信度计算 (名称权重70%，价格权重30%)
            confidence = name_similarity * 0.7 + price_similarity * 0.3

            if confidence >= confidence_threshold:
                matches.append(
                    {
                        "product_id": product["product_id"],
                        "product_name": product["name"],
                        "confidence": confidence,
                        "name_similarity": name_similarity,
                        "price_similarity": price_similarity,
                        "matching_method": "smart_algorithm",
                    }
                )

        # 按置信度排序
        matches.sort(key=lambda x: x["confidence"], reverse=True)

        return matches

    @handle_errors(default_return=False)
    def log_order_matching(
        self,
        platform_order_id: str,
        platform_type: str,
        platform_product_id: str,
        product_name: str,
        matching_status: str,
        matched_sku_id: Optional[str] = None,
        confidence_score: Optional[float] = None,
        matching_method: str = "manual",
        error_reason: Optional[str] = None,
        manual_operator: Optional[str] = None,
        processing_time: Optional[int] = None,
    ) -> bool:
        """
        记录订单匹配日志

        Args:
            platform_order_id: 平台订单号
            platform_type: 平台类型
            platform_product_id: 平台商品ID
            product_name: 商品名称
            matching_status: 匹配状态 (auto_matched, manual_matched, failed, pending)
            matched_sku_id: 匹配到的本地SKU
            confidence_score: 匹配置信度
            matching_method: 匹配方法
            error_reason: 失败原因
            manual_operator: 手动操作员
            processing_time: 处理时间(毫秒)

        Returns:
            bool: 记录是否成功
        """
        log_id = str(uuid.uuid4())

        sql = """
        INSERT INTO order_matching_logs
        (log_id, platform_order_id, platform_type, platform_product_id, product_name,
         matching_status, matched_sku_id, confidence_score, matching_method,
         error_reason, manual_operator, processing_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        params = (
            log_id,
            platform_order_id,
            platform_type,
            platform_product_id,
            product_name,
            matching_status,
            matched_sku_id,
            confidence_score,
            matching_method,
            error_reason,
            manual_operator,
            processing_time,
        )

        return self.db_manager.execute(sql, params)

    @handle_errors(default_return=[])
    def get_pending_matches(
        self, platform_type: Optional[str] = None, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取待处理的匹配记录

        Args:
            platform_type: 平台类型过滤
            limit: 限制数量

        Returns:
            List[Dict[str, Any]]: 待处理匹配列表
        """
        sql = """
        SELECT * FROM order_matching_logs
        WHERE matching_status = 'pending'
        """
        params = []

        if platform_type:
            sql += " AND platform_type = ?"
            params.append(platform_type)

        sql += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)

        rows = self.db_manager.fetch_all(sql, tuple(params))
        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [dict(zip(columns, row)) for row in rows]

        return []

    @handle_errors(default_return=False)
    def batch_create_mappings(self, mappings: List[Dict[str, Any]]) -> bool:
        """
        批量创建映射关系

        Args:
            mappings: 映射数据列表

        Returns:
            bool: 创建是否成功
        """
        if not mappings:
            return True

        sql = """
        INSERT INTO platform_product_mapping
        (mapping_id, sku_id, platform_type, platform_product_id, platform_store_id,
         platform_sku_code, auto_sync_enabled, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """

        params_list = []
        for mapping in mappings:
            mapping_id = str(uuid.uuid4())
            params = (
                mapping_id,
                mapping["sku_id"],
                mapping["platform_type"],
                mapping["platform_product_id"],
                mapping.get("platform_store_id"),
                mapping.get("platform_sku_code"),
                mapping.get("auto_sync_enabled", True),
                mapping.get("notes"),
            )
            params_list.append(params)

        if self.db_manager.execute_many(sql, params_list):
            self.logger.info(f"批量创建映射成功: {len(mappings)}条")

            # 更新所有相关商品的多平台状态
            for mapping in mappings:
                self._update_product_platform_status(mapping["sku_id"])

            return True

        return False
