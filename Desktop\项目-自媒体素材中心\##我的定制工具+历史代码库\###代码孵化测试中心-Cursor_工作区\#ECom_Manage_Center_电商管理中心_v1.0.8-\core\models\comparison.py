#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应链对比数据模型

用于供应链对比分析功能的数据模型。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import uuid
import json


@dataclass
class ComparisonItem:
    """对比项目"""
    
    item_id: Optional[str] = None
    product_name: str = ""
    source_platform: str = ""  # 来源平台
    source_label: str = ""  # 来源标签 (如: 1688, 我的店1, 我的店2)
    source_url: Optional[str] = None
    
    # 价格信息
    price: Decimal = field(default_factory=lambda: Decimal('0'))
    original_price: Optional[Decimal] = None
    shipping_fee: Decimal = field(default_factory=lambda: Decimal('0'))
    free_shipping_threshold: Optional[Decimal] = None  # 包邮门槛
    
    # 库存信息
    stock: int = 0
    min_order_quantity: int = 1
    
    # 商品信息
    image_url: Optional[str] = None
    specifications: Dict[str, Any] = field(default_factory=dict)
    supplier_info: Dict[str, Any] = field(default_factory=dict)
    
    # 状态和时间
    last_updated: Optional[datetime] = None
    status: str = "active"  # active, inactive, out_of_stock
    notes: Optional[str] = None
    
    def __post_init__(self):
        if self.item_id is None:
            self.item_id = f"CI{uuid.uuid4().hex[:8].upper()}"
        
        if self.last_updated is None:
            self.last_updated = datetime.now()
        
        # 确保价格字段为Decimal类型
        self._ensure_decimal_fields()
    
    def _ensure_decimal_fields(self):
        """确保价格字段为Decimal类型"""
        decimal_fields = ['price', 'original_price', 'shipping_fee', 'free_shipping_threshold']
        
        for field_name in decimal_fields:
            value = getattr(self, field_name)
            if value is not None and not isinstance(value, Decimal):
                setattr(self, field_name, Decimal(str(value)))
    
    def get_final_price(self, quantity: int = 1) -> Decimal:
        """获取最终价格（含运费）"""
        total_price = self.price * Decimal(str(quantity))
        
        # 检查是否满足包邮条件
        if self.free_shipping_threshold and total_price >= self.free_shipping_threshold:
            return total_price
        else:
            return total_price + self.shipping_fee
    
    def get_discount_rate(self) -> Decimal:
        """获取折扣率"""
        if self.original_price and self.original_price > 0:
            return ((self.original_price - self.price) / self.original_price) * Decimal('100')
        return Decimal('0')
    
    def is_available(self) -> bool:
        """检查是否可用"""
        return self.status == "active" and self.stock > 0
    
    def update_price(self, new_price: Decimal, reason: str = ""):
        """更新价格"""
        old_price = self.price
        self.price = new_price
        self.last_updated = datetime.now()
        
        # 记录价格变化到supplier_info
        if 'price_changes' not in self.supplier_info:
            self.supplier_info['price_changes'] = []
        
        self.supplier_info['price_changes'].append({
            'timestamp': datetime.now().isoformat(),
            'old_price': float(old_price),
            'new_price': float(new_price),
            'reason': reason
        })
    
    def update_stock(self, new_stock: int, reason: str = ""):
        """更新库存"""
        old_stock = self.stock
        self.stock = new_stock
        self.last_updated = datetime.now()
        
        # 更新状态
        if new_stock <= 0:
            self.status = "out_of_stock"
        elif self.status == "out_of_stock" and new_stock > 0:
            self.status = "active"
        
        # 记录库存变化
        if 'stock_changes' not in self.supplier_info:
            self.supplier_info['stock_changes'] = []
        
        self.supplier_info['stock_changes'].append({
            'timestamp': datetime.now().isoformat(),
            'old_stock': old_stock,
            'new_stock': new_stock,
            'reason': reason
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'item_id': self.item_id,
            'product_name': self.product_name,
            'source_platform': self.source_platform,
            'source_label': self.source_label,
            'source_url': self.source_url,
            'price': float(self.price),
            'original_price': float(self.original_price) if self.original_price else None,
            'shipping_fee': float(self.shipping_fee),
            'free_shipping_threshold': float(self.free_shipping_threshold) if self.free_shipping_threshold else None,
            'stock': self.stock,
            'min_order_quantity': self.min_order_quantity,
            'image_url': self.image_url,
            'specifications': json.dumps(self.specifications),
            'supplier_info': json.dumps(self.supplier_info),
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'status': self.status,
            'notes': self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComparisonItem':
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get('specifications'), str):
            data['specifications'] = json.loads(data['specifications']) if data['specifications'] else {}
        
        if isinstance(data.get('supplier_info'), str):
            data['supplier_info'] = json.loads(data['supplier_info']) if data['supplier_info'] else {}
        
        # 处理时间字段
        if isinstance(data.get('last_updated'), str):
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        
        return cls(**data)


@dataclass
class ComparisonGroup:
    """
    对比组数据模型
    
    管理同一商品在不同平台/来源的对比信息
    """
    
    # 基础信息
    group_id: Optional[str] = None
    group_name: str = ""
    description: str = ""
    category: Optional[str] = None
    status: str = "active"  # active, archived
    
    # 对比项目
    items: List[ComparisonItem] = field(default_factory=list)
    
    # 统计信息
    total_items: int = 0
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    avg_price: Optional[Decimal] = None
    price_range: Optional[Decimal] = None
    
    # 推荐信息
    best_price_item: Optional[str] = None  # 最低价商品ID
    best_value_item: Optional[str] = None  # 最佳性价比商品ID
    
    # 备注和元数据
    notes: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.group_id is None:
            self.group_id = f"CG{uuid.uuid4().hex[:8].upper()}"
        
        if self.created_at is None:
            self.created_at = datetime.now()
        
        self.updated_at = datetime.now()
        
        # 更新统计信息
        self.update_statistics()
    
    def add_item(self, item: ComparisonItem):
        """添加对比项目"""
        # 检查是否已存在相同来源的项目
        for existing_item in self.items:
            if (existing_item.source_platform == item.source_platform and 
                existing_item.source_label == item.source_label):
                # 更新现有项目
                existing_item.__dict__.update(item.__dict__)
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        
        # 添加新项目
        self.items.append(item)
        self.update_statistics()
        self.updated_at = datetime.now()
        return True
    
    def remove_item(self, item_id: str) -> bool:
        """移除对比项目"""
        for i, item in enumerate(self.items):
            if item.item_id == item_id:
                del self.items[i]
                self.update_statistics()
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_item(self, item_id: str) -> Optional[ComparisonItem]:
        """获取对比项目"""
        for item in self.items:
            if item.item_id == item_id:
                return item
        return None
    
    def get_item_by_source(self, source_platform: str, source_label: str) -> Optional[ComparisonItem]:
        """根据来源获取对比项目"""
        for item in self.items:
            if item.source_platform == source_platform and item.source_label == source_label:
                return item
        return None
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_items = len(self.items)
        
        if self.items:
            prices = [item.price for item in self.items if item.is_available()]
            
            if prices:
                self.min_price = min(prices)
                self.max_price = max(prices)
                self.avg_price = sum(prices) / len(prices)
                self.price_range = self.max_price - self.min_price
                
                # 找到最低价商品
                min_price_item = min(self.items, key=lambda x: x.price if x.is_available() else float('inf'))
                if min_price_item.is_available():
                    self.best_price_item = min_price_item.item_id
                
                # 找到最佳性价比商品（考虑运费）
                best_value_item = min(self.items, 
                                    key=lambda x: x.get_final_price() if x.is_available() else float('inf'))
                if best_value_item.is_available():
                    self.best_value_item = best_value_item.item_id
            else:
                self.min_price = None
                self.max_price = None
                self.avg_price = None
                self.price_range = None
                self.best_price_item = None
                self.best_value_item = None
        else:
            self.min_price = None
            self.max_price = None
            self.avg_price = None
            self.price_range = None
            self.best_price_item = None
            self.best_value_item = None
    
    def get_available_items(self) -> List[ComparisonItem]:
        """获取可用的对比项目"""
        return [item for item in self.items if item.is_available()]
    
    def get_price_comparison(self) -> Dict[str, Any]:
        """获取价格对比信息"""
        available_items = self.get_available_items()
        
        if not available_items:
            return {}
        
        comparison = {
            'items': [],
            'statistics': {
                'min_price': float(self.min_price) if self.min_price else None,
                'max_price': float(self.max_price) if self.max_price else None,
                'avg_price': float(self.avg_price) if self.avg_price else None,
                'price_range': float(self.price_range) if self.price_range else None
            },
            'recommendations': {
                'best_price_item': self.best_price_item,
                'best_value_item': self.best_value_item
            }
        }
        
        for item in available_items:
            item_info = {
                'item_id': item.item_id,
                'source_label': item.source_label,
                'price': float(item.price),
                'final_price': float(item.get_final_price()),
                'stock': item.stock,
                'discount_rate': float(item.get_discount_rate()),
                'is_best_price': item.item_id == self.best_price_item,
                'is_best_value': item.item_id == self.best_value_item
            }
            comparison['items'].append(item_info)
        
        return comparison
    
    def archive_group(self, reason: str = ""):
        """归档对比组"""
        self.status = "archived"
        self.updated_at = datetime.now()
        
        # 记录归档原因
        if 'archive_info' not in self.metadata:
            self.metadata['archive_info'] = {}
        
        self.metadata['archive_info'] = {
            'timestamp': datetime.now().isoformat(),
            'reason': reason
        }
    
    def activate_group(self):
        """激活对比组"""
        self.status = "active"
        self.updated_at = datetime.now()
        
        # 清除归档信息
        if 'archive_info' in self.metadata:
            del self.metadata['archive_info']
    
    def is_active(self) -> bool:
        """检查对比组是否活跃"""
        return self.status == "active"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'group_id': self.group_id,
            'group_name': self.group_name,
            'description': self.description,
            'category': self.category,
            'status': self.status,
            'items': json.dumps([item.to_dict() for item in self.items]),
            'total_items': self.total_items,
            'min_price': float(self.min_price) if self.min_price else None,
            'max_price': float(self.max_price) if self.max_price else None,
            'avg_price': float(self.avg_price) if self.avg_price else None,
            'price_range': float(self.price_range) if self.price_range else None,
            'best_price_item': self.best_price_item,
            'best_value_item': self.best_value_item,
            'notes': self.notes,
            'metadata': json.dumps(self.metadata),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComparisonGroup':
        """从字典创建对象"""
        # 处理JSON字段
        if isinstance(data.get('items'), str):
            items_data = json.loads(data['items']) if data['items'] else []
            data['items'] = [ComparisonItem.from_dict(item_data) for item_data in items_data]
        
        if isinstance(data.get('metadata'), str):
            data['metadata'] = json.loads(data['metadata']) if data['metadata'] else {}
        
        # 处理时间字段
        if isinstance(data.get('created_at'), str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        if isinstance(data.get('updated_at'), str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    @classmethod
    def from_db_row(cls, row, columns: List[str]) -> Optional['ComparisonGroup']:
        """从数据库行创建对象"""
        if not row:
            return None
        
        data = dict(zip(columns, row))
        return cls.from_dict(data)
    
    def validate(self) -> bool:
        """验证数据有效性"""
        if not self.group_name:
            raise ValueError("对比组名称不能为空")
        
        if self.status not in ["active", "archived"]:
            raise ValueError("对比组状态无效")
        
        # 验证对比项目
        for item in self.items:
            if not item.product_name:
                raise ValueError("对比项目商品名称不能为空")
            
            if not item.source_platform:
                raise ValueError("对比项目来源平台不能为空")
            
            if not item.source_label:
                raise ValueError("对比项目来源标签不能为空")
        
        return True
    
    def __str__(self) -> str:
        return f"{self.group_id} - {self.group_name} ({self.total_items}项)"
    
    def __repr__(self) -> str:
        return f"ComparisonGroup(group_id='{self.group_id}', name='{self.group_name}', items={self.total_items})"
