#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品管理器

负责商品的CRUD操作、库存管理、价格计算等业务逻辑。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from datetime import datetime
import json

from core.database import DatabaseManager
from core.models.product import Product
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class ProductManager(LoggerMixin):
    """商品管理器"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化商品管理器

        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger.info("商品管理器初始化完成")

    @handle_errors(default_return=False)
    def create_product(self, product: Product) -> bool:
        """
        创建商品

        Args:
            product: 商品对象

        Returns:
            bool: 创建是否成功
        """
        try:
            # 验证商品数据
            product.validate()

            # 检查商品编码是否重复
            if product.code and self.get_product_by_code(product.code):
                raise ValueError(f"商品编码 {product.code} 已存在")

            # 计算成本和利润
            product.calculate_total_cost()
            product.calculate_profit()

            # 插入数据库
            sql = """
            INSERT INTO products (
                product_id, name, code, category, description, quantity, unit, status,
                location, purchase_price, shipping_cost, other_cost, total_cost,
                selling_price, discount_rate, total_profit, supplier, supplier_id,
                supplier_link, purchase_link, selling_link, purchaser, image_path,
                images, brand, model, color, size, weight, tags, platform_products,
                remarks, metadata, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            data = product.to_dict()
            params = (
                data["product_id"],
                data["name"],
                data["code"],
                data["category"],
                data["description"],
                data["quantity"],
                data["unit"],
                data["status"],
                data["location"],
                data["purchase_price"],
                data["shipping_cost"],
                data["other_cost"],
                data["total_cost"],
                data["selling_price"],
                data["discount_rate"],
                data["total_profit"],
                data["supplier"],
                data["supplier_id"],
                data["supplier_link"],
                data["purchase_link"],
                data["selling_link"],
                data["purchaser"],
                data["image_path"],
                data["images"],
                data["brand"],
                data["model"],
                data["color"],
                data["size"],
                data["weight"],
                data["tags"],
                data["platform_products"],
                data["remarks"],
                data["metadata"],
                data["created_at"],
                data["updated_at"],
            )

            cursor = self.db_manager.execute(sql, params)
            if cursor:
                self.logger.info(f"商品创建成功: {product.product_id} - {product.name}")
                return True
            else:
                self.logger.error(f"商品创建失败: {product.name}")
                return False

        except Exception as e:
            self.logger.error(f"创建商品时出错: {e}")
            return False

    @handle_errors(default_return=None)
    def get_product(self, product_id: str) -> Optional[Product]:
        """
        获取商品

        Args:
            product_id: 商品ID

        Returns:
            Product: 商品对象
        """
        sql = "SELECT * FROM products WHERE product_id = ?"
        row = self.db_manager.fetch_one(sql, (product_id,))

        if row:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return Product.from_db_row(row, columns)

        return None

    @handle_errors(default_return=None)
    def get_product_by_code(self, code: str) -> Optional[Product]:
        """
        根据商品编码获取商品

        Args:
            code: 商品编码

        Returns:
            Product: 商品对象
        """
        sql = "SELECT * FROM products WHERE code = ?"
        row = self.db_manager.fetch_one(sql, (code,))

        if row:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return Product.from_db_row(row, columns)

        return None

    @handle_errors(default_return=[])
    def get_products(
        self,
        category: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Product]:
        """
        获取商品列表

        Args:
            category: 商品分类
            status: 商品状态
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Product]: 商品列表
        """
        sql = "SELECT * FROM products WHERE 1=1"
        params = []

        if category:
            sql += " AND category = ?"
            params.append(category)

        if status:
            sql += " AND status = ?"
            params.append(status)

        sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        rows = self.db_manager.fetch_all(sql, tuple(params))

        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [Product.from_db_row(row, columns) for row in rows]

        return []

    @handle_errors(default_return=[])
    def search_products(self, keyword: str, limit: int = 100) -> List[Product]:
        """
        搜索商品

        Args:
            keyword: 搜索关键词
            limit: 限制数量

        Returns:
            List[Product]: 商品列表
        """
        sql = """
        SELECT * FROM products 
        WHERE name LIKE ? OR code LIKE ? OR description LIKE ? OR category LIKE ?
        ORDER BY created_at DESC LIMIT ?
        """

        search_term = f"%{keyword}%"
        params = (search_term, search_term, search_term, search_term, limit)

        rows = self.db_manager.fetch_all(sql, params)

        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [Product.from_db_row(row, columns) for row in rows]

        return []

    @handle_errors(default_return=False)
    def update_product(self, product: Product) -> bool:
        """
        更新商品

        Args:
            product: 商品对象

        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证商品数据
            product.validate()

            # 重新计算成本和利润
            product.calculate_total_cost()
            product.calculate_profit()
            product.updated_at = datetime.now()

            # 更新数据库
            sql = """
            UPDATE products SET
                name = ?, code = ?, category = ?, description = ?, quantity = ?,
                unit = ?, status = ?, location = ?, purchase_price = ?,
                shipping_cost = ?, other_cost = ?, total_cost = ?, selling_price = ?,
                discount_rate = ?, total_profit = ?, supplier = ?, supplier_id = ?,
                supplier_link = ?, purchase_link = ?, selling_link = ?, purchaser = ?,
                image_path = ?, images = ?, brand = ?, model = ?, color = ?, size = ?,
                weight = ?, tags = ?, platform_products = ?, remarks = ?, metadata = ?,
                updated_at = ?
            WHERE product_id = ?
            """

            data = product.to_dict()
            params = (
                data["name"],
                data["code"],
                data["category"],
                data["description"],
                data["quantity"],
                data["unit"],
                data["status"],
                data["location"],
                data["purchase_price"],
                data["shipping_cost"],
                data["other_cost"],
                data["total_cost"],
                data["selling_price"],
                data["discount_rate"],
                data["total_profit"],
                data["supplier"],
                data["supplier_id"],
                data["supplier_link"],
                data["purchase_link"],
                data["selling_link"],
                data["purchaser"],
                data["image_path"],
                data["images"],
                data["brand"],
                data["model"],
                data["color"],
                data["size"],
                data["weight"],
                data["tags"],
                data["platform_products"],
                data["remarks"],
                data["metadata"],
                data["updated_at"],
                data["product_id"],
            )

            cursor = self.db_manager.execute(sql, params)
            if cursor:
                self.logger.info(f"商品更新成功: {product.product_id} - {product.name}")
                return True
            else:
                self.logger.error(f"商品更新失败: {product.name}")
                return False

        except Exception as e:
            self.logger.error(f"更新商品时出错: {e}")
            return False

    @handle_errors(default_return=False)
    def delete_product(self, product_id: str) -> bool:
        """
        删除商品

        Args:
            product_id: 商品ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查商品是否存在
            product = self.get_product(product_id)
            if not product:
                self.logger.warning(f"要删除的商品不存在: {product_id}")
                return False

            # 检查是否有关联的批次或订单
            # TODO: 添加关联检查逻辑

            # 删除商品
            sql = "DELETE FROM products WHERE product_id = ?"
            cursor = self.db_manager.execute(sql, (product_id,))

            if cursor:
                self.logger.info(f"商品删除成功: {product_id} - {product.name}")
                return True
            else:
                self.logger.error(f"商品删除失败: {product_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除商品时出错: {e}")
            return False

    @handle_errors(default_return=False)
    def update_stock(
        self, product_id: str, quantity_change: int, reason: str = ""
    ) -> bool:
        """
        更新库存

        Args:
            product_id: 商品ID
            quantity_change: 库存变化量
            reason: 变更原因

        Returns:
            bool: 更新是否成功
        """
        try:
            product = self.get_product(product_id)
            if not product:
                self.logger.error(f"商品不存在: {product_id}")
                return False

            # 更新库存
            product.update_stock(quantity_change, reason)

            # 保存到数据库
            if self.update_product(product):
                # 记录库存变化到交易记录
                self._record_stock_transaction(product_id, quantity_change, reason)
                return True

            return False

        except Exception as e:
            self.logger.error(f"更新库存时出错: {e}")
            return False

    def _record_stock_transaction(
        self, product_id: str, quantity_change: int, reason: str
    ):
        """记录库存交易"""
        try:
            transaction_type = "入库" if quantity_change > 0 else "出库"

            sql = """
            INSERT INTO transactions (
                transaction_id, product_id, transaction_type, quantity, 
                unit_price, reference_id, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            import uuid

            transaction_id = f"T{uuid.uuid4().hex[:8].upper()}"

            params = (
                transaction_id,
                product_id,
                transaction_type,
                abs(quantity_change),
                0,
                None,
                reason,
                datetime.now().isoformat(),
            )

            self.db_manager.execute(sql, params)

        except Exception as e:
            self.logger.error(f"记录库存交易失败: {e}")

    @handle_errors(default_return=0)
    def get_product_count(
        self, category: Optional[str] = None, status: Optional[str] = None
    ) -> int:
        """
        获取商品数量

        Args:
            category: 商品分类
            status: 商品状态

        Returns:
            int: 商品数量
        """
        sql = "SELECT COUNT(*) FROM products WHERE 1=1"
        params = []

        if category:
            sql += " AND category = ?"
            params.append(category)

        if status:
            sql += " AND status = ?"
            params.append(status)

        result = self.db_manager.fetch_one(sql, tuple(params))
        return result[0] if result else 0

    @handle_errors(default_return=[])
    def get_categories(self) -> List[str]:
        """
        获取所有商品分类

        Returns:
            List[str]: 分类列表
        """
        sql = "SELECT DISTINCT category FROM products WHERE category IS NOT NULL ORDER BY category"
        rows = self.db_manager.fetch_all(sql)
        return [row[0] for row in rows] if rows else []

    @handle_errors(default_return={})
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取商品统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_products": 0,
            "total_value": 0.0,
            "by_status": {},
            "by_category": {},
            "low_stock_products": 0,
        }

        # 总商品数
        stats["total_products"] = self.get_product_count()

        # 按状态统计
        sql = "SELECT status, COUNT(*) FROM products GROUP BY status"
        rows = self.db_manager.fetch_all(sql)
        if rows:
            stats["by_status"] = {row[0]: row[1] for row in rows}

        # 按分类统计
        sql = "SELECT category, COUNT(*) FROM products GROUP BY category"
        rows = self.db_manager.fetch_all(sql)
        if rows:
            stats["by_category"] = {row[0]: row[1] for row in rows}

        # 总价值
        sql = "SELECT SUM(total_cost * quantity) FROM products"
        result = self.db_manager.fetch_one(sql)
        if result and result[0]:
            stats["total_value"] = float(result[0])

        # 低库存商品数（库存小于10）
        sql = "SELECT COUNT(*) FROM products WHERE quantity < 10"
        result = self.db_manager.fetch_one(sql)
        if result:
            stats["low_stock_products"] = result[0]

        return stats

    # =====================================================
    # 多平台功能扩展方法
    # =====================================================

    @handle_errors(default_return=False)
    def enable_multi_platform(self, product_id: str) -> bool:
        """
        启用商品的多平台功能

        Args:
            product_id: 商品ID

        Returns:
            bool: 操作是否成功
        """
        sql = """
        UPDATE products
        SET is_multi_platform = TRUE, updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        if self.db_manager.execute(sql, (product_id,)):
            self.logger.info(f"商品 {product_id} 已启用多平台功能")
            return True

        return False

    @handle_errors(default_return=False)
    def disable_multi_platform(self, product_id: str) -> bool:
        """
        禁用商品的多平台功能

        Args:
            product_id: 商品ID

        Returns:
            bool: 操作是否成功
        """
        sql = """
        UPDATE products
        SET is_multi_platform = FALSE, platform_count = 0, updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        if self.db_manager.execute(sql, (product_id,)):
            self.logger.info(f"商品 {product_id} 已禁用多平台功能")
            return True

        return False

    @handle_errors(default_return=False)
    def update_sync_status(
        self, product_id: str, status: str, error_message: Optional[str] = None
    ) -> bool:
        """
        更新商品同步状态

        Args:
            product_id: 商品ID
            status: 同步状态 (none, syncing, synced, error)
            error_message: 错误信息

        Returns:
            bool: 更新是否成功
        """
        sql = """
        UPDATE products
        SET sync_status = ?, sync_error_message = ?,
            last_sync_time = CASE WHEN ? = 'synced' THEN CURRENT_TIMESTAMP ELSE last_sync_time END,
            updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        return self.db_manager.execute(sql, (status, error_message, status, product_id))

    @handle_errors(default_return=[])
    def get_multi_platform_products(
        self, platform_type: Optional[str] = None
    ) -> List[Product]:
        """
        获取多平台商品列表

        Args:
            platform_type: 平台类型过滤

        Returns:
            List[Product]: 多平台商品列表
        """
        if platform_type:
            sql = """
            SELECT DISTINCT p.* FROM products p
            INNER JOIN platform_product_mapping ppm ON p.product_id = ppm.sku_id
            WHERE p.is_multi_platform = TRUE AND ppm.platform_type = ?
            ORDER BY p.updated_at DESC
            """
            rows = self.db_manager.fetch_all(sql, (platform_type,))
        else:
            sql = """
            SELECT * FROM products
            WHERE is_multi_platform = TRUE
            ORDER BY updated_at DESC
            """
            rows = self.db_manager.fetch_all(sql)

        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [Product.from_dict(dict(zip(columns, row))) for row in rows]

        return []

    @handle_errors(default_return=[])
    def get_sync_pending_products(self) -> List[Product]:
        """
        获取待同步的商品列表

        Returns:
            List[Product]: 待同步商品列表
        """
        sql = """
        SELECT * FROM products
        WHERE is_multi_platform = TRUE
        AND (sync_status = 'none' OR sync_status = 'error')
        ORDER BY updated_at DESC
        """

        rows = self.db_manager.fetch_all(sql)
        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [Product.from_dict(dict(zip(columns, row))) for row in rows]

        return []

    @handle_errors(default_return=False)
    def set_inventory_type(self, product_id: str, inventory_type: str) -> bool:
        """
        设置商品库存类型

        Args:
            product_id: 商品ID
            inventory_type: 库存类型 (physical, dropship, mixed)

        Returns:
            bool: 设置是否成功
        """
        if inventory_type not in ["physical", "dropship", "mixed"]:
            self.logger.error(f"无效的库存类型: {inventory_type}")
            return False

        sql = """
        UPDATE products
        SET inventory_type = ?, updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        return self.db_manager.execute(sql, (inventory_type, product_id))

    @handle_errors(default_return=False)
    def set_stock_thresholds(
        self, product_id: str, min_threshold: int, max_threshold: int
    ) -> bool:
        """
        设置库存阈值

        Args:
            product_id: 商品ID
            min_threshold: 最低库存阈值
            max_threshold: 最高库存阈值

        Returns:
            bool: 设置是否成功
        """
        if min_threshold < 0 or max_threshold <= min_threshold:
            self.logger.error("库存阈值设置无效")
            return False

        sql = """
        UPDATE products
        SET min_stock_threshold = ?, max_stock_threshold = ?, updated_at = CURRENT_TIMESTAMP
        WHERE product_id = ?
        """

        return self.db_manager.execute(sql, (min_threshold, max_threshold, product_id))

    @handle_errors(default_return=[])
    def get_low_stock_products(self, include_dropship: bool = False) -> List[Product]:
        """
        获取低库存商品

        Args:
            include_dropship: 是否包含代发商品

        Returns:
            List[Product]: 低库存商品列表
        """
        if include_dropship:
            sql = """
            SELECT * FROM products
            WHERE quantity <= min_stock_threshold AND stock_warning_enabled = TRUE
            ORDER BY quantity ASC
            """
        else:
            sql = """
            SELECT * FROM products
            WHERE quantity <= min_stock_threshold AND stock_warning_enabled = TRUE
            AND inventory_type = 'physical'
            ORDER BY quantity ASC
            """

        rows = self.db_manager.fetch_all(sql)
        if rows:
            columns = [
                description[0] for description in self.db_manager.cursor.description
            ]
            return [Product.from_dict(dict(zip(columns, row))) for row in rows]

        return []
