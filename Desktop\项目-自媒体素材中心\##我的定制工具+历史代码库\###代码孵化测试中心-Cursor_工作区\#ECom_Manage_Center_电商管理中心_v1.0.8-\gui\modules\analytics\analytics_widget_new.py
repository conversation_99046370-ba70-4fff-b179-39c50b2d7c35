#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析模块 - 新版本

美观的数据分析界面，提供更好的用户体验。
"""

import logging
from datetime import datetime
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QFrame,
    QGridLayout,
    QPushButton,
    QTextEdit,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QGroupBox,
    QToolBar,
    QScrollArea,
    QSplitter,
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from utils.logger import LoggerMixin


class AnalyticsWidget(QWidget, LoggerMixin):
    """数据分析主界面 - 新版本"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager

        # 数据缓存
        self.analytics_data = {}

        # 存储卡片引用，用于刷新数据
        self.cards_frame = None
        self.cards_layout = None

        # 初始化界面
        self.init_ui()

        # 延迟加载数据
        QTimer.singleShot(100, self.load_sample_data)

        self.logger.info("数据分析界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题区域
        title_frame = self.create_title_section()
        layout.addWidget(title_frame)

        # 主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：指标卡片
        cards_frame = self.create_cards_section()
        main_splitter.addWidget(cards_frame)

        # 下半部分：图表和表格
        bottom_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：快速统计
        stats_frame = self.create_stats_section()
        bottom_splitter.addWidget(stats_frame)

        # 右侧：趋势图表
        chart_frame = self.create_chart_section()
        bottom_splitter.addWidget(chart_frame)

        bottom_splitter.setSizes([400, 600])
        main_splitter.addWidget(bottom_splitter)

        main_splitter.setSizes([300, 400])
        layout.addWidget(main_splitter)

    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet(
            """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """
        )

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 10, 20, 10)

        # 标题
        title = QLabel("📊 数据分析仪表板")
        title.setStyleSheet(
            """
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
        """
        )
        layout.addWidget(title)

        layout.addStretch()

        # 实时时间
        self.time_label = QLabel(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        self.time_label.setStyleSheet(
            """
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """
        )
        layout.addWidget(self.time_label)

        # 定时更新时间
        timer = QTimer(self)
        timer.timeout.connect(self.update_time)
        timer.start(1000)

        return frame

    def create_cards_section(self):
        """创建指标卡片区域"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: transparent;
                border: none;
            }
        """
        )

        layout = QGridLayout(frame)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # 存储引用用于刷新
        self.cards_frame = frame
        self.cards_layout = layout

        # 获取真实数据
        cards_data = self.get_real_cards_data()

        for i, (icon, title, value, color, trend) in enumerate(cards_data):
            card = self.create_modern_card(icon, title, value, color, trend)
            row = i // 4
            col = i % 4
            layout.addWidget(card, row, col)

        return frame

    def get_real_cards_data(self):
        """获取真实的卡片数据"""
        try:
            # 获取商品总数
            product_count = self.get_product_count()

            # 获取库存价值
            inventory_value = self.get_inventory_value()

            # 获取库存预警数量
            warning_count = self.get_warning_count()

            # 获取热销商品数量
            hot_products = self.get_hot_products_count()

            return [
                ("📦", "商品总数", str(product_count), "#4CAF50", f"当前库存商品数量"),
                (
                    "💰",
                    "库存价值",
                    f"¥{inventory_value:,.2f}",
                    "#2196F3",
                    f"总库存估值",
                ),
                ("⚠️", "库存预警", str(warning_count), "#FF9800", f"需要关注的商品"),
                ("📊", "热销商品", str(hot_products), "#9C27B0", f"销量较好的商品"),
            ]
        except Exception as e:
            print(f"获取卡片数据失败: {e}")
            # 如果获取数据失败，返回默认数据
            return [
                ("📦", "商品总数", "0", "#4CAF50", "数据加载中..."),
                ("💰", "库存价值", "¥0.00", "#2196F3", "数据加载中..."),
                ("⚠️", "库存预警", "0", "#FF9800", "数据加载中..."),
                ("📊", "热销商品", "0", "#9C27B0", "数据加载中..."),
            ]

    def get_product_count(self):
        """获取商品总数"""
        try:
            if hasattr(self.db_manager, "get_all_products"):
                products = self.db_manager.get_all_products()
                return len(products) if products else 0
            else:
                # 尝试直接查询数据库
                cursor = self.db_manager.connection.cursor()
                cursor.execute("SELECT COUNT(*) FROM products")
                result = cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            print(f"获取商品总数失败: {e}")
            return 0

    def get_inventory_value(self):
        """获取库存价值"""
        try:
            if hasattr(self.db_manager, "get_all_products"):
                products = self.db_manager.get_all_products()
                total_value = 0
                for product in products:
                    # 假设产品有价格和库存数量字段
                    price = getattr(product, "price", 0) or 0
                    quantity = getattr(product, "quantity", 0) or 0
                    total_value += price * quantity
                return total_value
            else:
                # 尝试直接查询数据库
                cursor = self.db_manager.connection.cursor()
                cursor.execute(
                    "SELECT SUM(price * quantity) FROM products WHERE price IS NOT NULL AND quantity IS NOT NULL"
                )
                result = cursor.fetchone()
                return result[0] if result and result[0] else 0
        except Exception as e:
            print(f"获取库存价值失败: {e}")
            return 0

    def get_warning_count(self):
        """获取库存预警数量"""
        try:
            if hasattr(self.db_manager, "get_all_products"):
                products = self.db_manager.get_all_products()
                warning_count = 0
                for product in products:
                    quantity = getattr(product, "quantity", 0) or 0
                    min_stock = (
                        getattr(product, "min_stock", 10) or 10
                    )  # 默认最小库存为10
                    if quantity <= min_stock:
                        warning_count += 1
                return warning_count
            else:
                # 尝试直接查询数据库
                cursor = self.db_manager.connection.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM products WHERE quantity <= COALESCE(min_stock, 10)"
                )
                result = cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            print(f"获取库存预警数量失败: {e}")
            return 0

    def get_hot_products_count(self):
        """获取热销商品数量"""
        try:
            # 这里可以根据销量或其他指标来判断热销商品
            # 暂时返回一个简单的计算
            if hasattr(self.db_manager, "get_all_products"):
                products = self.db_manager.get_all_products()
                hot_count = 0
                for product in products:
                    # 假设有销量字段，或者根据其他条件判断
                    sales = getattr(product, "sales", 0) or 0
                    if sales > 50:  # 销量大于50的算热销
                        hot_count += 1
                return hot_count
            else:
                # 尝试直接查询数据库
                cursor = self.db_manager.connection.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM products WHERE COALESCE(sales, 0) > 50"
                )
                result = cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            print(f"获取热销商品数量失败: {e}")
            return 0

    def create_modern_card(self, icon, title, value, color, trend):
        """创建现代化的指标卡片 - 重新设计更美观的样式"""
        card = QFrame()
        card.setFixedSize(240, 140)  # 调整卡片尺寸
        card.setStyleSheet(
            f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2a2a2a, stop:1 #1e1e1e);
                border: 2px solid {color};
                border-radius: 15px;
                padding: 0px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3a3a3a, stop:1 #2e2e2e);
                border: 2px solid {color};
                transform: scale(1.02);
            }}
        """
        )

        layout = QVBoxLayout(card)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)

        # 顶部：图标和标题在同一行
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(
            f"""
            font-size: 20px;
            color: {color};
            background: transparent;
            min-width: 24px;
            max-width: 24px;
        """
        )
        header_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(
            f"""
            font-size: 13px;
            color: {color};
            background: transparent;
            font-weight: bold;
        """
        )
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # 中间：数值 - 更大更突出
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        value_label.setStyleSheet(
            f"""
            font-size: 28px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin: 4px 0px;
            padding-left: 4px;
        """
        )
        layout.addWidget(value_label)

        # 底部：趋势信息
        trend_label = QLabel(trend)
        trend_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        trend_label.setStyleSheet(
            f"""
            font-size: 11px;
            color: #aaaaaa;
            background: transparent;
            padding: 2px 4px;
            border-top: 1px solid #444;
            margin-top: 4px;
            padding-top: 6px;
        """
        )
        layout.addWidget(trend_label)

        return card

    def create_stats_section(self):
        """创建统计区域"""
        frame = QGroupBox("📈 快速统计")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #4CAF50;
            }
        """
        )

        layout = QVBoxLayout(frame)

        # 统计列表
        stats_text = QTextEdit()
        stats_text.setMaximumHeight(200)
        stats_text.setStyleSheet(
            """
            QTextEdit {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                font-family: 'Consolas', monospace;
                font-size: 11px;
                padding: 8px;
            }
        """
        )
        stats_text.setPlainText(
            "📊 今日数据概览\n"
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
            "• 新增商品: 3个\n"
            "• 库存变动: +156件\n"
            "• 订单处理: 12个\n"
            "• 供应商活跃度: 87.5%\n"
            "• 系统运行时间: 24小时\n"
            "\n"
            "🎯 关键指标\n"
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
            "• 库存周转率: 4.2次/年 ↗\n"
            "• 平均利润率: 27.1% ↗\n"
            "• 客户满意度: 94.5% ↗\n"
            "• 供应商准时率: 96.2% ↗\n"
        )
        stats_text.setReadOnly(True)
        layout.addWidget(stats_text)

        return frame

    def create_chart_section(self):
        """创建图表区域 - 参考代发管理的美观设计"""
        frame = QGroupBox("📊 数据可视化")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #2196F3;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2b2b2b, stop:1 #1e1e1e);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2196F3;
                font-size: 14px;
            }
        """
        )

        layout = QVBoxLayout(frame)
        layout.setSpacing(15)

        # 图表选择按钮组
        btn_frame = QFrame()
        btn_frame.setStyleSheet(
            """
            QFrame {
                background-color: rgba(33, 150, 243, 0.1);
                border: 1px solid #2196F3;
                border-radius: 8px;
                padding: 8px;
            }
        """
        )
        btn_layout = QHBoxLayout(btn_frame)
        btn_layout.setSpacing(10)

        # 创建美观的按钮
        buttons_data = [
            ("📈", "销售趋势", self.show_sales_trend, "#4CAF50"),
            ("📦", "库存分析", self.show_inventory_turnover, "#FF9800"),
            ("💰", "利润分析", self.show_profit_analysis, "#9C27B0"),
            ("📊", "综合报表", self.show_comprehensive_report, "#2196F3"),
        ]

        for icon, text, callback, color in buttons_data:
            btn = QPushButton(f"{icon} {text}")
            btn.setStyleSheet(
                f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:1 rgba({color[1:]}, 0.8));
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba({color[1:]}, 0.9), stop:1 {color});
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background: {color};
                }}
            """
            )
            btn.clicked.connect(callback)
            btn_layout.addWidget(btn)

        btn_layout.addStretch()
        layout.addWidget(btn_frame)

        # 图表显示区域
        chart_area = self.create_chart_display_area()
        layout.addWidget(chart_area)

        return frame

    def create_chart_display_area(self):
        """创建图表显示区域"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(
            """
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #3a3a3a;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #2196F3;
                border-radius: 6px;
                min-height: 20px;
            }
        """
        )

        # 图表内容
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setSpacing(20)

        # 示例图表卡片
        chart_cards = [
            (
                "📈 销售趋势",
                "本月销售额: ¥15,680.50\n环比增长: +12.3%\n同比增长: +28.7%",
                "#4CAF50",
            ),
            (
                "📦 库存状态",
                "总库存: 1,234件\n预警商品: 3个\n周转率: 4.2次/年",
                "#FF9800",
            ),
            (
                "💰 利润分析",
                "毛利润: ¥3,340.50\n利润率: 27.1%\n净收益: ¥2,890.30",
                "#9C27B0",
            ),
        ]

        for title, content, color in chart_cards:
            card = self.create_chart_card(title, content, color)
            chart_layout.addWidget(card)

        scroll_area.setWidget(chart_widget)
        return scroll_area

    def create_chart_card(self, title, content, color):
        """创建图表卡片"""
        card = QFrame()
        card.setStyleSheet(
            f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #1a1a1a);
                border: 1px solid {color};
                border-radius: 8px;
                padding: 16px;
                margin: 4px;
            }}
            QFrame:hover {{
                border: 2px solid {color};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3d3d3d, stop:1 #2a2a2a);
            }}
        """
        )

        layout = QVBoxLayout(card)
        layout.setSpacing(12)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(
            f"""
            font-size: 16px;
            font-weight: bold;
            color: {color};
            background: transparent;
            padding: 4px;
        """
        )
        layout.addWidget(title_label)

        # 内容
        content_label = QLabel(content)
        content_label.setStyleSheet(
            """
            font-size: 14px;
            color: #cccccc;
            background: transparent;
            line-height: 1.5;
            padding: 8px;
        """
        )
        layout.addWidget(content_label)

        return card

    def update_time(self):
        """更新时间显示"""
        self.time_label.setText(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    def load_sample_data(self):
        """加载示例数据"""
        try:
            # 模拟数据加载
            self.analytics_data = {
                "total_products": 25,
                "inventory_value": 15680.50,
                "suppliers": 8,
                "active_batches": 12,
                "avg_profit_margin": 27.1,
            }
            self.logger.info("示例数据加载完成")
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")

    def show_sales_trend(self):
        """显示销售趋势分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget, QScrollArea

            dialog = QDialog(self)
            dialog.setWindowTitle("销售趋势分析")
            dialog.setModal(True)
            dialog.resize(900, 700)

            layout = QVBoxLayout(dialog)

            tab_widget = QTabWidget()

            # 趋势图表
            trend_widget = QWidget()
            trend_layout = QVBoxLayout(trend_widget)

            # 趋势统计
            trend_stats = QGroupBox("📈 销售趋势统计")
            stats_layout = QGridLayout(trend_stats)

            # 今日销售
            stats_layout.addWidget(QLabel("今日销售:"), 0, 0)
            today_sales = QLabel("¥15,680.50")
            today_sales.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(today_sales, 0, 1)

            # 本周销售
            stats_layout.addWidget(QLabel("本周销售:"), 0, 2)
            week_sales = QLabel("¥89,234.20")
            week_sales.setStyleSheet(
                "font-weight: bold; color: #2196F3; font-size: 14px;"
            )
            stats_layout.addWidget(week_sales, 0, 3)

            trend_layout.addWidget(trend_stats)
            tab_widget.addTab(trend_widget, "趋势分析")

            layout.addWidget(tab_widget)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示销售趋势分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示销售趋势分析失败:\n{e}")

    def show_inventory_turnover(self):
        """显示库存周转分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget, QMessageBox

            dialog = QDialog(self)
            dialog.setWindowTitle("库存周转分析")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # 周转率统计
            turnover_stats = QGroupBox("📊 总体周转指标")
            stats_layout = QGridLayout(turnover_stats)

            stats_layout.addWidget(QLabel("平均周转率:"), 0, 0)
            avg_turnover = QLabel("4.2次/年")
            avg_turnover.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 14px;"
            )
            stats_layout.addWidget(avg_turnover, 0, 1)

            layout.addWidget(turnover_stats)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示库存周转分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示库存周转分析失败:\n{e}")

    def show_profit_analysis(self):
        """显示利润分析"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget, QMessageBox

            dialog = QDialog(self)
            dialog.setWindowTitle("利润分析")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # 利润统计
            profit_stats = QGroupBox("💰 利润指标")
            stats_layout = QGridLayout(profit_stats)

            stats_layout.addWidget(QLabel("总利润:"), 0, 0)
            total_profit = QLabel("¥112,340.00")
            total_profit.setStyleSheet(
                "font-weight: bold; color: #4CAF50; font-size: 16px;"
            )
            stats_layout.addWidget(total_profit, 0, 1)

            layout.addWidget(profit_stats)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示利润分析失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示利润分析失败:\n{e}")

    def show_comprehensive_report(self):
        """显示综合报表"""
        try:
            from PyQt6.QtWidgets import QDialog, QTabWidget, QMessageBox

            dialog = QDialog(self)
            dialog.setWindowTitle("综合数据报表")
            dialog.setModal(True)
            dialog.resize(1000, 700)

            layout = QVBoxLayout(dialog)

            # 综合统计
            comprehensive_stats = QGroupBox("📊 综合数据概览")
            stats_layout = QGridLayout(comprehensive_stats)

            # 添加各种统计数据
            stats_data = [
                ("商品总数:", "25个", "#4CAF50"),
                ("库存价值:", "¥15,680.50", "#2196F3"),
                ("月度销售:", "¥12,340.00", "#FF9800"),
                ("利润率:", "27.1%", "#9C27B0"),
            ]

            for i, (label, value, color) in enumerate(stats_data):
                row = i // 2
                col = (i % 2) * 2

                label_widget = QLabel(label)
                label_widget.setStyleSheet("font-weight: bold; color: #cccccc;")
                stats_layout.addWidget(label_widget, row, col)

                value_widget = QLabel(value)
                value_widget.setStyleSheet(
                    f"font-weight: bold; color: {color}; font-size: 14px;"
                )
                stats_layout.addWidget(value_widget, row, col + 1)

            layout.addWidget(comprehensive_stats)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示综合报表失败: {e}")

    def refresh_cards_data(self):
        """刷新卡片数据"""
        try:
            if self.cards_layout is None:
                return

            # 清除现有卡片
            for i in reversed(range(self.cards_layout.count())):
                child = self.cards_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            # 重新创建卡片
            cards_data = self.get_real_cards_data()
            for i, (icon, title, value, color, trend) in enumerate(cards_data):
                card = self.create_modern_card(icon, title, value, color, trend)
                row = i // 4
                col = i % 4
                self.cards_layout.addWidget(card, row, col)

            self.logger.info("卡片数据刷新完成")

        except Exception as e:
            self.logger.error(f"刷新卡片数据失败: {e}")

    def refresh_data(self):
        """刷新所有数据"""
        try:
            self.refresh_cards_data()
            # 这里可以添加其他数据的刷新逻辑
            self.logger.info("数据刷新完成")
        except Exception as e:
            self.logger.error(f"数据刷新失败: {e}")
            from PyQt6.QtWidgets import QMessageBox

            QMessageBox.critical(self, "错误", f"显示综合报表失败:\n{e}")
