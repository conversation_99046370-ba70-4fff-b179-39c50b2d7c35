#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应链对比管理器

负责对比组和对比项目的CRUD操作、价格分析等业务逻辑。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from decimal import Decimal
import json

from core.database import DatabaseManager
from core.models.comparison import ComparisonGroup, ComparisonItem
from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class ComparisonManager(LoggerMixin):
    """供应链对比管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        初始化对比管理器
        
        Args:
            db_manager: 数据库管理器
        """
        self.db_manager = db_manager
        self.logger.info("供应链对比管理器初始化完成")
    
    @handle_errors(default_return=False)
    def create_comparison_group(self, group: ComparisonGroup) -> bool:
        """
        创建对比组
        
        Args:
            group: 对比组对象
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 验证对比组数据
            group.validate()
            
            # 检查对比组名称是否重复
            if self.get_comparison_group_by_name(group.group_name):
                raise ValueError(f"对比组名称 {group.group_name} 已存在")
            
            # 插入对比组基础信息
            sql = """
            INSERT INTO comparison_groups (
                group_id, group_name, description, category, status,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            data = group.to_dict()
            params = (
                data['group_id'], data['group_name'], data['description'],
                data['category'], data['status'], data['created_at'], data['updated_at']
            )
            
            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"对比组创建失败: {group.group_name}")
                return False
            
            # 插入对比项目
            if group.items:
                for item in group.items:
                    if not self._add_comparison_item_to_db(group.group_id, item):
                        self.logger.error(f"添加对比项目失败: {item.item_id}")
                        return False
            
            self.logger.info(f"对比组创建成功: {group.group_id} - {group.group_name}")
            return True
                
        except Exception as e:
            self.logger.error(f"创建对比组时出错: {e}")
            return False
    
    def _add_comparison_item_to_db(self, group_id: str, item: ComparisonItem) -> bool:
        """添加对比项目到数据库"""
        try:
            sql = """
            INSERT INTO comparison_items (
                item_id, group_id, product_name, source_platform, source_label,
                source_url, price, original_price, stock, min_order_quantity,
                shipping_fee, free_shipping_threshold, image_url, specifications,
                supplier_info, last_updated, status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            data = item.to_dict()
            params = (
                data['item_id'], group_id, data['product_name'],
                data['source_platform'], data['source_label'], data['source_url'],
                data['price'], data['original_price'], data['stock'],
                data['min_order_quantity'], data['shipping_fee'],
                data['free_shipping_threshold'], data['image_url'],
                data['specifications'], data['supplier_info'],
                data['last_updated'], data['status'], data['notes']
            )
            
            cursor = self.db_manager.execute(sql, params)
            return cursor is not None
            
        except Exception as e:
            self.logger.error(f"添加对比项目到数据库失败: {e}")
            return False
    
    @handle_errors(default_return=None)
    def get_comparison_group(self, group_id: str) -> Optional[ComparisonGroup]:
        """
        获取对比组
        
        Args:
            group_id: 对比组ID
            
        Returns:
            ComparisonGroup: 对比组对象
        """
        # 获取对比组基础信息
        sql = "SELECT * FROM comparison_groups WHERE group_id = ?"
        row = self.db_manager.fetch_one(sql, (group_id,))
        
        if not row:
            return None
        
        # 构建对比组对象
        columns = [description[0] for description in self.db_manager.cursor.description]
        group_data = dict(zip(columns, row))
        
        # 获取对比项目
        comparison_items = self._get_comparison_items(group_id)
        group_data['items'] = comparison_items
        
        return ComparisonGroup.from_dict(group_data)
    
    def _get_comparison_items(self, group_id: str) -> List[ComparisonItem]:
        """获取对比项目列表"""
        sql = """
        SELECT item_id, product_name, source_platform, source_label, source_url,
               price, original_price, stock, min_order_quantity, shipping_fee,
               free_shipping_threshold, image_url, specifications, supplier_info,
               last_updated, status, notes
        FROM comparison_items 
        WHERE group_id = ?
        ORDER BY price ASC
        """
        
        rows = self.db_manager.fetch_all(sql, (group_id,))
        items = []
        
        if rows:
            for row in rows:
                item_data = {
                    'item_id': row[0],
                    'product_name': row[1],
                    'source_platform': row[2],
                    'source_label': row[3],
                    'source_url': row[4],
                    'price': row[5],
                    'original_price': row[6],
                    'stock': row[7],
                    'min_order_quantity': row[8],
                    'shipping_fee': row[9],
                    'free_shipping_threshold': row[10],
                    'image_url': row[11],
                    'specifications': row[12],
                    'supplier_info': row[13],
                    'last_updated': row[14],
                    'status': row[15],
                    'notes': row[16]
                }
                item = ComparisonItem.from_dict(item_data)
                items.append(item)
        
        return items
    
    @handle_errors(default_return=None)
    def get_comparison_group_by_name(self, group_name: str) -> Optional[ComparisonGroup]:
        """
        根据对比组名称获取对比组
        
        Args:
            group_name: 对比组名称
            
        Returns:
            ComparisonGroup: 对比组对象
        """
        sql = "SELECT * FROM comparison_groups WHERE group_name = ?"
        row = self.db_manager.fetch_one(sql, (group_name,))
        
        if not row:
            return None
        
        columns = [description[0] for description in self.db_manager.cursor.description]
        group_data = dict(zip(columns, row))
        
        # 获取对比项目
        comparison_items = self._get_comparison_items(group_data['group_id'])
        group_data['items'] = comparison_items
        
        return ComparisonGroup.from_dict(group_data)
    
    @handle_errors(default_return=[])
    def get_comparison_groups(self, category: Optional[str] = None, status: Optional[str] = None,
                            limit: int = 100, offset: int = 0) -> List[ComparisonGroup]:
        """
        获取对比组列表
        
        Args:
            category: 分类
            status: 状态
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[ComparisonGroup]: 对比组列表
        """
        sql = "SELECT * FROM comparison_groups WHERE 1=1"
        params = []
        
        if category:
            sql += " AND category = ?"
            params.append(category)
        
        if status:
            sql += " AND status = ?"
            params.append(status)
        
        sql += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        rows = self.db_manager.fetch_all(sql, tuple(params))
        groups = []
        
        if rows:
            columns = [description[0] for description in self.db_manager.cursor.description]
            for row in rows:
                group_data = dict(zip(columns, row))
                
                # 获取对比项目
                comparison_items = self._get_comparison_items(group_data['group_id'])
                group_data['items'] = comparison_items
                
                group = ComparisonGroup.from_dict(group_data)
                groups.append(group)
        
        return groups
    
    @handle_errors(default_return=[])
    def search_comparison_groups(self, keyword: str, limit: int = 100) -> List[ComparisonGroup]:
        """
        搜索对比组
        
        Args:
            keyword: 搜索关键词
            limit: 限制数量
            
        Returns:
            List[ComparisonGroup]: 对比组列表
        """
        sql = """
        SELECT * FROM comparison_groups 
        WHERE group_name LIKE ? OR description LIKE ? OR category LIKE ?
        ORDER BY created_at DESC LIMIT ?
        """
        
        search_term = f"%{keyword}%"
        params = (search_term, search_term, search_term, limit)
        
        rows = self.db_manager.fetch_all(sql, params)
        groups = []
        
        if rows:
            columns = [description[0] for description in self.db_manager.cursor.description]
            for row in rows:
                group_data = dict(zip(columns, row))
                
                # 获取对比项目
                comparison_items = self._get_comparison_items(group_data['group_id'])
                group_data['items'] = comparison_items
                
                group = ComparisonGroup.from_dict(group_data)
                groups.append(group)
        
        return groups
    
    @handle_errors(default_return=False)
    def add_comparison_item(self, group_id: str, item: ComparisonItem) -> bool:
        """
        添加对比项目到对比组
        
        Args:
            group_id: 对比组ID
            item: 对比项目
            
        Returns:
            bool: 添加是否成功
        """
        try:
            group = self.get_comparison_group(group_id)
            if not group:
                self.logger.error(f"对比组不存在: {group_id}")
                return False
            
            # 添加项目到对比组
            group.add_item(item)
            
            # 保存到数据库
            return self.update_comparison_group(group)
            
        except Exception as e:
            self.logger.error(f"添加对比项目时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def remove_comparison_item(self, group_id: str, item_id: str) -> bool:
        """
        从对比组中移除对比项目
        
        Args:
            group_id: 对比组ID
            item_id: 对比项目ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            group = self.get_comparison_group(group_id)
            if not group:
                self.logger.error(f"对比组不存在: {group_id}")
                return False
            
            # 从对比组中移除项目
            if group.remove_item(item_id):
                # 保存到数据库
                return self.update_comparison_group(group)
            else:
                self.logger.warning(f"对比项目不在对比组中: {item_id}")
                return False
            
        except Exception as e:
            self.logger.error(f"移除对比项目时出错: {e}")
            return False
    
    @handle_errors(default_return=False)
    def update_comparison_group(self, group: ComparisonGroup) -> bool:
        """
        更新对比组
        
        Args:
            group: 对比组对象
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证对比组数据
            group.validate()
            group.updated_at = datetime.now()
            
            # 更新对比组基础信息
            sql = """
            UPDATE comparison_groups SET
                group_name = ?, description = ?, category = ?, status = ?, updated_at = ?
            WHERE group_id = ?
            """
            
            data = group.to_dict()
            params = (
                data['group_name'], data['description'], data['category'],
                data['status'], data['updated_at'], data['group_id']
            )
            
            cursor = self.db_manager.execute(sql, params)
            if not cursor:
                self.logger.error(f"对比组更新失败: {group.group_name}")
                return False
            
            # 更新对比项目（先删除再重新插入）
            self._delete_comparison_items(group.group_id)
            
            if group.items:
                for item in group.items:
                    if not self._add_comparison_item_to_db(group.group_id, item):
                        self.logger.error(f"更新对比项目失败: {item.item_id}")
                        return False
            
            self.logger.info(f"对比组更新成功: {group.group_id} - {group.group_name}")
            return True
                
        except Exception as e:
            self.logger.error(f"更新对比组时出错: {e}")
            return False
    
    def _delete_comparison_items(self, group_id: str) -> bool:
        """删除对比项目"""
        try:
            sql = "DELETE FROM comparison_items WHERE group_id = ?"
            cursor = self.db_manager.execute(sql, (group_id,))
            return cursor is not None
        except Exception as e:
            self.logger.error(f"删除对比项目失败: {e}")
            return False
    
    @handle_errors(default_return=False)
    def delete_comparison_group(self, group_id: str) -> bool:
        """
        删除对比组
        
        Args:
            group_id: 对比组ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 检查对比组是否存在
            group = self.get_comparison_group(group_id)
            if not group:
                self.logger.warning(f"要删除的对比组不存在: {group_id}")
                return False
            
            # 删除对比项目
            self._delete_comparison_items(group_id)
            
            # 删除对比组
            sql = "DELETE FROM comparison_groups WHERE group_id = ?"
            cursor = self.db_manager.execute(sql, (group_id,))
            
            if cursor:
                self.logger.info(f"对比组删除成功: {group_id} - {group.group_name}")
                return True
            else:
                self.logger.error(f"对比组删除失败: {group_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除对比组时出错: {e}")
            return False
    
    @handle_errors(default_return={})
    def get_price_analysis(self, group_id: str) -> Dict[str, Any]:
        """
        获取价格分析
        
        Args:
            group_id: 对比组ID
            
        Returns:
            Dict[str, Any]: 价格分析结果
        """
        try:
            group = self.get_comparison_group(group_id)
            if not group:
                return {}
            
            return group.get_price_comparison()
            
        except Exception as e:
            self.logger.error(f"获取价格分析时出错: {e}")
            return {}
    
    @handle_errors(default_return=False)
    def update_item_price(self, group_id: str, item_id: str, 
                         new_price: Decimal, reason: str = "") -> bool:
        """
        更新对比项目价格
        
        Args:
            group_id: 对比组ID
            item_id: 对比项目ID
            new_price: 新价格
            reason: 更新原因
            
        Returns:
            bool: 更新是否成功
        """
        try:
            group = self.get_comparison_group(group_id)
            if not group:
                self.logger.error(f"对比组不存在: {group_id}")
                return False
            
            item = group.get_item(item_id)
            if not item:
                self.logger.error(f"对比项目不存在: {item_id}")
                return False
            
            # 更新价格
            item.update_price(new_price, reason)
            
            # 更新统计信息
            group.update_statistics()
            
            # 保存到数据库
            return self.update_comparison_group(group)
            
        except Exception as e:
            self.logger.error(f"更新对比项目价格时出错: {e}")
            return False
    
    @handle_errors(default_return=0)
    def get_comparison_group_count(self, category: Optional[str] = None, 
                                  status: Optional[str] = None) -> int:
        """
        获取对比组数量
        
        Args:
            category: 分类
            status: 状态
            
        Returns:
            int: 对比组数量
        """
        sql = "SELECT COUNT(*) FROM comparison_groups WHERE 1=1"
        params = []
        
        if category:
            sql += " AND category = ?"
            params.append(category)
        
        if status:
            sql += " AND status = ?"
            params.append(status)
        
        result = self.db_manager.fetch_one(sql, tuple(params))
        return result[0] if result else 0
    
    @handle_errors(default_return={})
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取对比分析统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            'total_groups': 0,
            'active_groups': 0,
            'archived_groups': 0,
            'total_items': 0,
            'avg_items_per_group': 0.0,
            'price_range_analysis': {}
        }
        
        # 总对比组数
        stats['total_groups'] = self.get_comparison_group_count()
        
        # 按状态统计
        stats['active_groups'] = self.get_comparison_group_count(status="active")
        stats['archived_groups'] = self.get_comparison_group_count(status="archived")
        
        # 总对比项目数
        sql = "SELECT COUNT(*) FROM comparison_items"
        result = self.db_manager.fetch_one(sql)
        if result:
            stats['total_items'] = result[0]
        
        # 平均每组项目数
        if stats['total_groups'] > 0:
            stats['avg_items_per_group'] = stats['total_items'] / stats['total_groups']
        
        # 价格范围分析
        sql = """
        SELECT MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price
        FROM comparison_items WHERE status = 'active'
        """
        result = self.db_manager.fetch_one(sql)
        if result and result[0] is not None:
            stats['price_range_analysis'] = {
                'min_price': float(result[0]),
                'max_price': float(result[1]),
                'avg_price': float(result[2])
            }
        
        return stats
