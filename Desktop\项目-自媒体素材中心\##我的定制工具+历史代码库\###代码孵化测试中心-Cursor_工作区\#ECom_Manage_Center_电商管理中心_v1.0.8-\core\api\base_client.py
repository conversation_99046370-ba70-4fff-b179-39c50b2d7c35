#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础API客户端

提供所有平台API客户端的基础功能和接口定义。
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import requests
from datetime import datetime, timedelta
import json

from utils.logger import LoggerMixin
from utils.error_handler import handle_errors


class APIRateLimiter:
    """API调用频率限制器"""
    
    def __init__(self, max_calls_per_hour: int = 1000):
        self.max_calls_per_hour = max_calls_per_hour
        self.calls_history = []
    
    def can_make_call(self) -> bool:
        """检查是否可以进行API调用"""
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)
        
        # 清理一小时前的记录
        self.calls_history = [call_time for call_time in self.calls_history if call_time > one_hour_ago]
        
        return len(self.calls_history) < self.max_calls_per_hour
    
    def record_call(self):
        """记录API调用"""
        self.calls_history.append(datetime.now())
    
    def wait_time_until_next_call(self) -> int:
        """计算到下次可调用的等待时间（秒）"""
        if self.can_make_call():
            return 0
        
        # 找到最早的调用时间
        if self.calls_history:
            earliest_call = min(self.calls_history)
            wait_until = earliest_call + timedelta(hours=1)
            wait_seconds = (wait_until - datetime.now()).total_seconds()
            return max(0, int(wait_seconds))
        
        return 0


class BaseAPIClient(ABC, LoggerMixin):
    """基础API客户端抽象类"""
    
    def __init__(self, platform_type: str, api_config: Dict[str, Any]):
        """
        初始化API客户端
        
        Args:
            platform_type: 平台类型 (taobao, xiaohongshu, douyin, 1688)
            api_config: API配置信息
        """
        self.platform_type = platform_type
        self.api_config = api_config
        self.session = requests.Session()
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'MultiPlatform-Inventory-Manager/2.1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        
        # 初始化频率限制器
        self.rate_limiter = APIRateLimiter(
            max_calls_per_hour=api_config.get('rate_limit', 1000)
        )
        
        self.logger.info(f"{platform_type} API客户端初始化完成")
    
    @abstractmethod
    def authenticate(self) -> bool:
        """
        进行API认证
        
        Returns:
            bool: 认证是否成功
        """
        pass
    
    @abstractmethod
    def get_orders(self, start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None,
                   status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取订单列表
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            status: 订单状态
            
        Returns:
            List[Dict[str, Any]]: 订单列表
        """
        pass
    
    @abstractmethod
    def get_order_details(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        获取订单详情
        
        Args:
            order_id: 订单ID
            
        Returns:
            Optional[Dict[str, Any]]: 订单详情
        """
        pass
    
    @abstractmethod
    def update_inventory(self, product_id: str, quantity: int) -> bool:
        """
        更新商品库存
        
        Args:
            product_id: 平台商品ID
            quantity: 库存数量
            
        Returns:
            bool: 更新是否成功
        """
        pass
    
    @abstractmethod
    def get_product_info(self, product_id: str) -> Optional[Dict[str, Any]]:
        """
        获取商品信息
        
        Args:
            product_id: 平台商品ID
            
        Returns:
            Optional[Dict[str, Any]]: 商品信息
        """
        pass
    
    @handle_errors(default_return=None)
    def make_request(self, method: str, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        发起API请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            Optional[Dict[str, Any]]: 响应数据
        """
        # 检查频率限制
        if not self.rate_limiter.can_make_call():
            wait_time = self.rate_limiter.wait_time_until_next_call()
            self.logger.warning(f"API调用频率限制，需等待 {wait_time} 秒")
            if wait_time > 300:  # 如果等待时间超过5分钟，直接返回
                return None
            time.sleep(wait_time)
        
        start_time = time.time()
        
        try:
            # 记录API调用
            self.rate_limiter.record_call()
            
            # 发起请求
            response = self.session.request(method, url, **kwargs)
            
            # 计算响应时间
            response_time = int((time.time() - start_time) * 1000)
            
            # 记录API调用统计
            self._log_api_call(url, method, response.status_code, response_time)
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            if response.content:
                return response.json()
            else:
                return {}
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败: {method} {url}, 错误: {e}")
            response_time = int((time.time() - start_time) * 1000)
            self._log_api_call(url, method, 0, response_time, str(e))
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"API响应解析失败: {e}")
            return None
    
    def _log_api_call(self, url: str, method: str, status_code: int, 
                     response_time: int, error_message: Optional[str] = None):
        """
        记录API调用日志
        
        Args:
            url: 请求URL
            method: HTTP方法
            status_code: 响应状态码
            response_time: 响应时间(毫秒)
            error_message: 错误信息
        """
        # 这里可以记录到数据库的api_logs表
        log_data = {
            'platform_type': self.platform_type,
            'api_endpoint': url,
            'http_method': method,
            'response_code': status_code,
            'response_time': response_time,
            'error_message': error_message,
            'created_at': datetime.now().isoformat()
        }
        
        self.logger.debug(f"API调用记录: {log_data}")
    
    def get_platform_type(self) -> str:
        """获取平台类型"""
        return self.platform_type
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        # 子类可以重写此方法
        return True
    
    def close(self):
        """关闭客户端连接"""
        if self.session:
            self.session.close()
            self.logger.info(f"{self.platform_type} API客户端连接已关闭")
