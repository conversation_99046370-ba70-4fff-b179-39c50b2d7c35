#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品表单对话框

用于添加和编辑商品的对话框界面。
"""

import logging
from typing import Optional
from decimal import Decimal
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QLabel, QGroupBox, QFileDialog, QMessageBox,
    QCheckBox, QListWidget, QListWidgetItem, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon

from core.models.product import Product
from utils.logger import LoggerMixin


class ProductForm(QDialog, LoggerMixin):
    """商品表单对话框"""
    
    # 信号定义
    product_saved = pyqtSignal(Product)
    
    def __init__(self, product: Optional[Product] = None, categories: list = None, parent=None):
        """
        初始化商品表单
        
        Args:
            product: 要编辑的商品对象，None表示新建
            categories: 商品分类列表
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.product = product
        self.categories = categories or []
        self.is_edit_mode = product is not None
        
        # 初始化界面
        self.init_ui()
        
        # 如果是编辑模式，加载商品数据
        if self.is_edit_mode:
            self.load_product_data()
        
        self.logger.info(f"商品表单初始化完成 - {'编辑' if self.is_edit_mode else '新建'}模式")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("编辑商品" if self.is_edit_mode else "添加商品")
        self.setModal(True)
        self.resize(600, 700)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基础信息页面
        self.basic_tab = self.create_basic_tab()
        self.tab_widget.addTab(self.basic_tab, "📋 基础信息")
        
        # 价格信息页面
        self.price_tab = self.create_price_tab()
        self.tab_widget.addTab(self.price_tab, "💰 价格信息")
        
        # 供应商信息页面
        self.supplier_tab = self.create_supplier_tab()
        self.tab_widget.addTab(self.supplier_tab, "🏪 供应商信息")
        
        # 扩展信息页面
        self.extended_tab = self.create_extended_tab()
        self.tab_widget.addTab(self.extended_tab, "📝 扩展信息")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.clicked.connect(self.save_product)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_basic_tab(self) -> QFrame:
        """创建基础信息页面"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # 基础信息组
        basic_group = QGroupBox("基础信息")
        basic_form = QFormLayout(basic_group)
        
        # 商品名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入商品名称")
        basic_form.addRow("商品名称 *:", self.name_edit)
        
        # 商品编码
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("商品编码（可选）")
        basic_form.addRow("商品编码:", self.code_edit)
        
        # 商品分类
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.addItems(self.categories)
        basic_form.addRow("商品分类 *:", self.category_combo)
        
        # 商品描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("商品描述（可选）")
        basic_form.addRow("商品描述:", self.description_edit)
        
        layout.addWidget(basic_group)
        
        # 库存信息组
        stock_group = QGroupBox("库存信息")
        stock_form = QFormLayout(stock_group)
        
        # 库存数量
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(0, 999999)
        self.quantity_spin.setValue(0)
        stock_form.addRow("库存数量:", self.quantity_spin)
        
        # 计量单位
        self.unit_combo = QComboBox()
        self.unit_combo.setEditable(True)
        self.unit_combo.addItems(["个", "件", "套", "盒", "包", "瓶", "袋", "箱", "台", "张"])
        stock_form.addRow("计量单位:", self.unit_combo)
        
        # 商品状态
        self.status_combo = QComboBox()
        self.status_combo.addItems(["在库", "缺货", "停售", "预售"])
        stock_form.addRow("商品状态:", self.status_combo)
        
        # 存放位置
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("存放位置（可选）")
        stock_form.addRow("存放位置:", self.location_edit)
        
        layout.addWidget(stock_group)
        layout.addStretch()
        
        return frame
    
    def create_price_tab(self) -> QFrame:
        """创建价格信息页面"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # 成本信息组
        cost_group = QGroupBox("成本信息")
        cost_form = QFormLayout(cost_group)
        
        # 采购价
        self.purchase_price_spin = QDoubleSpinBox()
        self.purchase_price_spin.setRange(0, 999999.99)
        self.purchase_price_spin.setDecimals(2)
        self.purchase_price_spin.setSuffix(" 元")
        cost_form.addRow("采购价:", self.purchase_price_spin)
        
        # 运费
        self.shipping_cost_spin = QDoubleSpinBox()
        self.shipping_cost_spin.setRange(0, 999999.99)
        self.shipping_cost_spin.setDecimals(2)
        self.shipping_cost_spin.setSuffix(" 元")
        cost_form.addRow("运费:", self.shipping_cost_spin)
        
        # 其他成本
        self.other_cost_spin = QDoubleSpinBox()
        self.other_cost_spin.setRange(0, 999999.99)
        self.other_cost_spin.setDecimals(2)
        self.other_cost_spin.setSuffix(" 元")
        cost_form.addRow("其他成本:", self.other_cost_spin)
        
        # 总成本（自动计算）
        self.total_cost_label = QLabel("0.00 元")
        self.total_cost_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        cost_form.addRow("总成本:", self.total_cost_label)
        
        layout.addWidget(cost_group)
        
        # 销售信息组
        sale_group = QGroupBox("销售信息")
        sale_form = QFormLayout(sale_group)
        
        # 销售价
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0, 999999.99)
        self.selling_price_spin.setDecimals(2)
        self.selling_price_spin.setSuffix(" 元")
        sale_form.addRow("销售价:", self.selling_price_spin)
        
        # 折扣率
        self.discount_rate_spin = QDoubleSpinBox()
        self.discount_rate_spin.setRange(0, 100)
        self.discount_rate_spin.setDecimals(1)
        self.discount_rate_spin.setValue(100)
        self.discount_rate_spin.setSuffix(" %")
        sale_form.addRow("折扣率:", self.discount_rate_spin)
        
        # 折后价（自动计算）
        self.discounted_price_label = QLabel("0.00 元")
        self.discounted_price_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        sale_form.addRow("折后价:", self.discounted_price_label)
        
        # 预估利润（自动计算）
        self.profit_label = QLabel("0.00 元")
        self.profit_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        sale_form.addRow("预估利润:", self.profit_label)
        
        # 利润率（自动计算）
        self.profit_margin_label = QLabel("0.0 %")
        self.profit_margin_label.setStyleSheet("font-weight: bold; color: #9C27B0;")
        sale_form.addRow("利润率:", self.profit_margin_label)
        
        layout.addWidget(sale_group)
        
        # 连接价格变化信号
        self.purchase_price_spin.valueChanged.connect(self.calculate_profit)
        self.shipping_cost_spin.valueChanged.connect(self.calculate_profit)
        self.other_cost_spin.valueChanged.connect(self.calculate_profit)
        self.selling_price_spin.valueChanged.connect(self.calculate_profit)
        self.discount_rate_spin.valueChanged.connect(self.calculate_profit)
        
        layout.addStretch()
        
        return frame
    
    def create_supplier_tab(self) -> QFrame:
        """创建供应商信息页面"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # 供应商信息组
        supplier_group = QGroupBox("供应商信息")
        supplier_form = QFormLayout(supplier_group)
        
        # 供应商名称
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("供应商名称（可选）")
        supplier_form.addRow("供应商:", self.supplier_edit)
        
        # 供应商链接
        self.supplier_link_edit = QLineEdit()
        self.supplier_link_edit.setPlaceholderText("供应商链接（可选）")
        supplier_form.addRow("供应商链接:", self.supplier_link_edit)
        
        # 采购链接
        self.purchase_link_edit = QLineEdit()
        self.purchase_link_edit.setPlaceholderText("采购链接（可选）")
        supplier_form.addRow("采购链接:", self.purchase_link_edit)
        
        # 销售链接
        self.selling_link_edit = QLineEdit()
        self.selling_link_edit.setPlaceholderText("销售链接（可选）")
        supplier_form.addRow("销售链接:", self.selling_link_edit)
        
        # 采购员
        self.purchaser_edit = QLineEdit()
        self.purchaser_edit.setPlaceholderText("采购员（可选）")
        supplier_form.addRow("采购员:", self.purchaser_edit)
        
        layout.addWidget(supplier_group)
        
        # 图片管理组
        image_group = QGroupBox("图片管理")
        image_layout = QVBoxLayout(image_group)
        
        # 主图片
        image_form = QFormLayout()
        
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("主图片路径")
        self.image_path_edit.setReadOnly(True)
        
        self.browse_image_btn = QPushButton("📁 浏览")
        self.browse_image_btn.clicked.connect(self.browse_image)
        
        image_row_layout = QHBoxLayout()
        image_row_layout.addWidget(self.image_path_edit)
        image_row_layout.addWidget(self.browse_image_btn)
        
        image_form.addRow("主图片:", image_row_layout)
        image_layout.addLayout(image_form)
        
        # 图片预览
        self.image_preview = QLabel()
        self.image_preview.setFixedSize(150, 150)
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 5px;
                background-color: #f9f9f9;
                color: #666;
            }
        """)
        self.image_preview.setText("图片预览")
        image_layout.addWidget(self.image_preview)
        
        layout.addWidget(image_group)
        layout.addStretch()
        
        return frame
    
    def create_extended_tab(self) -> QFrame:
        """创建扩展信息页面"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        
        # 商品属性组
        attr_group = QGroupBox("商品属性")
        attr_form = QFormLayout(attr_group)
        
        # 品牌
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("品牌（可选）")
        attr_form.addRow("品牌:", self.brand_edit)
        
        # 型号
        self.model_edit = QLineEdit()
        self.model_edit.setPlaceholderText("型号（可选）")
        attr_form.addRow("型号:", self.model_edit)
        
        # 颜色
        self.color_edit = QLineEdit()
        self.color_edit.setPlaceholderText("颜色（可选）")
        attr_form.addRow("颜色:", self.color_edit)
        
        # 尺寸
        self.size_edit = QLineEdit()
        self.size_edit.setPlaceholderText("尺寸（可选）")
        attr_form.addRow("尺寸:", self.size_edit)
        
        # 重量
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(0, 999999.99)
        self.weight_spin.setDecimals(2)
        self.weight_spin.setSuffix(" kg")
        attr_form.addRow("重量:", self.weight_spin)
        
        layout.addWidget(attr_group)
        
        # 标签管理组
        tags_group = QGroupBox("标签管理")
        tags_layout = QVBoxLayout(tags_group)
        
        # 标签输入
        tag_input_layout = QHBoxLayout()
        self.tag_edit = QLineEdit()
        self.tag_edit.setPlaceholderText("输入标签后按回车添加")
        self.tag_edit.returnPressed.connect(self.add_tag)
        
        self.add_tag_btn = QPushButton("➕ 添加")
        self.add_tag_btn.clicked.connect(self.add_tag)
        
        tag_input_layout.addWidget(self.tag_edit)
        tag_input_layout.addWidget(self.add_tag_btn)
        tags_layout.addLayout(tag_input_layout)
        
        # 标签列表
        self.tags_list = QListWidget()
        self.tags_list.setMaximumHeight(100)
        tags_layout.addWidget(self.tags_list)
        
        # 删除标签按钮
        self.remove_tag_btn = QPushButton("🗑️ 删除选中标签")
        self.remove_tag_btn.clicked.connect(self.remove_tag)
        tags_layout.addWidget(self.remove_tag_btn)
        
        layout.addWidget(tags_group)
        
        # 备注信息组
        remarks_group = QGroupBox("备注信息")
        remarks_layout = QVBoxLayout(remarks_group)
        
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(100)
        self.remarks_edit.setPlaceholderText("备注信息（可选）")
        remarks_layout.addWidget(self.remarks_edit)
        
        layout.addWidget(remarks_group)
        layout.addStretch()
        
        return frame
    
    def calculate_profit(self):
        """计算利润和利润率"""
        try:
            # 获取价格数据
            purchase_price = Decimal(str(self.purchase_price_spin.value()))
            shipping_cost = Decimal(str(self.shipping_cost_spin.value()))
            other_cost = Decimal(str(self.other_cost_spin.value()))
            selling_price = Decimal(str(self.selling_price_spin.value()))
            discount_rate = Decimal(str(self.discount_rate_spin.value()))
            
            # 计算总成本
            total_cost = purchase_price + shipping_cost + other_cost
            self.total_cost_label.setText(f"{total_cost:.2f} 元")
            
            # 计算折后价
            discounted_price = selling_price * (discount_rate / Decimal('100'))
            self.discounted_price_label.setText(f"{discounted_price:.2f} 元")
            
            # 计算利润
            profit = discounted_price - total_cost
            self.profit_label.setText(f"{profit:.2f} 元")
            
            # 设置利润颜色
            if profit > 0:
                self.profit_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
            elif profit < 0:
                self.profit_label.setStyleSheet("font-weight: bold; color: #F44336;")
            else:
                self.profit_label.setStyleSheet("font-weight: bold; color: #FF9800;")
            
            # 计算利润率
            if discounted_price > 0:
                profit_margin = (profit / discounted_price) * Decimal('100')
                self.profit_margin_label.setText(f"{profit_margin:.1f} %")
                
                # 设置利润率颜色
                if profit_margin > 20:
                    self.profit_margin_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
                elif profit_margin > 10:
                    self.profit_margin_label.setStyleSheet("font-weight: bold; color: #FF9800;")
                else:
                    self.profit_margin_label.setStyleSheet("font-weight: bold; color: #F44336;")
            else:
                self.profit_margin_label.setText("0.0 %")
                self.profit_margin_label.setStyleSheet("font-weight: bold; color: #666;")
                
        except Exception as e:
            self.logger.error(f"计算利润时出错: {e}")
    
    def browse_image(self):
        """浏览图片文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择商品图片",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp);;所有文件 (*)"
        )
        
        if file_path:
            self.image_path_edit.setText(file_path)
            self.load_image_preview(file_path)
    
    def load_image_preview(self, image_path: str):
        """加载图片预览"""
        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                # 缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    self.image_preview.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.image_preview.setPixmap(scaled_pixmap)
            else:
                self.image_preview.setText("图片加载失败")
        except Exception as e:
            self.logger.error(f"加载图片预览失败: {e}")
            self.image_preview.setText("图片加载失败")
    
    def add_tag(self):
        """添加标签"""
        tag_text = self.tag_edit.text().strip()
        if tag_text:
            # 检查标签是否已存在
            for i in range(self.tags_list.count()):
                if self.tags_list.item(i).text() == tag_text:
                    QMessageBox.warning(self, "警告", "标签已存在")
                    return
            
            # 添加标签
            self.tags_list.addItem(tag_text)
            self.tag_edit.clear()
    
    def remove_tag(self):
        """删除选中的标签"""
        current_row = self.tags_list.currentRow()
        if current_row >= 0:
            self.tags_list.takeItem(current_row)
    
    def load_product_data(self):
        """加载商品数据到表单"""
        if not self.product:
            return
        
        try:
            # 基础信息
            self.name_edit.setText(self.product.name)
            self.code_edit.setText(self.product.code or "")
            self.category_combo.setCurrentText(self.product.category)
            self.description_edit.setPlainText(self.product.description)
            
            # 库存信息
            self.quantity_spin.setValue(self.product.quantity)
            self.unit_combo.setCurrentText(self.product.unit)
            self.status_combo.setCurrentText(self.product.status)
            self.location_edit.setText(self.product.location or "")
            
            # 价格信息
            self.purchase_price_spin.setValue(float(self.product.purchase_price))
            self.shipping_cost_spin.setValue(float(self.product.shipping_cost))
            self.other_cost_spin.setValue(float(self.product.other_cost))
            self.selling_price_spin.setValue(float(self.product.selling_price))
            self.discount_rate_spin.setValue(float(self.product.discount_rate))
            
            # 供应商信息
            self.supplier_edit.setText(self.product.supplier or "")
            self.supplier_link_edit.setText(self.product.supplier_link or "")
            self.purchase_link_edit.setText(self.product.purchase_link or "")
            self.selling_link_edit.setText(self.product.selling_link or "")
            self.purchaser_edit.setText(self.product.purchaser or "")
            
            # 图片
            if self.product.image_path:
                self.image_path_edit.setText(self.product.image_path)
                self.load_image_preview(self.product.image_path)
            
            # 扩展信息
            self.brand_edit.setText(self.product.brand or "")
            self.model_edit.setText(self.product.model or "")
            self.color_edit.setText(self.product.color or "")
            self.size_edit.setText(self.product.size or "")
            if self.product.weight:
                self.weight_spin.setValue(float(self.product.weight))
            
            # 标签
            for tag in self.product.tags:
                self.tags_list.addItem(tag)
            
            # 备注
            self.remarks_edit.setPlainText(self.product.remarks or "")
            
            # 计算利润
            self.calculate_profit()
            
        except Exception as e:
            self.logger.error(f"加载商品数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载商品数据失败:\n{e}")
    
    def save_product(self):
        """保存商品"""
        try:
            # 验证必填字段
            if not self.name_edit.text().strip():
                QMessageBox.warning(self, "警告", "商品名称不能为空")
                self.tab_widget.setCurrentIndex(0)
                self.name_edit.setFocus()
                return
            
            if not self.category_combo.currentText().strip():
                QMessageBox.warning(self, "警告", "商品分类不能为空")
                self.tab_widget.setCurrentIndex(0)
                self.category_combo.setFocus()
                return
            
            # 收集标签
            tags = []
            for i in range(self.tags_list.count()):
                tags.append(self.tags_list.item(i).text())
            
            # 创建或更新商品对象
            if self.is_edit_mode:
                product = self.product
            else:
                product = Product()
            
            # 设置商品属性
            product.name = self.name_edit.text().strip()
            product.code = self.code_edit.text().strip() or None
            product.category = self.category_combo.currentText().strip()
            product.description = self.description_edit.toPlainText().strip()
            
            product.quantity = self.quantity_spin.value()
            product.unit = self.unit_combo.currentText()
            product.status = self.status_combo.currentText()
            product.location = self.location_edit.text().strip() or None
            
            product.purchase_price = Decimal(str(self.purchase_price_spin.value()))
            product.shipping_cost = Decimal(str(self.shipping_cost_spin.value()))
            product.other_cost = Decimal(str(self.other_cost_spin.value()))
            product.selling_price = Decimal(str(self.selling_price_spin.value()))
            product.discount_rate = Decimal(str(self.discount_rate_spin.value()))
            
            product.supplier = self.supplier_edit.text().strip() or None
            product.supplier_link = self.supplier_link_edit.text().strip() or None
            product.purchase_link = self.purchase_link_edit.text().strip() or None
            product.selling_link = self.selling_link_edit.text().strip() or None
            product.purchaser = self.purchaser_edit.text().strip() or None
            
            product.image_path = self.image_path_edit.text().strip() or None
            
            product.brand = self.brand_edit.text().strip() or None
            product.model = self.model_edit.text().strip() or None
            product.color = self.color_edit.text().strip() or None
            product.size = self.size_edit.text().strip() or None
            if self.weight_spin.value() > 0:
                product.weight = Decimal(str(self.weight_spin.value()))
            
            product.tags = tags
            product.remarks = self.remarks_edit.toPlainText().strip() or None
            
            # 计算成本和利润
            product.calculate_total_cost()
            product.calculate_profit()
            
            # 验证商品数据
            product.validate()
            
            # 发送保存信号
            self.product_saved.emit(product)
            
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            self.logger.error(f"保存商品失败: {e}")
            QMessageBox.critical(self, "错误", f"保存商品失败:\n{e}")
