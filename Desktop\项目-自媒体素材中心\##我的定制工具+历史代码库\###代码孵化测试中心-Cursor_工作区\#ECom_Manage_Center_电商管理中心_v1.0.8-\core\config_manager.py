#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器

负责系统配置的读取、保存和管理。
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional
from utils.logger import LoggerMixin


class ConfigManager(LoggerMixin):
    """配置管理器"""
    
    def __init__(self, db_manager=None):
        """
        初始化配置管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.config_cache = {}
        self.default_config = self._get_default_config()
        
        # 加载配置
        self._load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 应用程序设置
            "app_name": "电商管理系统整合版",
            "app_version": "2.0.0",
            "language": "简体中文",
            "autosave_interval": 5,
            "check_update": True,
            
            # 数据设置
            "currency": "CNY (人民币)",
            "decimal_places": 2,
            "log_retention_days": 90,
            
            # 性能设置
            "page_size": 50,
            "cache_size": 100,
            "enable_multithread": True,
            
            # 界面设置
            "theme": "暗黑主题",
            "font_size": 12,
            "ui_scale": 100,
            "remember_window_pos": True,
            "minimize_to_tray": False,
            "start_maximized": False,
            
            # 数据库设置
            "database_path": "data/inventory.db",
            "db_pool_size": 5,
            "query_timeout": 30,
            "auto_backup": True,
            "backup_interval": 24,
            "backup_keep_count": 7,
            
            # API设置
            "api_1688_key": "",
            "api_1688_secret": "",
            "api_taobao_key": "",
            "api_taobao_secret": "",
            "api_timeout": 30,
            "api_retry_count": 3,
            "api_rate_limit": 100,
            
            # 监控设置
            "enable_monitor": True,
            "monitor_interval": 10,
            "memory_threshold": 80,
            "log_level": "INFO",
            "log_file_size": 10,
            "log_file_count": 5,
        }
    
    def _load_config(self):
        """加载配置"""
        try:
            if self.db_manager:
                # 从数据库加载配置
                self._load_from_database()
            else:
                # 从文件加载配置
                self._load_from_file()
        except Exception as e:
            self.logger.warning(f"加载配置失败，使用默认配置: {e}")
            self.config_cache = self.default_config.copy()
    
    def _load_from_database(self):
        """从数据库加载配置"""
        try:
            # 查询所有设置
            sql = "SELECT setting_key, setting_value, setting_type FROM system_settings"
            settings = self.db_manager.fetch_all(sql)
            
            for setting in settings:
                key = setting[0]
                value = setting[1]
                setting_type = setting[2]
                
                # 根据类型转换值
                if setting_type == "boolean":
                    value = value.lower() in ("true", "1", "yes")
                elif setting_type == "integer":
                    value = int(value)
                elif setting_type == "float":
                    value = float(value)
                # string类型保持原样
                
                self.config_cache[key] = value
            
            # 合并默认配置（确保所有配置项都存在）
            for key, default_value in self.default_config.items():
                if key not in self.config_cache:
                    self.config_cache[key] = default_value
                    
        except Exception as e:
            self.logger.error(f"从数据库加载配置失败: {e}")
            self.config_cache = self.default_config.copy()
    
    def _load_from_file(self):
        """从文件加载配置"""
        config_file = Path("config/app_config.json")
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                
                # 合并配置
                self.config_cache = self.default_config.copy()
                self.config_cache.update(file_config)
                
            except Exception as e:
                self.logger.error(f"从文件加载配置失败: {e}")
                self.config_cache = self.default_config.copy()
        else:
            self.config_cache = self.default_config.copy()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config_cache.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config_cache[key] = value
        
        # 保存到数据库或文件
        self._save_setting(key, value)
    
    def _save_setting(self, key: str, value: Any):
        """保存单个设置"""
        try:
            if self.db_manager:
                self._save_to_database(key, value)
            else:
                self._save_to_file()
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
    
    def _save_to_database(self, key: str, value: Any):
        """保存设置到数据库"""
        try:
            # 确定设置类型
            if isinstance(value, bool):
                setting_type = "boolean"
                setting_value = "true" if value else "false"
            elif isinstance(value, int):
                setting_type = "integer"
                setting_value = str(value)
            elif isinstance(value, float):
                setting_type = "float"
                setting_value = str(value)
            else:
                setting_type = "string"
                setting_value = str(value)
            
            # 插入或更新设置
            sql = """
            INSERT OR REPLACE INTO system_settings
            (setting_key, setting_value, setting_type, description, category, updated_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            description = f"用户设置: {key}"
            category = "user"
            
            self.db_manager.execute(sql, (key, setting_value, setting_type, description, category))
            
        except Exception as e:
            self.logger.error(f"保存设置到数据库失败: {e}")
    
    def _save_to_file(self):
        """保存配置到文件"""
        try:
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            
            config_file = config_dir / "app_config.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_cache, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存配置到文件失败: {e}")
    
    def save_all(self):
        """保存所有配置"""
        try:
            if self.db_manager:
                # 保存到数据库
                for key, value in self.config_cache.items():
                    self._save_to_database(key, value)
            else:
                # 保存到文件
                self._save_to_file()
                
            self.logger.info("所有配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存所有配置失败: {e}")
            raise
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config_cache = self.default_config.copy()
        self.save_all()
        self.logger.info("配置已重置为默认值")
    
    def export_to_file(self, file_path: str):
        """
        导出配置到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_cache, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置导出成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            raise
    
    def import_from_file(self, file_path: str):
        """
        从文件导入配置
        
        Args:
            file_path: 文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证并合并配置
            for key, value in imported_config.items():
                if key in self.default_config:
                    self.config_cache[key] = value
            
            self.save_all()
            self.logger.info(f"配置导入成功: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            raise
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_cache.copy()
    
    def get_category_config(self, category: str) -> Dict[str, Any]:
        """
        获取特定分类的配置
        
        Args:
            category: 配置分类
            
        Returns:
            该分类的配置字典
        """
        # 这里可以根据需要实现分类逻辑
        # 目前返回所有配置
        return self.get_all()
